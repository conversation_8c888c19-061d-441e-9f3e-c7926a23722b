import os
import ray
import asyncio
from pymilvus import DataType,MilvusClient
from typing import Optional,Literal
import yaml
import json
import time
import logging
from model_process import ModelProcessor
import argparse
import datetime  # 添加datetime模块
from openai import AsyncOpenAI
from oss_manager import OSSDataManager  # 添加OSS管理器
from kafka_actor import KafkaActor  # 添加Kafka生产者
from utils import convert_timestamp_to_unix
from json_repair import repair_json

@ray.remote(max_task_retries=3)
class VideoProcessor:
    def __init__(self):
        
        self.logger = logging.getLogger(__name__)
        self.load_config()
        self.milvus_client = MilvusClient(uri=self.uri,token=self.token)
        self.model_processor = ModelProcessor()
        
        # 在Ray Actor中延迟初始化数据库连接，避免阻塞启动
        self._db_initialized = False
        self.logger.info("VideoProcessor initialized, database will be initialized on first use")
            
        #设置当前时区为上海
        os.environ['TZ'] = 'Asia/Shanghai'
        time.tzset()

        # 加载文本嵌入服务配置
        text_embedding_config = self.config.get("text_embedding_service", {})
        self.text_embedding_api_key = text_embedding_config.get("api_key")
        self.text_embedding_base_url = text_embedding_config.get("base_url")
        self.text_embedding_model = text_embedding_config.get("embedding_model")
        # 更新向量维度为文本嵌入模型的维度
        self.vector_dim = text_embedding_config.get("embedding_dim", 1536)
        self.text_embedding_client = AsyncOpenAI(api_key=self.text_embedding_api_key, base_url=self.text_embedding_base_url)
        
        # 初始化OSS数据管理器
        self.oss_manager = None
        try:
            self.logger.info("Initializing OSS Manager...")
            self.oss_manager = OSSDataManager()
            if self.oss_manager.enabled:
                self.logger.info("OSS Manager initialized successfully - OSS storage enabled")
            else:
                self.logger.info("OSS Manager initialized - OSS storage disabled by configuration")
        except Exception as e:
            self.logger.warning(f"OSS Manager initialization failed: {e}. OSS storage will be disabled.")
            self.oss_manager = None
        
        # 初始化Kafka生产者用于发送回调消息
        self.kafka_producer = None
        self.kafka_reconnect_attempts = 0
        self.next_kafka_reconnect_time = 0
        self.max_kafka_reconnect_wait = 60  # 最大退避等待时间（秒）
        try:
            self.logger.info("Initializing Kafka Producer...")
            producer = KafkaActor.remote(self.config, is_producer_only=True)
            # 同步启动Kafka生产者，确保服务启动时连接正常
            ray.get(producer.start.remote())
            self.kafka_producer = producer
            self.logger.info("Kafka Producer initialized successfully for callbacks")
        except Exception as e:
            self.logger.warning(f"Initial Kafka Producer initialization failed: {e}. Will attempt to reconnect on first use with backoff.")
            self.kafka_producer = None # 确保在失败时为None
            self._start_kafka_backoff() # 启动退避
        
        self.logger.info(f"Using database: {self.db_name}, video collection: {self.video_collection_name}, frame collection: {self.frame_collection_name}, vector dimension: {self.vector_dim}")

    def _start_kafka_backoff(self, is_send_failure: bool = False):
        """启动或增加Kafka连接的指数退避计时器"""
        if is_send_failure:
            # 如果是发送失败，我们从第一次尝试开始计算退避
            self.kafka_reconnect_attempts = 1
        else:
            # 如果是连接失败，我们累加尝试次数
            self.kafka_reconnect_attempts += 1
        
        # 计算退避时间：2, 4, 8, 16, 32, 60, 60...
        backoff_time = min(self.max_kafka_reconnect_wait, 2 ** self.kafka_reconnect_attempts)
        self.next_kafka_reconnect_time = time.time() + backoff_time
        self.logger.warning(f"Kafka connection/send failed. Backing off for {backoff_time} seconds (attempt {self.kafka_reconnect_attempts}).")

    async def _send_callback_to_kafka(self, data: dict) -> bool:
        """
        将回调消息发送到Kafka。如果生产者不可用，会尝试使用指数退避策略重新连接。
        
        Args:
            data: 视频分析结果数据
            
        Returns:
            发送是否成功
        """
        if not self.kafka_producer:
            current_time = time.time()
            if current_time < self.next_kafka_reconnect_time:
                self.logger.warning(f"Kafka reconnect is in backoff period. Will try again in {self.next_kafka_reconnect_time - current_time:.1f} seconds.")
                return False

            self.logger.info(f"Kafka producer not available, attempting to reconnect (attempt {self.kafka_reconnect_attempts + 1})...")
            try:
                producer = KafkaActor.remote(self.config, is_producer_only=True)
                await producer.start.remote()
                self.kafka_producer = producer
                self.logger.info("Kafka Producer re-initialized successfully.")
                self.kafka_reconnect_attempts = 0
                self.next_kafka_reconnect_time = 0
            except Exception as e:
                self.logger.error(f"Failed to re-initialize Kafka Producer: {e}.")
                self.kafka_producer = None
                self._start_kafka_backoff()
                return False
            
        try:
            # 获取Kafka配置
            kafka_config = self.config.get("kafka", {})
            output_topic = kafka_config.get("output_topic", "video_unstanding_result_topic")
            
            # 准备回调消息
            callback_message = {
                # "messageType": "video_analysis_result",
                # "timestamp": datetime.datetime.now().isoformat(),
                "eventId": data.get("event_id"),
                "esn": data.get("esn"),
                "videoUrl": data.get("video_url"),
                "title": data.get("title", ""),
                "timestamp": convert_timestamp_to_unix(data.get("timestamp", 0)),
                "numPersons": data.get("num_persons", 0),
                "persons": json.loads(data.get("persons", "[]")) if isinstance(data.get("persons"), str) else data.get("persons", []),
                "couriers": json.loads(data.get("couriers", "[]")) if isinstance(data.get("couriers"), str) else data.get("couriers", []),
                "foodDeliverers": json.loads(data.get("food_deliverers", "[]")) if isinstance(data.get("food_deliverers"), str) else data.get("food_deliverers", []),
                "packages": json.loads(data.get("packages", "[]")) if isinstance(data.get("packages"), str) else data.get("packages", []),
                "pets": json.loads(data.get("pets", "[]")) if isinstance(data.get("pets"), str) else data.get("pets", []),
                "summary": data.get("summary", ""),
                "securityRisk": data.get("security_risk", ""),
                "recommendation": data.get("recommendation", "")
                
                # "processingInfo": {
                #     "ossPath": data.get("oss_path", ""),
                #     "processingTime": datetime.datetime.now().isoformat()
                # }
            }
            
            # 发送消息到Kafka
            success = await self.kafka_producer.send_message.remote(output_topic, callback_message)
            
            if success:
                self.logger.info(f"Callback message sent to Kafka topic {output_topic} for event_id: {data.get('event_id')}")
                # 成功发送后，重置退避状态
                self.kafka_reconnect_attempts = 0
                self.next_kafka_reconnect_time = 0
                return True
            else:
                self.logger.error(f"Failed to send callback message to Kafka for event_id: {data.get('event_id')}. Invalidating producer and starting backoff.")
                self.kafka_producer = None # 发送失败，假设生产者已失效
                self._start_kafka_backoff(is_send_failure=True)
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending callback message to Kafka: {str(e)}. Invalidating producer and starting backoff.")
            self.kafka_producer = None # Actor可能已死亡
            self._start_kafka_backoff(is_send_failure=True)
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    async def _ensure_db_initialized(self):
        """确保数据库已初始化"""
        if not self._db_initialized:
            try:
                await self.check_database(self.db_name)
                await self.check_video_collection(self.video_collection_name)
                # 暂时禁用帧集合初始化
                # await self.check_frame_collection(self.frame_collection_name)
                self.logger.info("帧集合初始化已暂时禁用")
                self._db_initialized = True
                self.logger.info("Database initialization completed")
            except Exception as e:
                self.logger.error(f"Database initialization failed: {e}")
                raise

    def load_config(self,path=None):
        if not path:
            path = f"config_{os.environ['DEPLOY_MODE']}.yaml"
        self.logger.info(f"VideoProcessor Loading config from: {path}")
        with open(path, "r") as f:
            self.config = yaml.safe_load(f)
        self.db_name = self.config["Milvus"]["db_name"]
        self.video_collection_name = self.config["Milvus"]["video_collection_name"]
        self.frame_collection_name = self.config["Milvus"]["frame_collection_name"]
        # 保留frame embedding的向量维度（Chinese-CLIP使用512维）
        self.frame_vector_dim = self.config["Milvus"]["vector_dim"]
        self.uri = self.config["Milvus"]["uri"]
        self.token = self.config["Milvus"]["token"]
        self.db_name = self.config["Milvus"]["db_name"]
        self.logger.info(f"Using database: {self.db_name}, video collection: {self.video_collection_name}, frame collection: {self.frame_collection_name}, frame vector dimension: {self.frame_vector_dim}")
    async def check_database(self, db_name: str):
        databases = self.milvus_client.list_databases()
        if db_name in databases:
            self.logger.info(f"Database {db_name} already exists")
        else:
            self.milvus_client.create_database(db_name=db_name)
            self.logger.info(f"Database {db_name} created!")
        self.milvus_client.use_database(db_name=db_name)
        self.logger.info(f"Database {db_name} connected!")
    async def check_video_collection(self, collection_name: str):
        collections = self.milvus_client.list_collections()
        if collection_name in collections:
            self.logger.info(f"Collection {collection_name} already exists")
            # 检查是否需要重置集合（如果与当前架构不兼容）
            try:
                # 尝试获取集合信息
                collection_info = self.milvus_client.describe_collection(collection_name)
                self.logger.info(f"Collection info: {collection_info}")
                # 这里可以添加检查逻辑，判断是否需要重建集合
            except Exception as e:
                self.logger.error(f"Error checking collection: {str(e)}")
        else:
            created_successfully = await self.create_video_collection(collection_name)
            if created_successfully:
                self.logger.info(f"Collection {collection_name} created!")
            else:
                # create_video_collection 内部已经记录了具体的创建失败错误
                self.logger.error(f"Post-check: Failed to ensure collection {collection_name} was created and ready.")
    async def check_frame_collection(self, collection_name: str):
        collections = self.milvus_client.list_collections()
        if collection_name in collections:
            self.logger.info(f"Collection {collection_name} already exists")
            # 检查是否需要重置集合（如果与当前架构不兼容）
            try:
                # 尝试获取集合信息
                collection_info = self.milvus_client.describe_collection(collection_name)
                self.logger.info(f"Collection info: {collection_info}")
                # 这里可以添加检查逻辑，判断是否需要重建集合
            except Exception as e:
                self.logger.error(f"Error checking collection: {str(e)}")
        else:
            created_successfully = await self.create_frame_collection(collection_name)
            if created_successfully:
                self.logger.info(f"Collection {collection_name} created!")
            else:
                # create_frame_collection 内部已经记录了具体的创建失败错误
                self.logger.error(f"Post-check: Failed to ensure collection {collection_name} was created and ready.")
    async def create_video_collection(self, collection_name: str):
        schema=self.milvus_client.create_schema(dynamic_schema=True)
        schema.add_field("video_id", DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field("event_id", DataType.VARCHAR, max_length=65535)
        schema.add_field("video_url", DataType.VARCHAR, max_length=255)
        schema.add_field("esn",DataType.VARCHAR, max_length=255)
        schema.add_field("title", DataType.VARCHAR, max_length=255)
        schema.add_field("title_embedding",datatype=DataType.FLOAT_VECTOR,dim=self.vector_dim)
        schema.add_field("timestamp", DataType.VARCHAR, max_length=255)
        schema.add_field("num_persons", DataType.INT64)
        schema.add_field("persons", DataType.VARCHAR, max_length=65535, nullable=True)
        schema.add_field("couriers", DataType.VARCHAR, max_length=65535, nullable=True)
        schema.add_field("food_deliverers", DataType.VARCHAR, max_length=65535, nullable=True)
        schema.add_field("packages", DataType.VARCHAR, max_length=65535, nullable=True)
        schema.add_field("pets", DataType.VARCHAR, max_length=65535, nullable=True)
        schema.add_field("summary", DataType.VARCHAR, max_length=65535)
        schema.add_field("summary_embedding",datatype=DataType.FLOAT_VECTOR,dim=self.vector_dim)
        schema.add_field("security_risk", DataType.VARCHAR, max_length=65535)
        schema.add_field("recommendation", DataType.VARCHAR, max_length=65535)
        schema.add_field("reasoning_content", DataType.VARCHAR, max_length=65535,nullable=True) # 思考过程(对于支持思考的模型)
        
        
        #创建collection
        sucess=False # Initialize success to False
        try:
            self.milvus_client.create_collection(collection_name=collection_name,schema=schema,properties={"collection.ttl.seconds": 2592000},)# 默认记录的失效时间为30天
            sucess = True # If no exception, assume success
        except Exception as e:
            self.logger.error(f"Exception during create_collection for {collection_name}: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            # sucess remains False

        if sucess:
            self.logger.info(f"Collection {collection_name} created successfully with dynamic fields enabled")
            
            try:
                # 创建索引参数对象
                index_params = self.milvus_client.prepare_index_params()
                
                # 为title_embedding添加索引
                index_params.add_index(
                    field_name="title_embedding",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )
                
                # 为summary_embedding添加索引
                index_params.add_index(
                    field_name="summary_embedding",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )
                
                # 创建索引
                self.milvus_client.create_index(
                    collection_name=collection_name,
                    index_params=index_params
                )
                self.logger.info(f"Created indexes for title_embedding and summary_embedding in collection {collection_name}")
                
                # 创建索引后自动加载集合
                try:
                    self.milvus_client.load_collection(collection_name=collection_name)
                    
                    # 等待加载完成并验证
                    import asyncio
                    max_wait = 30  # 最多等待30秒
                    wait_time = 0
                    while wait_time < max_wait:
                        await asyncio.sleep(1)
                        wait_time += 1
                        load_state = self.milvus_client.get_load_state(collection_name)
                        state_str = str(load_state.get('state', ''))
                        if 'Loaded' in state_str:
                            self.logger.info(f"Collection {collection_name} loaded successfully after index creation")
                            break
                    else:
                        self.logger.error(f"Collection {collection_name} failed to load within {max_wait} seconds")
                        
                except Exception as load_e:
                    self.logger.error(f"Failed to load collection after index creation: {str(load_e)}")
                
                self.logger.info(f"All indexes created and collection loaded successfully for {collection_name}")
            except Exception as e:
                self.logger.error(f"Failed to create indexes: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
        else:
            self.logger.error(f"Failed to create collection {collection_name}")
        return sucess
    
    async def create_frame_collection(self, collection_name: str):
        schema=self.milvus_client.create_schema(dynamic_schema=True)
        schema.add_field("frame_id", DataType.INT64, is_primary=True, auto_id=True)
        schema.add_field("event_id", DataType.VARCHAR, max_length=65535)  # 添加事件ID字段用于追踪
        schema.add_field("esn", DataType.VARCHAR, max_length=255)
        schema.add_field("source_url", DataType.VARCHAR, max_length=255)
        schema.add_field("time", DataType.VARCHAR, max_length=255) # 日期时间
        schema.add_field("timestamp", DataType.FLOAT) # 视频时间戳
        schema.add_field("embedding",datatype=DataType.FLOAT_VECTOR,dim=self.frame_vector_dim)
        
        
        #创建索引参数
        sucess=False # Initialize success to False
        try:
            self.milvus_client.create_collection(collection_name=collection_name,schema=schema,properties={"collection.ttl.seconds": 2592000},)# 默认记录的失效时间为30天
            sucess = True # If no exception, assume success
        except Exception as e:
            self.logger.error(f"Exception during create_collection for {collection_name}: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            # sucess remains False
            
        if sucess:
            self.logger.info(f"Collection {collection_name} created successfully with dynamic fields enabled")
            
            try:
                # 创建索引参数对象
                index_params = self.milvus_client.prepare_index_params()
                
                # 为embedding添加索引
                index_params.add_index(
                    field_name="embedding",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )
                
                # 创建索引
                self.milvus_client.create_index(
                    collection_name=collection_name,
                    index_params=index_params
                )
                # 创建索引后自动加载集合
                try:
                    self.milvus_client.load_collection(collection_name=collection_name)
                    
                    # 等待加载完成并验证
                    import asyncio
                    max_wait = 30  # 最多等待30秒
                    wait_time = 0
                    while wait_time < max_wait:
                        await asyncio.sleep(1)
                        wait_time += 1
                        load_state = self.milvus_client.get_load_state(collection_name)
                        state_str = str(load_state.get('state', ''))
                        if 'Loaded' in state_str:
                            self.logger.info(f"Collection {collection_name} loaded successfully after index creation")
                            break
                    else:
                        self.logger.error(f"Collection {collection_name} failed to load within {max_wait} seconds")
                        
                except Exception as load_e:
                    self.logger.error(f"Failed to load collection after index creation: {str(load_e)}")
                
                self.logger.info(f"Index created successfully for collection {collection_name}, field: embedding")
            except Exception as e:
                self.logger.error(f"Failed to create index: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
        else:
            self.logger.error(f"Failed to create collection {collection_name}")
        return sucess
    
    async def _get_text_embeddings_async(self, text: str) -> Optional[list[float]]:
        """通过文本嵌入服务获取文本的嵌入向量"""
        if not text:
            return None
        try:
            response = await self.text_embedding_client.embeddings.create(
                input=[text],
                model=self.text_embedding_model,
                dimensions=self.vector_dim  # 明确指定向量维度
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while getting text embeddings: {e}")
            return None

    async def process_oai_completion(self,url:str,mode:Literal["local","cloud"]="local"):
        #print("\n" + "=" * 20 + "思考过程" + "=" * 20 + "\n")
        # 获取流对象(现在直接是AsyncStream，不再是异步生成器)
        if mode == "local":
            stream = await self.model_processor.analyze_video_with_local(url)
        elif mode == "cloud":
            stream = await self.model_processor.analyze_video_with_cloud(url)
        else:
            raise ValueError(f"Invalid mode: {mode}")
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""     # 定义完整回复
        is_answering = False    # 判断是否结束思考过程并开始回复

        try:
            # 直接异步迭代AsyncStream对象
            async for chunk in stream:
                # 处理每个响应块
                if hasattr(chunk, 'choices') and chunk.choices:
                    delta = chunk.choices[0].delta
                    # 打印思考过程
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        #print(delta.reasoning_content, end='', flush=True)
                        reasoning_content += delta.reasoning_content
                    elif hasattr(delta, 'content') and delta.content is not None:
                        # 开始回复
                        if not is_answering:
                            #print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                            is_answering = True
                        # 打印回复过程
                        #print(delta.content, end='', flush=True)
                        answer_content += delta.content
            return reasoning_content,answer_content
        except Exception as e:
            self.logger.error(f"\n[ERROR] 处理流时出错: {e}")
            self.logger.error(f"Error type: {type(e)}")
            import traceback
            traceback.print_exc()

    async def process_video(self, esn: str,event_id:str,video_url: str,mode:Literal["local","cloud"]="local") -> str:
        #获取视频的理解
        reasoning_content,answer_content=await self.process_oai_completion(video_url,mode)
        self.logger.info(f"思考过程: {reasoning_content}")
        self.logger.info(f"完整回复: {answer_content}")
        
        #准备视频集合的embedding
        try:
            #处理可能的json格式问题 - 移除可能的markdown标记
            clean_content = answer_content
            # 移除可能的markdown代码块标记
            if clean_content.startswith("```"):
                # 寻找第一个换行符，之后的内容才是JSON
                first_newline = clean_content.find('\n')
                if first_newline != -1:
                    clean_content = clean_content[first_newline+1:]
                else: # 如果没有换行符，可能格式是 ```json{...}```
                    if clean_content.startswith("```json"):
                        clean_content = clean_content[7:] # 移除 ```json
            
            if clean_content.endswith("```"):
                clean_content = clean_content[:-3].strip()
            
            # 使用json_repair修复JSON
            repaired_json_str = repair_json(clean_content)
                
            #首先获取模型回复的json数据
            self.logger.info(f"修复后的JSON内容: {repaired_json_str}")
            json_data=json.loads(repaired_json_str)
            self.logger.info(f"视频理解json: {json_data}")
            
            title_text=json_data.get("title","")
            summary_text=json_data.get("summary","")
            title_embedding = await self._get_text_embeddings_async(title_text)
            summary_embedding = await self._get_text_embeddings_async(summary_text)
        
            # 安全地处理和验证时间戳
            raw_timestamp_str = json_data.get("timestamp", "")
            validated_timestamp_str = ""
            try:
                # 尝试解析时间戳，验证其格式
                datetime.datetime.fromisoformat(raw_timestamp_str.replace(" ", "T"))
                validated_timestamp_str = raw_timestamp_str
            except (ValueError, TypeError):
                self.logger.warning(f"Invalid or empty timestamp from model: '{raw_timestamp_str}'. Using current time as fallback.")
                validated_timestamp_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
            # 将复杂JSON数组序列化为字符串
            persons_str = json.dumps(json_data.get("persons", []), ensure_ascii=False)
            couriers_str = json.dumps(json_data.get("couriers", []), ensure_ascii=False)
            food_deliverers_str = json.dumps(json_data.get("food_deliverers", []), ensure_ascii=False)
            packages_str = json.dumps(json_data.get("packages", []), ensure_ascii=False)
            pets_str = json.dumps(json_data.get("pets", []), ensure_ascii=False)
            
            #往视频集合中插入数据
            data={
                "esn":esn,
                "event_id":event_id,
                "video_url":video_url,
                "title":json_data.get("title",""),
                "title_embedding":title_embedding,
                "timestamp":validated_timestamp_str,
                "num_persons":json_data.get("num_persons",0),
                "persons":persons_str,
                "couriers":couriers_str,
                "food_deliverers":food_deliverers_str,
                "packages":packages_str,
                "pets":pets_str,
                "summary":json_data.get("summary",""),
                "summary_embedding":summary_embedding,
                "reasoning_content":reasoning_content,
                "security_risk":json_data.get("security_risk",""),
                "recommendation":json_data.get("recommendation",""),
            }
            #往milvus视频集合中插入数据
            result = self.milvus_client.insert(self.video_collection_name,data)
            if result:
                self.logger.info(f"视频数据插入成功，esn:{esn},video_url:{video_url}")
                self.logger.info(f"返回数据: {result}")
                
                # 保存到OSS（如果OSS管理器可用且已启用）
                if self.oss_manager and self.oss_manager.enabled:
                    try:
                        # 准备要保存的分析结果（移除embedding避免文件过大）
                        analysis_result_for_oss = json_data.copy()
                        
                        # 准备处理信息
                        processing_info = {
                            "processing_time": datetime.datetime.now().isoformat(),
                            "mode": mode,
                            "milvus_insert_result": str(result),
                            "vector_dimensions": {
                                "title_embedding": len(title_embedding) if title_embedding else 0,
                                "summary_embedding": len(summary_embedding) if summary_embedding else 0
                            }
                        }
                        
                        # 准备文件信息
                        file_info = {
                            "video_url": video_url,
                            "esn": esn,
                            "analysis_version": "v1.4.0"
                        }
                        
                        # 保存到OSS
                        oss_path = self.oss_manager.save_video_analysis(
                            event_id=event_id,
                            esn=esn,
                            video_url=video_url,
                            analysis_result=analysis_result_for_oss,
                            reasoning_content=reasoning_content,
                            processing_info=processing_info,
                            file_info=file_info
                        )
                        self.logger.info(f"视频分析结果已保存到OSS: {oss_path}")
                        
                        # 在返回数据中添加OSS路径信息
                        data["oss_path"] = oss_path
                        
                    except Exception as oss_e:
                        self.logger.error(f"保存到OSS失败: {str(oss_e)}")
                        # OSS保存失败不影响主流程，继续返回数据
                
                # 发送回调消息到Kafka通知业务方
                try:
                    callback_success = await self._send_callback_to_kafka(data)
                    if callback_success:
                        self.logger.info(f"Callback message sent to Kafka successfully for event_id: {event_id}")
                    else:
                        self.logger.warning(f"Failed to send callback message to Kafka for event_id: {event_id}")
                except Exception as callback_e:
                    self.logger.error(f"Kafka callback error for event_id {event_id}: {str(callback_e)}")
                    # 回调失败不影响主流程
                
                return data
            else:
                self.logger.error(f"视频数据插入失败，esn:{esn},video_url:{video_url}")
                self.logger.info(f"返回数据: {result}")
                return None
        except json.JSONDecodeError as e:
            self.logger.error(f"\n[ERROR] 处理json数据时出错: {e}")
            self.logger.error(f"尝试解析的内容: '{answer_content}'")
            
            # 尝试从错误的JSON中提取基本信息，避免完全失败
            try:
                fallback_data = await self._extract_fallback_data(answer_content, esn, event_id, video_url, reasoning_content)
                if fallback_data:
                    self.logger.info("使用降级数据处理模式")
                    return fallback_data
            except Exception as fallback_e:
                self.logger.error(f"降级数据处理也失败: {fallback_e}")
            
            return None
        except Exception as e:
            self.logger.error(f"\n[ERROR] 处理json数据时出现除解析外的错误: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    async def _extract_fallback_data(self, content: str, esn: str, event_id: str, video_url: str, reasoning_content: str) -> dict:
        """从错误的JSON中提取基本信息作为降级处理"""
        try:
            # 使用正则表达式提取基本信息
            import re
            
            title_match = re.search(r'"title"[:\s]*"([^"]*)"', content)
            summary_match = re.search(r'"summary"[:\s]*"([^"]*)"', content)
            security_risk_match = re.search(r'"security_risk"[:\s]*"([^"]*)"', content)
            recommendation_match = re.search(r'"recommendation"[:\s]*"([^"]*)"', content)
            
            title = title_match.group(1) if title_match else "视频分析"
            summary = summary_match.group(1) if summary_match else "分析处理中出现格式错误"
            security_risk = security_risk_match.group(1) if security_risk_match else "未知"
            recommendation = recommendation_match.group(1) if recommendation_match else "请检查视频内容"
            
            # 生成基础的embedding
            title_embedding = [0.0] * 1536  # 使用零向量作为默认值
            summary_embedding = [0.0] * 1536
            
            # 创建降级数据
            fallback_data = {
                "esn": esn,
                "event_id": event_id,
                "video_url": video_url,
                "title": title,
                "title_embedding": title_embedding,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "num_persons": 0,
                "persons": "[]",
                "couriers": "[]",
                "food_deliverers": "[]",
                "packages": "[]",
                "pets": "[]",
                "summary": summary,
                "summary_embedding": summary_embedding,
                "reasoning_content": reasoning_content,
                "security_risk": security_risk,
                "recommendation": recommendation,
            }
            
            # 尝试插入到数据库
            result = self.milvus_client.insert(self.video_collection_name, fallback_data)
            if result:
                self.logger.info(f"降级数据插入成功，esn:{esn},video_url:{video_url}")
                
                # 发送回调消息到Kafka（降级模式）
                try:
                    callback_success = await self._send_callback_to_kafka(fallback_data)
                    if callback_success:
                        self.logger.info(f"Fallback callback message sent to Kafka for event_id: {event_id}")
                except Exception as callback_e:
                    self.logger.error(f"Fallback Kafka callback error for event_id {event_id}: {str(callback_e)}")
                
                return fallback_data
            else:
                self.logger.error(f"降级数据插入失败，esn:{esn},video_url:{video_url}")
                return None
                
        except Exception as e:
            self.logger.error(f"降级数据提取失败: {e}")
            return None

    async def process_frame(self,esn:str,event_id:str,url:str):
        #对视频进行抽帧
        frames,timestamps,frame_paths=await self.model_processor.extract_key_frames(url)
        #计算每个帧的embedding
        embeddings=await self.model_processor.compute_chinese_clip_embeddings_with_frames(frames)
        #往milvus帧集合中插入数据,使用更高性能的批次插入
        
        # 获取当前时间作为帧处理时间（上海时区）
        current_datetime = datetime.datetime.now()
        # 确保使用上海时区（Asia/Shanghai）
        formatted_datetime_str = current_datetime.strftime("%Y-%m-%d %H:%M:%S")
        # 注意：时区已在__init__方法中通过os.environ['TZ'] = 'Asia/Shanghai'和time.tzset()设置
        
        processed_data = []
        for embedding_vector, ts_value in zip(embeddings, timestamps):
            self.logger.info(f"Processing frame for ESN {esn}, event_id {event_id}, URL {url}. Raw timestamp from model: {ts_value}")

            processed_data.append({
                "event_id": event_id,            # 添加事件ID用于追踪
                "esn": esn,
                "source_url": url,
                "time": formatted_datetime_str,  # 存储当前实际日期时间
                "timestamp": ts_value,           # 存储视频内的时间点
                "embedding": embedding_vector
            })
        
        result=self.milvus_client.insert(collection_name=self.frame_collection_name,data=processed_data)
        if result:
            self.logger.info(f"帧数据插入成功，esn:{esn},url:{url}")
            self.logger.info(f"返回数据: {result}")
            for frame_path in frame_paths:
                if os.path.exists(frame_path):
                    os.remove(frame_path)
                    self.logger.info(f"帧临时文件已删除，frame_path:{frame_path}")
            return processed_data
        else:
            self.logger.error(f"帧数据插入失败，esn:{esn},url:{url}")
            self.logger.info(f"返回数据: {result}")
            return None
    
    async def process(self,esn:str,event_id:str,url:str,mode:Literal["local","cloud"]="local"):
        # 确保数据库已初始化
        await self._ensure_db_initialized()
        
        # 处理视频，使用指数退避重试
        max_retries = 3
        video_result = None
        for attempt in range(max_retries):
            try:
                if mode == "local":
                    video_result = await self.process_video(esn, event_id, url, mode)
                elif mode == "cloud":
                    video_result = await self.process_video(esn, event_id, url, mode)
                else:
                    raise ValueError(f"Invalid mode: {mode}")
                if video_result:
                    break
            except Exception as e:
                self.logger.error(f"处理视频失败 (尝试 {attempt+1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    # 指数退避等待: 2^attempt 秒 (1, 2, 4秒...)
                    wait_time = 2 ** attempt
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
        
        # 暂时禁用帧处理功能
        self.logger.info("帧处理功能已暂时禁用")
        frame_result=None
        """
        目前该功能产品不使用，暂时禁用
        """
        # # 处理帧，使用指数退避重试
        # max_retries = 3
        # frame_result = None
        # for attempt in range(max_retries):
        #     try:
        #         frame_result = await self.process_frame(esn, event_id, url)
        #         if frame_result:
        #             break
        #     except Exception as e:
        #         self.logger.error(f"处理帧失败 (尝试 {attempt+1}/{max_retries}): {str(e)}")
        #         if attempt < max_retries - 1:
        #             # 指数退避等待: 2^attempt 秒 (1, 2, 4秒...)
        #             wait_time = 2 ** attempt
        #             self.logger.info(f"等待 {wait_time} 秒后重试...")
        #             await asyncio.sleep(wait_time)
        
        # 返回统一的结果格式
        return {
            "video_result": video_result,
            "frame_result": frame_result,
            "status": "completed" if video_result else "failed",
            "event_id": event_id,
            "esn": esn,
            "url": url,
            "process_time": 0,  # 可以后续添加计时逻辑
            "file_size": 0,     # 可以后续添加文件大小统计
            "frames_processed": 0,  # 暂时禁用帧处理
            "total_frames": 0,      # 暂时禁用帧处理
            "video_info": {},
            "processing_stats": {}
        }

    async def reset_collection(self, collection_name: str, is_video_collection=True):
        """删除并重新创建集合（用于架构更改时）"""
        collections = self.milvus_client.list_collections()
        if collection_name in collections:
            try:
                # 首先删除现有集合
                self.milvus_client.drop_collection(collection_name)
                self.logger.info(f"已删除现有集合 {collection_name}")
            except Exception as e:
                self.logger.error(f"删除集合时出错: {str(e)}")
        
        # 重新创建集合
        if is_video_collection:
            await self.create_video_collection(collection_name)
        else:
            await self.create_frame_collection(collection_name)
        self.logger.info(f"已重新创建集合 {collection_name}")

    async def list_collections(self) -> list[dict]:
        """List all collections."""
        try:
            return self.milvus_client.list_collections()
        except Exception as e:
            raise ValueError(f"Failed to list collections: {str(e)}")
        
    async def get_collection_info(self, collection_name: str) -> dict:
        """Get detailed information about a collection."""
        try:
            return self.milvus_client.describe_collection(collection_name)
        except Exception as e:
            raise ValueError(f"Failed to get collection info: {str(e)}")
        
    async def query_collection(
        self,
        collection_name: str,
        filter_expr: str,
        output_fields: Optional[list[str]] = None,
        limit: int = 10,
    ) -> list[dict]:
        """
        Query collection using filter expressions.

        Args:
            collection_name: Name of collection to search
            filter_expr: Filter expression to select entities to query
            output_fields: Fields to return in results
            limit: Maximum number of results
        """
        try:
            return self.milvus_client.query(
                collection_name=collection_name,
                filter=filter_expr,
                output_fields=output_fields,
                limit=limit,
            )
        except Exception as e:
            raise ValueError(f"Query failed: {str(e)}")
        
    async def search_video_title_collection(
        self,
        esn:str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """
        基于标题的语义搜索视频集合。
        
        将查询文本转换为向量嵌入，并在视频集合中搜索与标题语义相似的视频。
        
        Args:
            query_text: 要搜索的文本查询
            limit: 返回结果的最大数量
            drop_ratio: 忽略的低频项比例 (0.0-1.0)
            
        Returns:
            匹配视频的列表，按相似度排序
        """
        try:
            search_params = {"params": {"drop_ratio_search": drop_ratio}}
            filter_expr = f"esn == '{esn}'"
            #对query_text进行文本embedding计算（使用文本嵌入服务）
            query_embedding = await self._get_text_embeddings_async(query_text)
            if not query_embedding:
                self.logger.error("Failed to get text embedding for title search")
                return []
            results = self.milvus_client.search(
                collection_name=self.video_collection_name,
                data=[query_embedding],
                anns_field="title_embedding",
                filter=filter_expr,
                limit=limit,
                output_fields=["esn", "video_url"],
                search_params=search_params,
            )
            self.logger.debug(f"搜索结果：{results}")
            return results
        except Exception as e:
            raise ValueError(f"Search failed: {str(e)}")
        
    async def search_video_summary_collection(
        self,
        esn:str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """
        基于摘要的语义搜索视频集合。
        
        将查询文本转换为向量嵌入，并在视频集合中搜索与摘要语义相似的视频。
        这种搜索对于查找内容相关的视频特别有用。
        
        Args:
            query_text: 要搜索的文本查询
            limit: 返回结果的最大数量
            drop_ratio: 忽略的低频项比例 (0.0-1.0)
            
        Returns:
            匹配视频的列表，按相似度排序
        """
        try:
            search_params = {"params": {"drop_ratio_search": drop_ratio}}
            filter_expr = f"esn == '{esn}'"
            #对query_text进行文本embedding计算（使用文本嵌入服务）
            query_embedding = await self._get_text_embeddings_async(query_text)
            if not query_embedding:
                self.logger.error("Failed to get text embedding for summary search")
                return []
            results = self.milvus_client.search(
                collection_name=self.video_collection_name,
                data=[query_embedding],
                anns_field="summary_embedding",
                filter=filter_expr,
                limit=limit,
                output_fields=["esn", "video_url"],
                search_params=search_params,
            )
            self.logger.debug(f"搜索结果：{results}")
            return results
        except Exception as e:
            raise ValueError(f"Search failed: {str(e)}")
        
    async def search_frame_collection(
        self,
        esn:str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """
        基于语义搜索视频帧集合。
        
        将查询文本转换为向量嵌入，并在帧集合中搜索与查询语义相似的关键帧。
        这对于定位视频中特定内容出现的时间点特别有用。
        
        Args:
            query_text: 要搜索的文本查询，描述要查找的场景或对象
            limit: 返回结果的最大数量
            drop_ratio: 忽略的低频项比例 (0.0-1.0)
            
        Returns:
            匹配帧的列表，包含ESN、视频URL和时间戳信息，按相似度排序
        """
        try:
            search_params = {"params": {"drop_ratio_search": drop_ratio}}
            filter_expr = f"esn == '{esn}'"
            #对query_text进行Chinese-CLIP embedding计算（用于帧搜索）
            query_embedding = await self.model_processor.compute_chinese_clip_embeddings_with_text(query_text)
            if not query_embedding:
                self.logger.error("Failed to get Chinese-CLIP embedding for frame search")
                return []
            results = self.milvus_client.search(
                collection_name=self.frame_collection_name,
                data=[query_embedding],
                anns_field="embedding",
                filter=filter_expr,
                limit=limit,
                output_fields=["esn", "source_url", "timestamp"],
                search_params=search_params,
            )
            self.logger.debug(f"搜索结果：{results}")
            return results
        except Exception as e:
            raise ValueError(f"Search failed: {str(e)}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='视频处理系统')
    parser.add_argument('--reset', action='store_true', help='重置集合并重新创建')
    parser.add_argument('--video', type=str, help='要处理的视频URL', 
                       default="")
    parser.add_argument('--esn', type=str, help='设备ESN', default="1234567890")
    parser.add_argument('--event_id', type=str, help='事件ID', default="asfasgasdasdasds")
    parser.add_argument('--query_video_method', type=str, help='查询方法', default="title",choices=["title","summary"])
    parser.add_argument('--query_frame_method', type=str, help='查询方法', default="frame",choices=["frame"])
    parser.add_argument('--query_text', type=str, help='查询文本', default="")
    
    args = parser.parse_args()
    
    # 初始化处理器
    video_processor = VideoProcessor()
    
    # 如果指定了重置参数，则重置集合
    if args.reset:
        asyncio.run(video_processor.reset_collection(video_processor.video_collection_name, is_video_collection=True))
        asyncio.run(video_processor.reset_collection(video_processor.frame_collection_name, is_video_collection=False))
    
    if args.video:
        # 处理视频
        asyncio.run(video_processor.process(args.esn, args.event_id, args.video))

    # 搜索并且打印结果
    # results = asyncio.run(video_processor.search_frame_collection(
    #     query_text="有人", 
    #     limit=10,
    #     drop_ratio=0.2
    # ))
    if args.query_video_method == "title":
        results = asyncio.run(video_processor.search_video_title_collection(
            query_text=args.query_text,
            esn=args.esn,
            limit=10,
            drop_ratio=0.2
        ))
    elif args.query_video_method == "summary":
        results = asyncio.run(video_processor.search_video_summary_collection(
        query_text=args.query_text,
        esn=args.esn,
        limit=10,
        drop_ratio=0.2
    ))
    elif args.query_frame_method == "frame":
        results = asyncio.run(video_processor.search_frame_collection(
            query_text=args.query_text,
            esn=args.esn,
            limit=10,
            drop_ratio=0.2
        ))
    
    print("搜索结果:")
    # 处理结果是一个包含列表的列表，第一个元素是搜索结果
    if results and isinstance(results, list) and len(results) > 0:
        search_hits = results[0]
        # 假设search_hits是一个字符串形式的列表，需要解析
        if isinstance(search_hits, str) and search_hits.startswith("[") and search_hits.endswith("]"):
            import json
            try:
                # 尝试解析JSON字符串
                parsed_results = json.loads(search_hits)
                for i, result in enumerate(parsed_results):
                    print(f"结果 {i+1}:")
                    for key, value in result.items():
                        print(f"  {key}: {value}")
                    print("-" * 30)
            except json.JSONDecodeError:
                print(f"无法解析搜索结果: {search_hits}")
        else:
            # 直接处理列表对象
            for i, result in enumerate(search_hits):
                print(f"结果 {i+1}:")
                if hasattr(result, 'items'):
                    for key, value in result.items():
                        print(f"  {key}: {value}")
                else:
                    print(f"  {result}")
                print("-" * 30)
    else:
        print("未找到结果")
