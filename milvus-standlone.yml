---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
    kompose.version: 1.36.0 (ae2a39403)
  labels:
    io.kompose.service: minio
  name: minio
spec:
  ports:
    - name: "9001"
      port: 9001
      targetPort: 9001
    - name: "9000"
      port: 9000
      targetPort: 9000
  selector:
    io.kompose.service: minio

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
    kompose.version: 1.36.0 (ae2a39403)
  labels:
    io.kompose.service: standalone
  name: standalone
spec:
  ports:
    - name: "19530"
      port: 19530
      targetPort: 19530
    - name: "9091"
      port: 9091
      targetPort: 9091
  selector:
    io.kompose.service: standalone

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
    kompose.version: 1.36.0 (ae2a39403)
  labels:
    io.kompose.service: etcd
  name: etcd
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: etcd
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
        kompose.version: 1.36.0 (ae2a39403)
      labels:
        io.kompose.service: etcd
    spec:
      containers:
        - args:
            - etcd
            - -advertise-client-urls=http://etcd:2379
            - -listen-client-urls
            - http://0.0.0.0:2379
            - --data-dir
            - /etcd
          env:
            - name: ETCD_AUTO_COMPACTION_MODE
              value: revision
            - name: ETCD_AUTO_COMPACTION_RETENTION
              value: "1000"
            - name: ETCD_QUOTA_BACKEND_BYTES
              value: "**********"
            - name: ETCD_SNAPSHOT_COUNT
              value: "50000"
          image: quay.io/coreos/etcd:v3.5.18
          livenessProbe:
            exec:
              command:
                - etcdctl
                - endpoint
                - health
            failureThreshold: 3
            periodSeconds: 30
            timeoutSeconds: 20
          name: milvus-etcd
          volumeMounts:
            - mountPath: /etcd
              name: etcd-claim0
      restartPolicy: Always
      volumes:
        - name: etcd-claim0
          persistentVolumeClaim:
            claimName: etcd-claim0

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: etcd-claim0
  name: etcd-claim0
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
    kompose.version: 1.36.0 (ae2a39403)
  labels:
    io.kompose.service: minio
  name: minio
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: minio
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
        kompose.version: 1.36.0 (ae2a39403)
      labels:
        io.kompose.service: minio
    spec:
      containers:
        - args:
            - minio
            - server
            - /minio_data
            - --console-address
            - :9001
          env:
            - name: MINIO_ACCESS_KEY
              value: minioadmin
            - name: MINIO_SECRET_KEY
              value: minioadmin
          image: minio/minio:RELEASE.2023-03-20T20-16-18Z
          livenessProbe:
            exec:
              command:
                - curl
                - -f
                - http://localhost:9000/minio/health/live
            failureThreshold: 3
            periodSeconds: 30
            timeoutSeconds: 20
          name: milvus-minio
          ports:
            - containerPort: 9001
              protocol: TCP
            - containerPort: 9000
              protocol: TCP
          volumeMounts:
            - mountPath: /minio_data
              name: minio-claim0
      restartPolicy: Always
      volumes:
        - name: minio-claim0
          persistentVolumeClaim:
            claimName: minio-claim0

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: minio-claim0
  name: minio-claim0
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
    kompose.version: 1.36.0 (ae2a39403)
  labels:
    io.kompose.service: standalone
  name: standalone
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: standalone
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose convert -f milvus-standalone-docker-compose.yml -o milvus-standlone.yml
        kompose.version: 1.36.0 (ae2a39403)
      labels:
        io.kompose.service: standalone
    spec:
      containers:
        - args:
            - milvus
            - run
            - standalone
          env:
            - name: ETCD_ENDPOINTS
              value: etcd:2379
            - name: MINIO_ADDRESS
              value: minio:9000
            - name: MQ_TYPE
              value: woodpecker
          image: milvusdb/milvus:v2.6.0-rc1
          livenessProbe:
            exec:
              command:
                - curl
                - -f
                - http://localhost:9091/healthz
            failureThreshold: 3
            initialDelaySeconds: 90
            periodSeconds: 30
            timeoutSeconds: 20
          name: milvus-standalone
          ports:
            - containerPort: 19530
              protocol: TCP
            - containerPort: 9091
              protocol: TCP
          volumeMounts:
            - mountPath: /var/lib/milvus
              name: standalone-claim0
      restartPolicy: Always
      volumes:
        - name: standalone-claim0
          persistentVolumeClaim:
            claimName: standalone-claim0

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: standalone-claim0
  name: standalone-claim0
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

