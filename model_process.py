import base64
import tempfile
import uuid
import time
import os
import contextlib
import asyncio
import aiohttp
import ffmpeg
import numpy as np
import yaml
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any
from PIL import Image
from io import BytesIO
from openai import AsyncOpenAI
from dotenv import load_dotenv
import datetime
import json
import logging

# 新增导入
try:
    from ultralytics import YOLOWorld
    import cv2
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    YOLOWorld = None
    cv2 = None

# 设置日志
logger = logging.getLogger(__name__)

class ModelProcessor:
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化模型处理器
        
        Args:
            config_path: 配置文件路径，默认为 "config.yaml"
        """
        # 设置日志记录器
        self.logger = logger
        # 加载配置文件
        self.load_config(config_path)
        
        # 初始化服务客户端配置
        self.chinese_clip_url = self.config.get("chinese_clip_service", {}).get("url", "http://localhost:8000")
        self.max_retries = self.config.get("chinese_clip_service", {}).get("max_retries", 3)
        self.retry_delay = self.config.get("chinese_clip_service", {}).get("retry_delay", 1.0)
        self.timeout = self.config.get("chinese_clip_service", {}).get("timeout", 30)
        
        # 视频处理配置
        video_config = self.config.get("video_processing", {})
        self.batch_size = video_config.get("batch_size", 16)
        self.default_fps = video_config.get("fps", 1.0)
        self.max_frames = video_config.get("max_frames", 100)
        self.image_quality = video_config.get("image_quality", 85)

        # 网格布局配置
        self.grid_frame_width = video_config.get("grid_frame_width", 320)
        self.grid_frame_height = video_config.get("grid_frame_height", 180)
        self.grid_cols = video_config.get("grid_cols", 4)
        self.grid_quality = video_config.get("grid_quality", 80)
        
        # 创建关键帧目录
        self.keyframes_dir = Path(tempfile.gettempdir()) / "video_frames_grid"
        self.keyframes_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化OpenAI客户端
        try:
            self.client = AsyncOpenAI(
                api_key=self.config["VLM"]["api_key"],
                base_url=self.config["VLM"]["base_url"],
            )
            self.model = self.config["VLM"]["model_name"]
        except Exception as e:
            self.logger.error(f"OpenAI客户端初始化失败: {e}")
            raise

        # 初始化智能筛选组件
        self.process_mode = video_config.get("process_mode", "grid")
        if self.process_mode == "smart_filter":
            self._init_smart_filter()
        else:
            self.yolo_model = None
            self.smart_config = None

    def _init_smart_filter(self):
        """初始化智能筛选组件"""
        try:
            if not YOLO_AVAILABLE:
                raise ImportError("ultralytics 或 opencv-python 未安装，无法使用智能筛选模式")
            
            smart_config = self.config["video_processing"]["smart_filter"]
            
            # 检查智能筛选是否启用
            if not smart_config.get("enabled", True):
                self.logger.warning("智能筛选已禁用，将回退到网格模式")
                self.process_mode = "grid"
                self.yolo_model = None
                self.smart_config = None
                return
            
            self.logger.info("正在初始化YOLO-World模型...")
            
            # 初始化YOLO-World模型
            model_config = smart_config.get("model", {})
            model_name = model_config.get("name", "yolov8s-world.pt")
            device = model_config.get("device", "auto")
            imgsz = model_config.get("imgsz", 640)
            
            self.yolo_model = YOLOWorld(model_name) # type: ignore
            
            # 设置设备
            if device != "auto":
                self.yolo_model.to(device)
            
            # 设置检测类别
            all_targets = (smart_config["detection_targets"]["primary"] + 
                          smart_config["detection_targets"]["secondary"])
            self.yolo_model.set_classes(all_targets)
            
            # 保存配置
            self.smart_config = smart_config
            self.primary_targets = set(smart_config["detection_targets"]["primary"])
            self.secondary_targets = set(smart_config["detection_targets"]["secondary"])
            
            self.logger.info(f"YOLO-World模型初始化成功")
            self.logger.info(f"检测类别: {all_targets}")
            self.logger.info(f"优先类别: {list(self.primary_targets)}")
            
        except Exception as e:
            self.logger.error(f"智能筛选初始化失败: {e}")
            self.logger.warning("回退到网格模式")
            self.process_mode = "grid"
            self.yolo_model = None
            self.smart_config = None

    def load_config(self, config_path: Optional[str] = None) -> None:
        """加载配置文件"""
        load_dotenv(".env",override=True)
        config_path = config_path or f"config_{os.getenv('DEPLOY_MODE', 'dev')}.yaml"
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                self.config = yaml.safe_load(f)
            self.logger.info(f"配置文件加载成功: {config_path}")
        except FileNotFoundError:
            self.logger.error(f"配置文件未找到: {config_path}")
            raise
        except yaml.YAMLError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            raise

    async def encode_base64_file(self, file_path: str) -> str:
        """将视频文件或者图片编码为base64格式"""
        try:
            with open(file_path, "rb") as video_file:
                return base64.b64encode(video_file.read()).decode("utf-8")
        except Exception as e:
            self.logger.error(f"文件base64编码失败 {file_path}: {e}")
            raise

    @contextlib.asynccontextmanager
    async def _cleanup_file(self, file_path: str):
        """文件清理上下文管理器"""
        try:
            yield file_path
        finally:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    self.logger.info(f"临时文件已清理: {file_path}")
                except Exception as e:
                    self.logger.warning(f"清理文件失败 {file_path}: {e}")

    async def download_m3u8_to_mp4(self, m3u8_url: str) -> Optional[str]:
        """
        下载m3u8格式的视频并转换为本地mp4文件
        
        Args:
            m3u8_url: m3u8格式的视频URL
            
        Returns:
            本地mp4文件的路径，失败时返回None
        """
        try:
            # 使用时间戳创建更唯一的临时文件名
            unique_id = f"{uuid.uuid4()}_{int(time.time())}"
            
            # 创建临时目录用于保存视频文件
            temp_dir = Path(tempfile.gettempdir()) / "video_processing"
            temp_dir.mkdir(exist_ok=True)
            
            # 定义输入和输出文件路径
            input_path = str(temp_dir / f"input_{unique_id}.mp4")
            
            self.logger.info(f"开始下载视频: {m3u8_url}")
            self.logger.info(f"保存路径: {input_path}")
            
            # 使用ffmpeg下载并转换视频
            (
                ffmpeg
                .input(m3u8_url)
                .output(
                    input_path, 
                    format='mp4',
                    vcodec='copy',  # 直接复制视频流
                    an=None,  # 不包含音频
                    r=30  # 强制设置为30fps，防止异常的高帧率
                )
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )
            
            # 验证下载的文件
            if not os.path.exists(input_path) or os.path.getsize(input_path) == 0:
                self.logger.error("下载的视频文件无效")
                return None
                
            self.logger.info(f"视频下载完成: {input_path}")
            return input_path
            
        except ffmpeg.Error as e:
            self.logger.error(f"FFmpeg错误: {e.stderr.decode() if e.stderr else str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"下载视频时出错: {str(e)}", exc_info=True)
            return None

    async def extract_key_frames(self, video_path: str, fps: float = None) -> Tuple[List[Image.Image], List[float], List[str]]:
        """
        从视频中提取关键帧
        
        Args:
            video_path: 视频文件路径
            fps: 每秒提取的帧数，如果为None则使用配置文件中的默认值
            
        Returns:
            (frames列表, timestamps列表, frame_paths列表)
        """
        if fps is None:
            fps = self.default_fps
            
        frames = []
        timestamps = []
        frame_paths = []
        
        try:
            # 创建保存帧的目录
            frames_dir = Path(tempfile.gettempdir()) / "video_frames" / str(uuid.uuid4())
            frames_dir.mkdir(parents=True, exist_ok=True)
            
            # 获取视频信息
            video_info = await self._get_video_info(video_path)
            
            # 计算要提取的时间点
            target_timestamps = self._calculate_timestamps(video_info['duration'], fps)
            
            self.logger.info(f"将提取 {len(target_timestamps)} 个时间点的帧")
            
            # 提取每一帧
            for i, timestamp in enumerate(target_timestamps):
                frame_filename = str(frames_dir / f"frame_{i:06d}.jpg")
                
                if await self._extract_single_frame(video_path, timestamp, frame_filename):
                    try:
                        pil_image = Image.open(frame_filename)
                        frames.append(pil_image)
                        timestamps.append(timestamp)
                        frame_paths.append(frame_filename)
                    except Exception as e:
                        self.logger.warning(f"加载帧图像失败 {frame_filename}: {e}")
            
            # 如果没有成功提取任何帧，尝试提取第一帧
            if not frames:
                self.logger.warning("尝试提取第一帧作为备用")
                if await self._extract_fallback_frame(video_path, frames_dir):
                    frames, timestamps, frame_paths = await self._extract_fallback_frame(video_path, frames_dir)
            
            if not frames:
                raise Exception("无法提取任何有效帧")
            
            self.logger.info(f"成功提取 {len(frames)} 帧")
            return frames, timestamps, frame_paths
            
        except Exception as e:
            self.logger.error(f"提取帧时出错: {str(e)}", exc_info=True)
            return [], [], []

    async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频信息"""
        try:
            probe = ffmpeg.probe(video_path)
            video_info = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)
        except Exception as e:
            self.logger.warning(f"获取视频信息失败: {str(e)}，使用默认值")
            return {'fps': 30.0, 'duration': 10.0, 'total_frames': 300}
        
        # 解析视频信息
        fps = 30.0
        duration = 10.0
        
        if video_info:
            # 获取帧率
            try:
                fps = float(eval(video_info.get('r_frame_rate', '30/1')))
            except:
                self.logger.warning(f"无法解析帧率，使用默认值: {fps}")
            
            # 获取持续时间
            duration_str = video_info.get('duration') or probe.get('format', {}).get('duration')
            if duration_str:
                try:
                    duration = float(duration_str)
                except:
                    self.logger.warning(f"无法解析视频时长，使用默认值: {duration}")
        
        total_frames = int(duration * fps)
        self.logger.info(f"视频信息 - 帧率: {fps}, 时长: {duration}s, 估计总帧数: {total_frames}")
        
        return {'fps': fps, 'duration': duration, 'total_frames': total_frames}

    def _calculate_timestamps(self, duration: float, fps: float) -> List[float]:
        """计算要提取的时间戳"""
        if duration <= 0:
            return [0.0, 1.0, 2.0, 5.0, 8.0]
        
        num_frames = min(int(duration * fps), self.max_frames)
        
        if num_frames <= 1:
            return [0.0]
        
        return [i * duration / num_frames for i in range(num_frames)]

    async def _extract_single_frame(self, video_path: str, timestamp: float, output_path: str) -> bool:
        """提取单个帧"""
        try:
            (
                ffmpeg
                .input(video_path, ss=timestamp)
                .output(
                    output_path, 
                    vframes=1, 
                    format='image2', 
                    vcodec='mjpeg', 
                    qscale=0
                )
                .global_args('-loglevel', 'error')
                .run(capture_stdout=True, capture_stderr=True, overwrite_output=True)
            )
            
            return os.path.exists(output_path) and os.path.getsize(output_path) > 0
            
        except Exception as e:
            self.logger.warning(f"在时间点 {timestamp}s 提取帧失败: {e}")
            return False

    async def _extract_fallback_frame(self, video_path: str, frames_dir: Path) -> Optional[Tuple[List[Image.Image], List[float], List[str]]]:
        """备用帧提取方法"""
        try:
            frame_path = str(frames_dir / "fallback_frame.jpg")
            ffmpeg.input(video_path).output(frame_path, vframes=1).run(quiet=True, overwrite_output=True)
            
            if os.path.exists(frame_path) and os.path.getsize(frame_path) > 0:
                pil_image = Image.open(frame_path)
                return [pil_image], [0.0], [frame_path]
                
        except Exception as e:
            self.logger.error(f"备用帧提取也失败: {e}")
        
        return None
    async def _create_keyframes_grid(self, frame_paths: List[str]) -> Optional[str]:
        """
        将关键帧按网格形式组合成一张图片
        
        Args:
            frame_paths: 关键帧文件路径列表
            
        Returns:
            组合图片的路径，失败时返回None
        """
        try:
            if not frame_paths:
                return None
                
            # 加载所有图片
            images = []
            for path in frame_paths:
                if os.path.exists(path):
                    img = Image.open(path)
                    images.append(img)
                else:
                    self.logger.warning(f"关键帧文件不存在: {path}")
            
            if not images:
                return None
            
            # 确保所有图片尺寸一致，使用配置的参数
            target_width, target_height = self.grid_frame_width, self.grid_frame_height
            resized_images = []
            for img in images:
                resized_img = img.resize((target_width, target_height), Image.Resampling.LANCZOS)
                resized_images.append(resized_img)
            
            # 计算网格尺寸，使用配置的列数
            num_images = len(resized_images)
            grid_cols = min(self.grid_cols, num_images)  # 不超过图片数量
            grid_rows = (num_images + grid_cols - 1) // grid_cols  # 向上取整
            
            # 创建空白画布
            canvas_width = grid_cols * target_width
            canvas_height = grid_rows * target_height
            canvas = Image.new('RGB', (canvas_width, canvas_height), (0, 0, 0))
            
            # 在画布上拼接图片
            for idx, img in enumerate(resized_images):
                row = idx // grid_cols
                col = idx % grid_cols
                x = col * target_width
                y = row * target_height
                canvas.paste(img, (x, y))
            
            # 添加时间标注（可选）
            # 这里可以在每个格子上添加时间戳标注
            
            # 保存组合图片，使用配置的质量参数
            grid_filename = f"keyframes_grid_{uuid.uuid4().hex[:8]}.jpg"
            grid_path = os.path.join(self.keyframes_dir, grid_filename)
            canvas.save(grid_path, 'JPEG', quality=self.grid_quality, optimize=True)
            
            # 计算最终文件大小
            file_size = os.path.getsize(grid_path) / (1024 * 1024)
            
            self.logger.info(f"✅ 关键帧网格已创建: {grid_path}")
            self.logger.info(f"📐 网格尺寸: {grid_cols}x{grid_rows} ({num_images}个帧)")
            self.logger.info(f"🖼️  每帧分辨率: {target_width}x{target_height}")
            self.logger.info(f"📦 整体分辨率: {canvas_width}x{canvas_height}")
            self.logger.info(f"💾 文件大小: {file_size:.2f}MB")
            
            return grid_path
            
        except Exception as e:
            self.logger.error(f"创建关键帧网格失败: {str(e)}")
            return None
        
    async def _make_request_with_retry(self, url: str, json_data: dict) -> dict:
        """
        带重试机制的异步HTTP请求
        
        Args:
            url: 请求URL
            json_data: 请求数据
            
        Returns:
            响应数据
        """
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                timeout = aiohttp.ClientTimeout(total=self.timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(
                        url,
                        json=json_data,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            error_text = await response.text()
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"HTTP {response.status}: {error_text}"
                            )
                            
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    wait_time = self.retry_delay * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                    self.logger.info(f"等待 {wait_time:.1f} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error(f"所有重试都失败了: {e}")
                    
        # 如果所有重试都失败，抛出最后一个异常
        raise last_exception

    async def compute_chinese_clip_embeddings_with_frames(self, frames: List[Image.Image]) -> List[List[float]]:
        """
        使用Chinese-CLIP服务计算视频帧的嵌入向量
        
        Args:
            frames: 视频帧列表 (PIL.Image对象)
            
        Returns:
            帧嵌入向量列表（Python列表格式）
        """
        frame_embeddings = []
        
        self.logger.info(f"使用Chinese-CLIP服务计算 {len(frames)} 帧的嵌入向量")
        
        try:
            # 分批处理帧
            for i in range(0, len(frames), self.batch_size):
                batch_frames = frames[i:i+self.batch_size]
                self.logger.info(f"处理批次 {i//self.batch_size + 1}/{(len(frames) + self.batch_size - 1)//self.batch_size}")
                
                # 将PIL图像转换为base64
                image_base64_list = []
                for frame in batch_frames:
                    buffer = BytesIO()
                    frame.save(buffer, format="JPEG", quality=self.image_quality)
                    image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                    image_base64_list.append(f"data:image/jpeg;base64,{image_base64}")
                
                # 发送请求到Chinese-CLIP服务
                try:
                    result = await self._make_request_with_retry(
                        f"{self.chinese_clip_url}/embeddings",
                        {"images": image_base64_list}
                    )
                    
                    batch_embeddings = result.get('image_embeddings', [])
                    if len(batch_embeddings) == len(batch_frames):
                        frame_embeddings.extend(batch_embeddings)
                    else:
                        self.logger.error(f"嵌入向量数量不匹配: 期望 {len(batch_frames)}, 得到 {len(batch_embeddings)}")
                        # 创建零向量作为替代
                        frame_embeddings.extend([[0.0] * 512 for _ in range(len(batch_frames))])
                        
                except Exception as e:
                    self.logger.error(f"批次 {i//self.batch_size + 1} 处理失败: {e}")
                    # 创建零向量作为替代
                    frame_embeddings.extend([[0.0] * 512 for _ in range(len(batch_frames))])
            
            self.logger.info(f"成功计算 {len(frame_embeddings)} 个嵌入向量，维度: {len(frame_embeddings[0]) if frame_embeddings else 0}")
            return frame_embeddings
            
        except Exception as e:
            self.logger.error(f"计算嵌入向量失败: {e}")
            raise

    async def compute_chinese_clip_embeddings_with_text(self, text: str) -> List[float]:
        """
        使用Chinese-CLIP服务计算文本的嵌入向量
        
        Args:
            text: 需要计算嵌入的文本
        
        Returns:
            文本嵌入向量（Python列表格式）
        """
        try:
            self.logger.info(f"计算文本嵌入: {text[:50]}...")
            
            # 发送请求到Chinese-CLIP服务
            result = await self._make_request_with_retry(
                f"{self.chinese_clip_url}/embeddings",
                {"texts": [text]}
            )
            
            text_embeddings = result.get('text_embeddings', [[]])
            if text_embeddings and len(text_embeddings) > 0:
                return text_embeddings[0]
            else:
                self.logger.error("未获取到有效的文本嵌入向量")
                return [0.0] * 512
            
        except Exception as e:
            self.logger.error(f"计算文本嵌入向量失败: {e}")
            return [0.0] * 512

    async def compute_similarity_between_text_and_frames(self, text: str, frames: List[Image.Image]) -> List[float]:
        """
        计算文本与视频帧之间的相似度
        
        Args:
            text: 查询文本
            frames: 视频帧列表
            
        Returns:
            相似度分数列表
        """
        try:
            self.logger.info(f"计算文本与 {len(frames)} 帧的相似度")
            
            # 并行计算文本和图像嵌入
            text_task = self.compute_chinese_clip_embeddings_with_text(text)
            frames_task = self.compute_chinese_clip_embeddings_with_frames(frames)
            
            text_embedding, frame_embeddings = await asyncio.gather(text_task, frames_task)
            
            # 计算余弦相似度
            similarities = []
            text_vec = np.array(text_embedding)
            text_norm = np.linalg.norm(text_vec)
            
            for frame_embedding in frame_embeddings:
                frame_vec = np.array(frame_embedding)
                frame_norm = np.linalg.norm(frame_vec)
                
                if text_norm > 0 and frame_norm > 0:
                    similarity = np.dot(text_vec, frame_vec) / (text_norm * frame_norm)
                    similarities.append(float(similarity))
                else:
                    similarities.append(0.0)
            
            self.logger.info(f"相似度计算完成，平均相似度: {np.mean(similarities):.4f}")
            return similarities
            
        except Exception as e:
            self.logger.error(f"相似度计算失败: {e}")
            return [0.0] * len(frames)

    async def save_frame_data(self, video_id: str, frame_paths: List[str], timestamps: List[float], 
                            embeddings: List[List[float]], output_dir: str = "frame_embeddings") -> str:
        """
        保存帧数据，包括路径、时间戳和嵌入向量
        
        Args:
            video_id: 视频ID
            frame_paths: 帧图像文件路径列表
            timestamps: 时间戳列表
            embeddings: 嵌入向量列表
            output_dir: 输出目录
            
        Returns:
            保存的数据文件路径
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 创建数据字典
            frame_data = {
                "video_id": video_id,
                "created_at": datetime.datetime.now().isoformat(),
                "frames": []
            }
            
            # 添加每一帧的数据
            for i in range(len(frame_paths)):
                frame_data["frames"].append({
                    "frame_id": f"{video_id}_{i:06d}",
                    "frame_path": frame_paths[i],
                    "timestamp": float(timestamps[i]),
                    "embedding": embeddings[i]
                })
            
            # 保存为JSON文件
            output_path = os.path.join(output_dir, f"{video_id}_frame_data.json")
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(frame_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"帧数据已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"保存帧数据失败: {e}")
            raise

    def is_smart_filter_available(self) -> bool:
        """检查智能筛选是否可用"""
        return (self.process_mode == "smart_filter" and 
                self.yolo_model is not None and 
                self.smart_config is not None and
                YOLO_AVAILABLE)
    
    def get_detection_info(self) -> dict:
        """获取检测配置信息"""
        if not self.is_smart_filter_available():
            return {"available": False, "reason": "智能筛选不可用"}
        
        return {
            "available": True,
            "model": self.smart_config["model"]["name"],
            "device": self.smart_config["model"]["device"],
            "primary_targets": list(self.primary_targets),
            "secondary_targets": list(self.secondary_targets),
            "confidence_threshold": self.smart_config["filtering"]["confidence_threshold"]
        }

    def get_smart_filter_stats(self) -> dict:
        """
        获取智能筛选统计信息
        
        Returns:
            统计信息字典
        """
        if not self.is_smart_filter_available():
            return {"available": False, "reason": "智能筛选不可用"}
        
        stats = {
            "available": True,
            "mode": "smart_filter",
            "config": {
                "model": self.smart_config["model"]["name"],
                "device": self.smart_config["model"]["device"],
                "confidence_threshold": self.smart_config["filtering"]["confidence_threshold"],
                "time_window": self.smart_config["filtering"]["time_window"],
                "min_frames": self.smart_config["filtering"]["min_frames"],
                "max_frames": self.smart_config["filtering"]["max_frames"],
            },
            "targets": {
                "primary": list(self.primary_targets),
                "secondary": list(self.secondary_targets),
                "total": len(self.primary_targets) + len(self.secondary_targets)
            },
            "adaptive_mode": self.smart_config.get("adaptive_mode", {}),
            "weights": self.smart_config["priority_weights"]
        }
        
        return stats

    async def benchmark_smart_filter(self, test_frames: List[Image.Image], 
                                   test_timestamps: List[float]) -> dict:
        """
        对智能筛选进行基准测试
        
        Args:
            test_frames: 测试帧列表
            test_timestamps: 测试时间戳列表
            
        Returns:
            基准测试结果
        """
        if not self.is_smart_filter_available():
            return {"error": "智能筛选不可用"}
        
        import time
        
        start_time = time.time()
        
        try:
            # 执行智能筛选
            selected_indices, scores = await self._smart_filter_frames(test_frames, test_timestamps)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 计算统计信息
            total_frames = len(test_frames)
            selected_frames = len(selected_indices)
            reduction_ratio = (total_frames - selected_frames) / total_frames if total_frames > 0 else 0
            
            # 分析分数分布
            score_stats = {
                "min": min(scores) if scores else 0,
                "max": max(scores) if scores else 0,
                "mean": sum(scores) / len(scores) if scores else 0,
                "count": len(scores)
            }
            
            return {
                "success": True,
                "processing_time": processing_time,
                "fps": total_frames / processing_time if processing_time > 0 else 0,
                "frames": {
                    "input": total_frames,
                    "output": selected_frames,
                    "reduction_ratio": reduction_ratio,
                    "reduction_percentage": reduction_ratio * 100
                },
                "scores": score_stats,
                "selected_indices": selected_indices,
                "performance": {
                    "time_per_frame": processing_time / total_frames if total_frames > 0 else 0,
                    "efficiency": "good" if processing_time < total_frames * 0.1 else "needs_optimization"
                }
            }
            
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "error": str(e),
                "processing_time": end_time - start_time
            }

    async def analyze_video_with_cloud(self, video_path: str):
        """
        使用云端模型分析视频并生成分析结果
        
        Args:
            video_path: 视频文件路径或URL
        
        Returns:
            视频分析的流式结果
        """
        # 根据process_mode选择分析方式
        process_mode = self.config["video_processing"]["process_mode"]
        
        if process_mode == "smart_filter":
            return await self.analyze_video_with_smart_filter(video_path)
        
        # 原有的grid和video模式逻辑
        temp_video_path = None
        grid_path = None
        frame_paths = []
        
        try:
            # 下载视频
            temp_video_path = await self.download_m3u8_to_mp4(video_path)
            if not temp_video_path:
                raise Exception("视频下载失败")
            
            # 提取视频帧并计算嵌入（使用配置的fps）
            frames, timestamps, frame_paths = await self.extract_key_frames(temp_video_path)
            if not frames:
                raise Exception("帧提取失败")
                
            # embeddings = await self.compute_chinese_clip_embeddings_with_frames(frames)
            base64_video = None
            base64_grid = None
            stream = None
            
            if process_mode == "grid":
                grid_path = await self._create_keyframes_grid(frame_paths)
                if not grid_path:
                    raise Exception("网格创建失败")
                base64_grid = await self.encode_base64_file(grid_path)
                stream = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user","content": [
                        {"type": "image_url","image_url":{"url": f"data:image/jpeg;base64,{base64_grid}"} },
                        {"type": "text","text": self.config["prompt"]["video_analysis_grid"]},
                    ]}],
                    stream=True,
                )
            elif process_mode == "video":
                base64_video = await self.encode_base64_file(temp_video_path)
                stream = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user","content": [
                        {"type": "video_url","video_url":{"url": f"data:video/mp4;base64,{base64_video}"} },
                        {"type": "text","text": self.config["prompt"]["video_analysis"]},
                    ]}],
                    stream=True,
                )
            else:
                raise ValueError(f"Invalid process mode: {process_mode}")
            
            return stream
            
        except Exception as e:
            self.logger.error(f"云端视频分析失败: {e}")
            raise
        finally:
            # 清理临时文件
            cleanup_files = []
            
            # 添加临时视频文件
            if temp_video_path:
                cleanup_files.append(("临时视频文件", temp_video_path))
            
            # 添加网格文件
            if grid_path:
                cleanup_files.append(("关键帧网格文件", grid_path))
            
            # 添加关键帧文件
            for frame_path in frame_paths:
                cleanup_files.append(("关键帧文件", frame_path))
            
            # 执行清理
            for file_type, file_path in cleanup_files:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        self.logger.info(f"{file_type}已删除: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.logger.warning(f"删除{file_type}失败 {file_path}: {e}")

    async def analyze_video_with_local(self,video_path: str):
        """
        使用本地模型分析视频
        由于本地模型部署在例如(vllm,sglang)，不支持视频直接传入，所以需要先提取帧分析
        
        Args:
            video_path: 视频文件路径或URL
        
        Returns:
            视频分析的流式结果
        """
        # 根据process_mode选择分析方式
        process_mode = self.config["video_processing"]["process_mode"]
        
        if process_mode == "smart_filter":
            return await self.analyze_video_with_smart_filter(video_path)
        
        # 原有的grid和video模式逻辑
        temp_video_path = None
        grid_path = None
        frame_paths = []
        
        try:
            # 下载视频
            temp_video_path = await self.download_m3u8_to_mp4(video_path)
            if not temp_video_path:
                raise Exception("视频下载失败")

            # 提取视频帧并计算嵌入（使用配置的fps）
            frames, timestamps, frame_paths = await self.extract_key_frames(temp_video_path)
            if not frames:
                raise Exception("帧提取失败")
                
            # embeddings = await self.compute_chinese_clip_embeddings_with_frames(frames)
            base64_video = None
            base64_grid = None
            stream = None
            if process_mode == "grid":
                grid_path = await self._create_keyframes_grid(frame_paths)
                if not grid_path:
                    raise Exception("网格创建失败")
                base64_grid = await self.encode_base64_file(grid_path)
                stream = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user","content": [
                        {"type": "image_url","image_url":{"url": f"data:image/jpeg;base64,{base64_grid}"} },
                        {"type": "text","text": self.config["prompt"]["video_analysis_grid"]},
                    ]}],
                    stream=True,
                )
            elif process_mode == "video":
                #由于本地模型不支持视频直接传入，所以需要先提取帧分析
                #对这些帧进行编码
                base64_frames = []
                for frame in frames:
                    buffer = BytesIO()
                    frame.save(buffer, format="JPEG", quality=self.image_quality)
                    base64_frames.append(base64.b64encode(buffer.getvalue()).decode('utf-8'))
                
                #构造消息，传入所有帧
                messages = [{"role": "user","content": [
                    {"type": "image_url","image_url":{"url": f"data:image/jpeg;base64,{frame}"} } for frame in base64_frames
                ]}]
                messages.append({"type": "text","text": self.config["prompt"]["video_analysis"]})
                #发送请求
                stream = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    stream=True,
                )
            else:
                raise ValueError(f"Invalid process mode: {process_mode}")
            
            return stream
            
        except Exception as e:
            self.logger.error(f"本地视频分析失败: {e}")
            raise
        finally:
            # 清理临时文件
            cleanup_files = []
            
            # 添加临时视频文件
            if temp_video_path:
                cleanup_files.append(("临时视频文件", temp_video_path))
            
            # 添加网格文件
            if grid_path:
                cleanup_files.append(("关键帧网格文件", grid_path))
            
            # 添加关键帧文件
            for frame_path in frame_paths:
                cleanup_files.append(("关键帧文件", frame_path))
            
            # 执行清理
            for file_type, file_path in cleanup_files:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        self.logger.info(f"{file_type}已删除: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.logger.warning(f"删除{file_type}失败 {file_path}: {e}")

    async def _smart_filter_frames(self, frames: List[Image.Image], 
                                 timestamps: List[float]) -> Tuple[List[int], List[float]]:
        """
        使用YOLO-World智能筛选关键帧
        
        Args:
            frames: 所有提取的帧
            timestamps: 对应的时间戳
            
        Returns:
            (selected_indices, priority_scores) - 选中的帧索引和优先级分数
        """
        if not self.is_smart_filter_available():
            self.logger.warning("智能筛选不可用，使用均匀采样回退策略")
            return self._fallback_uniform_sampling(frames, timestamps)
        
        self.logger.info(f"开始智能筛选 {len(frames)} 帧...")
        frame_scores = []
        
        try:
            for i, frame in enumerate(frames):
                try:
                    # 转换PIL图像为OpenCV格式
                    cv_frame = cv2.cvtColor(np.array(frame), cv2.COLOR_RGB2BGR)
                    
                    # 获取配置的置信度阈值
                    conf_threshold = self.smart_config["filtering"]["confidence_threshold"]
                    self.logger.info(f"帧 {i}: 使用YOLO检测，置信度阈值: {conf_threshold}")

                    # YOLO检测
                    results = self.yolo_model(
                        cv_frame,
                        conf=conf_threshold,
                        imgsz=self.smart_config["model"]["imgsz"],
                        verbose=False  # 减少输出
                    )

                    # 记录检测结果
                    if results[0].boxes is not None and len(results[0].boxes) > 0:
                        boxes_count = len(results[0].boxes)
                        classes = []
                        confidences = []

                        for box_idx in range(boxes_count):
                            cls_id = int(results[0].boxes.cls[box_idx])
                            class_name = self.yolo_model.names[cls_id]
                            confidence = float(results[0].boxes.conf[box_idx])
                            classes.append(class_name)
                            confidences.append(f"{confidence:.3f}")

                        self.logger.info(f"帧 {i}: 检测到 {boxes_count} 个对象: {classes}, 置信度: {confidences}")
                    else:
                        self.logger.info(f"帧 {i}: 未检测到任何对象")

                    # 计算帧优先级分数
                    score = self._calculate_frame_priority(results[0], i, timestamps)
                    frame_scores.append((i, score, results[0]))

                    self.logger.info(f"帧 {i}: 时间戳 {timestamps[i]:.2f}s, 最终优先级分数 {score:.3f}")
                    
                except Exception as frame_error:
                    self.logger.warning(f"处理帧 {i} 时出错: {frame_error}, 使用默认分数")
                    frame_scores.append((i, 0.1, None))  # 默认低分数
        
        except Exception as e:
            self.logger.error(f"YOLO检测过程出错: {e}, 使用回退策略")
            return self._fallback_uniform_sampling(frames, timestamps)
        
        # 筛选关键帧
        try:
            selected_indices = self._select_key_frames(frame_scores, timestamps)
            selected_scores = [frame_scores[i][1] for i in selected_indices]
            
            # 验证结果合理性
            if not selected_indices:
                self.logger.warning("智能筛选未选中任何帧，使用回退策略")
                return self._fallback_uniform_sampling(frames, timestamps)
            
            self.logger.info(f"智能筛选完成: {len(frames)} -> {len(selected_indices)} 帧")
            self.logger.info(f"选中帧分数范围: {min(selected_scores):.3f} - {max(selected_scores):.3f}")
            
            return selected_indices, selected_scores
            
        except Exception as e:
            self.logger.error(f"帧选择过程出错: {e}, 使用回退策略")
            return self._fallback_uniform_sampling(frames, timestamps)

    def _fallback_uniform_sampling(self, frames: List[Image.Image], 
                                  timestamps: List[float]) -> Tuple[List[int], List[float]]:
        """
        回退策略：均匀采样
        
        Args:
            frames: 所有帧
            timestamps: 时间戳
            
        Returns:
            (selected_indices, uniform_scores)
        """
        total_frames = len(frames)
        if total_frames == 0:
            return [], []
        
        # 使用配置中的帧数限制，如果智能筛选配置不可用则使用默认值
        if self.smart_config:
            min_frames = self.smart_config["filtering"]["min_frames"]
            max_frames = self.smart_config["filtering"]["max_frames"]
        else:
            min_frames = min(8, total_frames)
            max_frames = min(25, total_frames)
        
        # 计算采样数量
        target_frames = min(max_frames, max(min_frames, total_frames // 4))
        
        if total_frames <= target_frames:
            # 如果总帧数不多，返回所有帧
            selected_indices = list(range(total_frames))
        else:
            # 均匀采样
            step = total_frames / target_frames
            selected_indices = [int(i * step) for i in range(target_frames)]
        
        # 所有帧给相同的分数
        uniform_scores = [0.5] * len(selected_indices)
        
        self.logger.info(f"回退策略采样: {total_frames} -> {len(selected_indices)} 帧")
        return selected_indices, uniform_scores

    def _calculate_frame_priority(self, detection_result, frame_idx: int, timestamps: List[float]) -> float:
        """
        计算帧的优先级分数
        
        Args:
            detection_result: YOLO检测结果
            frame_idx: 帧索引
            timestamps: 时间戳列表
            
        Returns:
            优先级分数 (0.0 - 无关键内容, 1.0+ - 高优先级)
        """
        score = 0.0
        weights = self.smart_config["priority_weights"]
        
        # 如果没有检测到任何对象，使用增强的基础分数策略
        if detection_result.boxes is None or len(detection_result.boxes) == 0:
            # 基础分数配置
            fallback_config = self.smart_config["filtering"].get("fallback_strategy", {})
            baseline_score = fallback_config.get("min_baseline_score", 0.3)
            
            # 可以基于帧位置给予不同权重
            # 开头和结尾的帧可能更重要
            total_frames = len(timestamps)
            if frame_idx == 0 or frame_idx == total_frames - 1:
                baseline_score *= 1.2  # 首尾帧加权
            elif frame_idx < total_frames * 0.3 or frame_idx > total_frames * 0.7:
                baseline_score *= 1.1  # 前30%和后30%的帧稍微加权
            
            return baseline_score
        
        # 获取检测到的类别名称
        detected_classes = []
        detection_confidences = []
        box_areas = []

        # 获取图像尺寸用于归一化
        img_height, img_width = detection_result.orig_shape if hasattr(detection_result, 'orig_shape') else (640, 640)
        total_img_area = img_height * img_width

        for i in range(len(detection_result.boxes)):
            cls_id = int(detection_result.boxes.cls[i])
            class_name = self.yolo_model.names[cls_id]
            confidence = float(detection_result.boxes.conf[i])

            # 计算检测框面积并归一化到[0,1]
            box = detection_result.boxes.xyxy[i]
            area_pixels = float((box[2] - box[0]) * (box[3] - box[1]))
            area_normalized = area_pixels / total_img_area  # 归一化到[0,1]

            detected_classes.append(class_name)
            detection_confidences.append(confidence)
            box_areas.append(area_normalized)

            # 添加详细的检测日志
            self.logger.debug(f"帧 {frame_idx} 检测到: {class_name} (置信度: {confidence:.3f}, 归一化面积: {area_normalized:.4f})")
        
        # 1. 人员检测权重 (最高优先级)
        person_detections = [i for i, cls in enumerate(detected_classes) if cls == "person"]
        if person_detections:
            # 考虑人员检测的置信度和面积，限制最大值避免异常高分数
            person_score = sum(min(detection_confidences[i] * (1 + box_areas[i] * 10), 2.0)
                             for i in person_detections)
            score += person_score * weights["has_person"]
            self.logger.debug(f"帧 {frame_idx} 人员检测: {len(person_detections)}个, 分数: {person_score:.3f}")
        
        # 2. 宠物检测权重
        pet_classes = ["dog", "cat", "bird"]
        pet_detections = [i for i, cls in enumerate(detected_classes) if cls in pet_classes]
        if pet_detections:
            pet_score = sum(min(detection_confidences[i] * (1 + box_areas[i] * 5), 1.5)
                           for i in pet_detections)
            score += pet_score * weights["has_pet"]
            self.logger.debug(f"帧 {frame_idx} 宠物检测: {len(pet_detections)}个, 分数: {pet_score:.3f}")

        # 3. 包裹/物品检测权重
        package_classes = ["package", "bag", "tool"]
        package_detections = [i for i, cls in enumerate(detected_classes) if cls in package_classes]
        if package_detections:
            package_score = sum(min(detection_confidences[i], 1.0) for i in package_detections)
            score += package_score * weights["has_package"]
            self.logger.debug(f"帧 {frame_idx} 包裹检测: {len(package_detections)}个, 分数: {package_score:.3f}")

        # 4. 车辆检测权重
        vehicle_classes = ["car", "motorcycle", "bicycle"]
        vehicle_detections = [i for i, cls in enumerate(detected_classes) if cls in vehicle_classes]
        if vehicle_detections:
            vehicle_score = sum(min(detection_confidences[i], 1.0) for i in vehicle_detections)
            score += vehicle_score * weights["has_vehicle"]
            self.logger.debug(f"帧 {frame_idx} 车辆检测: {len(vehicle_detections)}个, 分数: {vehicle_score:.3f}")

        # 5. 检测目标数量权重 (多目标场景更重要)
        detection_count_score = min(len(detected_classes) * weights["detection_count"], 2.0)
        score += detection_count_score

        # 6. 动作分数 (基于检测框总面积，面积大说明目标更明显)
        total_area = sum(box_areas)
        motion_score = min(total_area * weights["motion_score"], 1.0)  # 限制最大值
        score += motion_score

        # 记录详细信息用于调试
        self.logger.info(f"帧 {frame_idx} 检测详情: 类别={detected_classes}, 置信度={[f'{c:.3f}' for c in detection_confidences]}, 最终分数={score:.3f}")

        return score

    def _select_key_frames(self, frame_scores: List[Tuple[int, float, Any]], 
                          timestamps: List[float]) -> List[int]:
        """
        基于分数和时间窗口选择关键帧
        
        Args:
            frame_scores: [(frame_idx, score, detection_result), ...]
            timestamps: 时间戳列表
            
        Returns:
            选中的帧索引列表 (按时间顺序)
        """
        if not frame_scores:
            return []
        
        # 按分数排序 (分数高的优先)
        sorted_frames = sorted(frame_scores, key=lambda x: x[1], reverse=True)
        
        selected = []
        time_window = self.smart_config["filtering"]["time_window"]
        min_frames = self.smart_config["filtering"]["min_frames"]
        max_frames = self.smart_config["filtering"]["max_frames"]
        
        self.logger.debug(f"筛选参数: 时间窗口={time_window}s, 最少帧数={min_frames}, 最多帧数={max_frames}")
        
        # 第一轮：优先选择有人的高分帧
        person_frames = []
        other_frames = []

        for frame_idx, score, detection_result in sorted_frames:
            # 检查是否有人员检测
            has_person = False
            if detection_result and detection_result.boxes is not None and len(detection_result.boxes) > 0:
                for i in range(len(detection_result.boxes)):
                    cls_id = int(detection_result.boxes.cls[i])
                    class_name = self.yolo_model.names[cls_id] if self.yolo_model else ""
                    if class_name == "person":
                        has_person = True
                        break

            if has_person:
                person_frames.append((frame_idx, score, detection_result))
            else:
                other_frames.append((frame_idx, score, detection_result))

        self.logger.info(f"筛选统计: 有人帧数={len(person_frames)}, 无人帧数={len(other_frames)}")

        # 优先选择有人的帧
        for frame_idx, score, _ in person_frames:
            if len(selected) >= max_frames:
                break

            current_time = timestamps[frame_idx]

            # 检查与已选帧的时间窗口冲突
            conflict = any(abs(current_time - timestamps[sel_idx]) < time_window
                          for sel_idx in selected)

            if not conflict:
                selected.append(frame_idx)
                self.logger.info(f"选中有人帧 {frame_idx} (时间: {current_time:.2f}s, 分数: {score:.3f})")

        # 如果还需要更多帧，再选择其他帧
        for frame_idx, score, _ in other_frames:
            if len(selected) >= max_frames:
                break

            current_time = timestamps[frame_idx]

            # 检查与已选帧的时间窗口冲突
            conflict = any(abs(current_time - timestamps[sel_idx]) < time_window
                          for sel_idx in selected)

            if not conflict:
                selected.append(frame_idx)
                self.logger.info(f"选中其他帧 {frame_idx} (时间: {current_time:.2f}s, 分数: {score:.3f})")
        
        # 检查有人帧的数量
        selected_person_frames = []
        for frame_idx in selected:
            for p_frame_idx, _, _ in person_frames:
                if p_frame_idx == frame_idx:
                    selected_person_frames.append(frame_idx)
                    break

        min_person_frames = self.smart_config["filtering"].get("min_person_frames", 1)
        self.logger.info(f"已选中 {len(selected_person_frames)} 个有人帧，最小要求: {min_person_frames}")

        # 如果有人帧不足，强制添加更多有人帧
        if len(person_frames) > 0 and len(selected_person_frames) < min_person_frames:
            self.logger.info(f"有人帧不足，强制添加更多有人帧")
            for frame_idx, score, _ in person_frames:
                if frame_idx not in selected and len(selected_person_frames) < min_person_frames:
                    selected.append(frame_idx)
                    selected_person_frames.append(frame_idx)
                    self.logger.info(f"强制添加有人帧 {frame_idx} (分数: {score:.3f})")

        # 第二轮：如果帧数不足最小要求，智能填充
        if len(selected) < min_frames:
            self.logger.info(f"当前选中 {len(selected)} 帧，少于最小要求 {min_frames}，开始智能填充")
            
            # 计算时间间隔，优先填充时间空隙大的区域
            if selected:
                selected_times = sorted([timestamps[i] for i in selected])
                
                # 寻找最大时间间隔
                max_gap = 0
                gap_position = 0
                for i in range(len(selected_times) - 1):
                    gap = selected_times[i + 1] - selected_times[i]
                    if gap > max_gap:
                        max_gap = gap
                        gap_position = i
                
                # 在最大间隔中寻找合适的帧
                if max_gap > time_window * 2:  # 间隔足够大
                    gap_start = selected_times[gap_position]
                    gap_end = selected_times[gap_position + 1]
                    gap_center = (gap_start + gap_end) / 2
                    
                    # 寻找最接近间隔中心的未选中帧
                    best_candidate = None
                    best_distance = float('inf')
                    
                    for frame_idx, score, _ in sorted_frames:
                        if frame_idx not in selected:
                            frame_time = timestamps[frame_idx]
                            if gap_start < frame_time < gap_end:
                                distance = abs(frame_time - gap_center)
                                if distance < best_distance:
                                    best_distance = distance
                                    best_candidate = frame_idx
                    
                    if best_candidate is not None:
                        selected.append(best_candidate)
                        self.logger.debug(f"智能填充帧 {best_candidate} (填补时间间隙)")
            
            # 如果仍然不足，按分数强制添加
            for frame_idx, score, _ in sorted_frames:
                if len(selected) >= min_frames:
                    break
                if frame_idx not in selected:
                    selected.append(frame_idx)
                    self.logger.debug(f"强制添加帧 {frame_idx} (分数: {score:.3f})")
        
        # 第三轮：质量优化 - 如果有明显的低质量帧，尝试替换
        if len(selected) >= min_frames:
            self._optimize_frame_quality(selected, sorted_frames, timestamps, time_window)
        
        # 按时间顺序排序返回
        selected.sort()
        
        self.logger.info(f"最终选中 {len(selected)} 帧: {selected}")
        return selected

    def _optimize_frame_quality(self, selected: List[int], sorted_frames: List[Tuple[int, float, Any]], 
                               timestamps: List[float], time_window: float):
        """
        优化已选帧的质量，尝试用更高质量的帧替换低质量帧
        
        Args:
            selected: 已选中的帧索引列表 (会被修改)
            sorted_frames: 按分数排序的所有帧
            timestamps: 时间戳列表
            time_window: 时间窗口
        """
        try:
            # 获取已选帧的分数
            selected_scores = {}
            for frame_idx, score, _ in sorted_frames:
                if frame_idx in selected:
                    selected_scores[frame_idx] = score
            
            # 找到分数最低的帧
            if selected_scores:
                min_score_frame = min(selected_scores.keys(), key=lambda x: selected_scores[x])
                min_score = selected_scores[min_score_frame]
                
                # 寻找可以替换的更高分数帧
                for frame_idx, score, _ in sorted_frames:
                    if frame_idx not in selected and score > min_score * 1.5:  # 分数显著更高
                        frame_time = timestamps[frame_idx]
                        min_frame_time = timestamps[min_score_frame]
                        
                        # 检查是否在合理的时间范围内
                        if abs(frame_time - min_frame_time) <= time_window * 2:
                            # 检查与其他已选帧的冲突
                            conflict = any(abs(frame_time - timestamps[sel_idx]) < time_window 
                                         for sel_idx in selected if sel_idx != min_score_frame)
                            
                            if not conflict:
                                # 执行替换
                                selected.remove(min_score_frame)
                                selected.append(frame_idx)
                                self.logger.debug(f"质量优化: 用帧 {frame_idx} (分数: {score:.3f}) 替换帧 {min_score_frame} (分数: {min_score:.3f})")
                                break
                                
        except Exception as e:
            self.logger.warning(f"帧质量优化过程出错: {e}")

    async def analyze_video_with_smart_filter(self, video_path: str):
        """
        使用智能筛选模式分析视频
        
        Args:
            video_path: 视频文件路径或URL
        
        Returns:
            视频分析的流式结果
        """
        temp_video_path = None
        grid_path = None
        all_frame_paths = []
        
        try:
            self.logger.info(f"开始智能筛选分析: {video_path}")
            
            # 下载视频
            temp_video_path = await self.download_m3u8_to_mp4(video_path)
            if not temp_video_path:
                raise Exception("视频下载失败")
            
            # 提取所有帧
            all_frames, all_timestamps, all_frame_paths = await self.extract_key_frames(temp_video_path)
            if not all_frames:
                raise Exception("帧提取失败")
            
            self.logger.info(f"提取到 {len(all_frames)} 帧，开始智能筛选...")
            
            # 智能筛选关键帧
            selected_indices, priority_scores = await self._smart_filter_frames(all_frames, all_timestamps)
            
            if not selected_indices:
                self.logger.error("智能筛选未选中任何帧，这不应该发生")
                raise Exception("智能筛选失败")
            
            # 获取筛选后的帧
            frames = [all_frames[i] for i in selected_indices]
            timestamps = [all_timestamps[i] for i in selected_indices]
            frame_paths = [all_frame_paths[i] for i in selected_indices]
            
            self.logger.info(f"智能筛选完成: {len(all_frames)} -> {len(frames)} 帧")
            self.logger.info(f"选中帧时间点: {[f'{t:.2f}s' for t in timestamps]}")
            self.logger.info(f"优先级分数: {[f'{s:.3f}' for s in priority_scores]}")
            
            # 根据筛选后的帧数选择分析模式
            analysis_mode = self._determine_analysis_mode(len(frames))
            self.logger.info(f"选择分析模式: {analysis_mode}")
            
            stream = await self._execute_analysis_mode(analysis_mode, frames, frame_paths)
            
            return stream
            
        except Exception as e:
            self.logger.error(f"智能筛选视频分析失败: {e}")
            # 如果智能筛选完全失败，尝试回退到网格模式
            if all_frame_paths:
                self.logger.info("尝试回退到网格模式...")
                try:
                    grid_path = await self._create_keyframes_grid(all_frame_paths[:16])  # 限制帧数
                    if grid_path:
                        base64_grid = await self.encode_base64_file(grid_path)
                        stream = await self.client.chat.completions.create(
                            model=self.model,
                            messages=[{
                                "role": "user",
                                "content": [
                                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_grid}"}},
                                    {"type": "text", "text": self.config["prompt"]["video_analysis_grid"]},
                                ]
                            }],
                            stream=True,
                        )
                        self.logger.info("回退到网格模式成功")
                        return stream
                except Exception as fallback_error:
                    self.logger.error(f"回退模式也失败: {fallback_error}")
            
            raise
        finally:
            # 清理临时文件（包括未选中的帧）
            cleanup_files = []
            
            if temp_video_path:
                cleanup_files.append(("临时视频文件", temp_video_path))
            if grid_path:
                cleanup_files.append(("关键帧网格文件", grid_path))
            
            # 清理所有提取的帧文件
            for frame_path in all_frame_paths:
                cleanup_files.append(("关键帧文件", frame_path))
            
            # 执行清理
            for file_type, file_path in cleanup_files:
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        self.logger.debug(f"{file_type}已删除: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.logger.warning(f"删除{file_type}失败 {file_path}: {e}")

    def _determine_analysis_mode(self, frame_count: int) -> str:
        """
        根据帧数确定最佳分析模式
        
        Args:
            frame_count: 筛选后的帧数
            
        Returns:
            分析模式: 'grid', 'frames', 'adaptive_grid'
        """
        adaptive_config = self.smart_config.get("adaptive_mode", {})
        grid_threshold = adaptive_config.get("grid_threshold", 12)
        frame_threshold = adaptive_config.get("frame_threshold", 25)
        
        if frame_count <= grid_threshold:
            return "frames"  # 帧数少，使用逐帧模式获得详细分析
        elif frame_count > frame_threshold:
            return "adaptive_grid"  # 帧数太多，强制使用网格模式节省token
        else:
            return "grid"  # 中等帧数，使用网格模式平衡效果和效率

    async def _execute_analysis_mode(self, mode: str, frames: List[Image.Image], 
                                   frame_paths: List[str]):
        """
        执行指定的分析模式
        
        Args:
            mode: 分析模式
            frames: 帧列表
            frame_paths: 帧文件路径列表
            
        Returns:
            分析结果流
        """
        if mode in ["grid", "adaptive_grid"]:
            # 网格模式
            self.logger.info(f"使用网格模式分析 {len(frames)} 帧")
            grid_path = await self._create_keyframes_grid(frame_paths)
            if not grid_path:
                raise Exception("网格创建失败")
            
            base64_grid = await self.encode_base64_file(grid_path)
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_grid}"}},
                        {"type": "text", "text": self.config["prompt"]["video_analysis_grid"]},
                    ]
                }],
                stream=True,
            )
            
        elif mode == "frames":
            # 逐帧模式
            self.logger.info(f"使用逐帧模式分析 {len(frames)} 帧")
            base64_frames = []
            for frame in frames:
                buffer = BytesIO()
                frame.save(buffer, format="JPEG", quality=self.image_quality)
                base64_frames.append(base64.b64encode(buffer.getvalue()).decode('utf-8'))
            
            messages = [{
                "role": "user",
                "content": [
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{frame}"}}
                    for frame in base64_frames
                ] + [{"type": "text", "text": self.config["prompt"]["video_analysis"]}]
            }]
            
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True,
            )
            
        else:
            raise ValueError(f"未知的分析模式: {mode}")
        
        return stream

# 示例用法
if __name__ == "__main__":
    # 从URL获取视频
    url = r"https://device-cloudstore-prod.oss-cn-shenzhen.aliyuncs.com/pro_file/P3J1244310035/video_base/3_days_expires_1737969824.m3u8"
    url = url.split("?")[0]
    
    try:
        model_processor = ModelProcessor()
    except Exception as e:
        logger.error(f"模型处理器初始化失败: {e}")
        exit(1)
    
    # 使用asyncio运行异步函数
    import asyncio
    
    async def main():
        try:
            print("\n" + "=" * 20 + "思考过程" + "=" * 20 + "\n")
            
            # 获取流对象
            stream = await model_processor.analyze_video_with_cloud(url)
            reasoning_content = ""
            answer_content = ""
            is_answering = False

            # 处理流式响应
            async for chunk in stream:
                if hasattr(chunk, 'choices') and chunk.choices:
                    delta = chunk.choices[0].delta
                    
                    # 处理思考过程
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        print(delta.reasoning_content, end='', flush=True)
                        reasoning_content += delta.reasoning_content
                    elif hasattr(delta, 'content') and delta.content is not None:
                        # 开始回复
                        if not is_answering:
                            print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                            is_answering = True
                        print(delta.content, end='', flush=True)
                        answer_content += delta.content
                        
        except Exception as e:
            logger.error(f"视频分析失败: {e}")
            print(f"\n[ERROR] 处理失败: {e}")
    
    # 运行异步主函数
    asyncio.run(main())
