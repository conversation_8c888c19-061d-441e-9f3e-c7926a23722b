# 预下载CLIP模型，避免运行时下载338M文件

import os; 
import clip; 
print('预下载CLIP模型: ViT-B/32 (338MB)')
try: 
    model, preprocess = clip.load('ViT-B/32', device='cpu')
    print('CLIP ViT-B/32模型预下载完成')
    del model, preprocess
except Exception as e: 
    print(f'CLIP模型预下载失败: {e}')

try: 
    from ultralytics import YOLOWorld
    print('预下载YOLO-World模型: yolov8s-worldv2.pt')
    model = YOLOWorld('yolov8s-worldv2.pt')
    print('YOLO-World模型预下载完成')
    del model
except Exception as e: 
    print(f'YOLO-World模型预下载失败: {e}')
print('所有模型预下载完成')
