import ray
import json
import logging
import asyncio
from typing import Optional, Dict, Any, List, Set
import redis.asyncio as redis


@ray.remote#(num_cpus=1, max_task_retries=3)
class RedisActor:
    """专门管理Redis连接的Actor"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Redis连接管理器
        
        Args:
            config: 包含Redis配置的字典
        """
        self.config = config
        self.redis_config = config.get('redis', {})
        
        # 初始化连接对象
        self.redis_client = None
        
        # 连接状态
        self.redis_connected = False
        
        # 重连配置
        self.reconnect_delay = 1  # 初始重连延迟（秒）
        self.max_reconnect_delay = 60  # 最大重连延迟（秒）
        self.max_retries = 5  # 最大重试次数
        
        # 监控任务
        self.monitor_task = None
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    async def start(self):
        """启动Redis连接和监控"""
        try:
            # 启动连接
            await self._connect_redis()
            
            # 启动连接监控
            self.monitor_task = asyncio.create_task(self._monitor_connections())
            
            return True
        except Exception as e:
            self.logger.error(f"Error starting Redis connection: {e}")
            return False
            
    async def stop(self):
        """停止Redis连接和监控"""
        try:
            # 停止监控任务
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 停止连接
            if self.redis_client:
                await self.redis_client.close()
                
            return True
        except Exception as e:
            self.logger.error(f"Error stopping Redis connection: {e}")
            return False
            
    async def _monitor_connections(self):
        """监控连接状态并自动重连"""
        while True:
            try:
                # 检查Redis连接
                if not self.redis_connected:
                    self.logger.warning("Redis disconnected, attempting to reconnect...")
                    await self._reconnect_redis()
                
                # 定期进行健康检查
                await self._health_check()
                
                # 重置重连延迟
                self.reconnect_delay = 1
                
                # 等待一段时间再次检查
                await asyncio.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                self.logger.error(f"Error in Redis connection monitor: {e}")
                await asyncio.sleep(5)
                
    async def _health_check(self):
        """执行健康检查"""
        try:
            # 检查Redis连接
            if self.redis_client and self.redis_connected:
                try:
                    await self.redis_client.ping()
                    self.logger.debug("Redis health check passed")
                except Exception as e:
                    self.logger.error(f"Redis health check failed: {e}")
                    self.redis_connected = False
                    
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            
    async def _reconnect_redis(self):
        """重连Redis"""
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                self.logger.info(f"Attempting to reconnect Redis (attempt {retry_count + 1}/{self.max_retries})")
                await self._connect_redis()
                if self.redis_connected:
                    self.logger.info("Successfully reconnected Redis")
                    return True
                    
            except Exception as e:
                self.logger.error(f"Failed to reconnect Redis: {e}")
                
            retry_count += 1
            await asyncio.sleep(min(self.reconnect_delay * (2 ** retry_count), self.max_reconnect_delay))
            
        self.logger.error("Max retry attempts reached for Redis")
        return False

    async def _connect_redis(self):
        """连接到Redis"""
        try:
            # 关闭现有连接
            if self.redis_client:
                self.logger.info("Closing existing Redis connection")
                await self.redis_client.close()
                self.redis_client = None
                self.redis_connected = False
            
            # 从配置中获取参数
            host = self.redis_config.get('host', 'localhost')
            port = self.redis_config.get('port', 6379)
            db = self.redis_config.get('db', 0)
            password = self.redis_config.get('password', None)
            socket_timeout = self.redis_config.get('socket_timeout', 5)
            
            # 创建Redis客户端
            self.logger.info(f"Connecting to Redis at {host}:{port}/{db}")
            client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                socket_timeout=socket_timeout,
                decode_responses=True
            )
            
            # 测试连接
            await client.ping()
            
            self.redis_client = client
            self.redis_connected = True
            self.logger.info("Redis connected successfully")
            
            return True
        except Exception as e:
            self.logger.error(f"Error connecting to Redis: {e}")
            self.redis_connected = False
            return False

    async def redis_get(self, key: str) -> Optional[str]:
        """从Redis获取值
        
        Args:
            key: Redis键
            
        Returns:
            值字符串，如果键不存在则返回None
        """
        if not self.redis_connected:
            self.logger.error("Cannot get value: Redis not connected")
            return None
            
        try:
            value = await self.redis_client.get(key)
            return value
        except Exception as e:
            self.logger.error(f"Error getting value from Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return None

    async def redis_set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置Redis键值
        
        Args:
            key: Redis键
            value: 要存储的值
            ex: 可选的过期时间（秒）
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot set value: Redis not connected")
            return False
            
        try:
            await self.redis_client.set(key, value, ex=ex)
            return True
        except Exception as e:
            self.logger.error(f"Error setting value in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_sadd(self, key: str, values: str, ex: Optional[int] = None) -> bool:
        """向Redis集合添加元素
        
        Args:
            key: 集合键
            values: 要添加的值
            ex: 可选的过期时间（秒）
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot add to set: Redis not connected")
            return False
            
        try:
            # 添加到集合
            await self.redis_client.sadd(key, values)
            
            # 如果指定了过期时间，设置键的过期时间
            if ex is not None:
                await self.redis_client.expire(key, ex)
                
            return True
        except Exception as e:
            self.logger.error(f"Error adding to set in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_srem(self, key: str, values: str) -> bool:
        """从Redis集合中移除元素
        
        Args:
            key: 集合键
            values: 要移除的值
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot remove from set: Redis not connected")
            return False
            
        try:
            await self.redis_client.srem(key, values)
            return True
        except Exception as e:
            self.logger.error(f"Error removing from set in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_smembers(self, key: str) -> Set[str]:
        """获取Redis集合中的所有元素
        
        Args:
            key: 集合键
            
        Returns:
            集合中的元素，如果出错则返回空集合
        """
        if not self.redis_connected:
            self.logger.error("Cannot get set members: Redis not connected")
            return set()
            
        try:
            members = await self.redis_client.smembers(key)
            return members
        except Exception as e:
            self.logger.error(f"Error getting set members from Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return set()

    async def redis_incr(self, key: str, value=None) -> bool:
        """增加Redis计数器
        
        Args:
            key: 计数器键
            value: 增加的值（默认为1）
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot increment counter: Redis not connected")
            return False
            
        try:
            if value:
                await self.redis_client.incrby(key, value)
            else:
                await self.redis_client.incr(key)
            return True
        except Exception as e:
            self.logger.error(f"Error incrementing counter in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_decr(self, key: str, value=None) -> bool:
        """减少Redis计数器
        
        Args:
            key: 计数器键
            value: 减少的值（默认为1）
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot decrement counter: Redis not connected")
            return False
            
        try:
            if value:
                await self.redis_client.decrby(key, value)
            else:
                await self.redis_client.decr(key)
            return True
        except Exception as e:
            self.logger.error(f"Error decrementing counter in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_llen(self, key):
        """获取Redis列表长度
        
        Args:
            key: 列表键
            
        Returns:
            列表长度，出错时返回0
        """
        if not self.redis_connected:
            self.logger.error("Cannot get list length: Redis not connected")
            return 0
            
        try:
            length = await self.redis_client.llen(key)
            return length
        except Exception as e:
            self.logger.error(f"Error getting list length from Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return 0

    async def redis_lpush(self, key, value):
        """向Redis列表左侧添加元素
        
        Args:
            key: 列表键
            value: 要添加的值
            
        Returns:
            成功返回列表新长度，出错返回-1
        """
        if not self.redis_connected:
            self.logger.error("Cannot push to list: Redis not connected")
            return -1
            
        try:
            count = await self.redis_client.lpush(key, value)
            return count
        except Exception as e:
            self.logger.error(f"Error pushing to list in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return -1

    async def redis_rpop(self, key):
        """从Redis列表右侧弹出元素
        
        Args:
            key: 列表键
            
        Returns:
            弹出的元素，空列表或出错时返回None
        """
        if not self.redis_connected:
            self.logger.error("Cannot pop from list: Redis not connected")
            return None
            
        try:
            value = await self.redis_client.rpop(key)
            return value
        except Exception as e:
            self.logger.error(f"Error popping from list in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return None

    async def redis_expire(self, key, seconds):
        """设置Redis键的过期时间
        
        Args:
            key: 键
            seconds: 过期时间（秒）
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot set expiration: Redis not connected")
            return False
            
        try:
            result = await self.redis_client.expire(key, seconds)
            return result > 0
        except Exception as e:
            self.logger.error(f"Error setting expiration in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def redis_keys(self, pattern: str) -> List[str]:
        """获取匹配模式的Redis键
        
        Args:
            pattern: 匹配模式
            
        Returns:
            匹配的键列表，出错时返回空列表
        """
        if not self.redis_connected:
            self.logger.error("Cannot search keys: Redis not connected")
            return []
            
        try:
            keys = await self.redis_client.keys(pattern)
            return keys
        except Exception as e:
            self.logger.error(f"Error searching keys in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return []
            
    async def redis_delete(self, key: str) -> bool:
        """删除Redis键
        
        Args:
            key: 要删除的键
            
        Returns:
            成功返回True，否则返回False
        """
        if not self.redis_connected:
            self.logger.error("Cannot delete key: Redis not connected")
            return False
            
        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except Exception as e:
            self.logger.error(f"Error deleting key in Redis: {e}")
            self.redis_connected = False  # 标记为断开，触发重连
            return False

    async def check_health(self) -> Dict[str, bool]:
        """检查Redis连接健康状态
        
        Returns:
            包含Redis连接状态的字典
        """
        # 检查Redis连接
        redis_healthy = False
        if self.redis_client and self.redis_connected:
            try:
                await self.redis_client.ping()
                redis_healthy = True
            except:
                redis_healthy = False
        
        return {
            "redis": redis_healthy
        }

    async def restart(self):
        """重启所有连接"""
        await self.stop()
        await asyncio.sleep(1)  # 等待资源释放
        return await self.start() 
