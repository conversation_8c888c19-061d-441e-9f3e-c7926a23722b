# 视频理解服务 (Video Understanding Service)

基于Ray Serve的分布式视频理解和向量检索服务，采用独立Actor Pool架构设计，支持高并发视频分析和实时向量检索。

## 📋 目录

- [项目概述](#项目概述)
- [核心特性](#核心特性)
- [系统架构](#系统架构)
- [环境要求](#环境要求)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [API文档](#api文档)
- [测试指南](#测试指南)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)
- [性能调优](#性能调优)

## 🎯 项目概述

视频理解服务是一个基于Ray Serve构建的分布式微服务系统，专门用于视频内容分析和向量检索。该服务采用独立的Actor Pool架构，将视频处理和向量检索任务分离，确保资源隔离和高并发性能。

### 主要功能

- **视频内容分析**: 支持视频帧提取、内容理解和特征向量化
- **向量检索服务**: 基于Milvus的高性能向量相似度搜索
- **分布式任务处理**: 使用Kafka进行任务队列管理，支持事件ID追踪
- **实时状态管理**: 基于Redis的分布式状态存储
- **弹性扩缩容**: 支持自动扩缩容和负载均衡
- **事件追踪**: 完整的事件ID追踪机制，支持从Kafka到数据库的全链路追踪

## ✨ 核心特性

### 🏗️ 独立Actor Pool架构
- **视频处理Actor Pool**: 专门处理CPU密集型的视频分析任务
- **向量检索Actor Pool**: 专门处理向量检索和相似度计算
- **资源隔离**: 两个Pool相互独立，避免资源竞争

### 🚀 高性能设计
- **并发处理**: 支持多任务并行执行
- **背压控制**: 智能负载限制，防止系统过载
- **连接池管理**: 高效的数据库和中间件连接复用

### 🔧 灵活配置
- **环境分离**: 支持开发/生产环境配置
- **动态扩缩容**: 基于负载自动调整实例数量
- **监控集成**: 完整的指标收集和上报机制

## 🏛️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    视频理解服务架构图                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐    ┌──────────────────────────────────┐   │
│  │   HTTP      │    │         Ray Serve               │   │
│  │   Client    │───▶│      (FastAPI + 路由)           │   │
│  └─────────────┘    └──────────────────────────────────┘   │
│                                       │                     │
│                                       ▼                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              VideoUnstandingService                     │ │
│  │  ┌─────────────────┐  ┌─────────────────────────────┐  │ │
│  │  │ Video Actor Pool│  │  Search Actor Pool          │  │ │
│  │  │                 │  │                             │  │ │
│  │  │ - 视频分析      │  │ - 向量检索                  │  │ │
│  │  │ - 特征提取      │  │ - 相似度计算                │  │ │
│  │  │ - CPU密集      │  │ - 轻量级任务                │  │ │
│  │  │ - 事件ID追踪    │  │ - 事件关联查询              │  │ │
│  │  └─────────────────┘  └─────────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────┘ │
│                           │         │                       │
│                           ▼         ▼                       │
│  ┌─────────────┐    ┌─────────┐   ┌─────────┐              │
│  │   Kafka     │    │  Redis  │   │ Milvus  │              │
│  │ (任务队列)   │    │(状态存储)│   │(向量DB) │              │
│  │ + event_id  │    │         │   │+ event_id│              │
│  └─────────────┘    └─────────┘   └─────────┘              │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 组件说明

| 组件 | 功能 | 技术栈 |
|------|------|--------|
| Ray Serve | 服务网关和路由 | FastAPI + Ray |
| Video Actor Pool | 视频处理任务池，支持event_id追踪 | Ray Actor |
| Search Actor Pool | 向量检索任务池，支持事件关联查询 | Ray Actor |
| Kafka | 异步任务队列，传递taskId作为event_id | Apache Kafka |
| Redis | 分布式状态存储，维护事件状态 | Redis Cluster |
| Milvus | 向量数据库，存储event_id关联数据 | Milvus Vector DB |

## 💻 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Python版本**: 3.8+
- **内存**: 最低 4GB，推荐 8GB+
- **CPU**: 最低 4核，推荐 8核+
- **存储**: 至少 20GB 可用空间

### 依赖服务
- **Kafka**: 用于任务队列管理
- **Redis**: 用于状态存储和缓存
- **Milvus**: 用于向量存储和检索

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd video_unstanding

# 创建虚拟环境并同步依赖
uv sync
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env_dev.example .env_dev
cp .env_prod.example .env_prod

# 编辑环境变量
vim .env_dev
```

### 3. 启动依赖服务

```bash
#检查第三方组件redis,milvus,kafka的运行情况
```

### 4. 启动服务

```bash
# 开发环境
export DEPLOY_MODE=dev
ray start --head
#部署启动
uv run serve deploy server_dev.yaml
#命令行开发启动
uv run serve run server_dev.yaml

# 生产环境
export DEPLOY_MODE=prod
ray start --head
#部署启动
uv run serve deploy server_dev.yaml
#命令行开发启动
uv run serve run server_dev.yaml
```

### 5. 验证服务

```bash
# 检查服务状态
curl http://localhost:8009/kaadas_ai/video_unstanding/search/stats

# 运行测试
uv run test_vector_search.py
```

## ⚙️ 配置说明

### 环境配置文件

#### 开发环境 (`config_dev.yaml`)
```yaml
# Ray集群配置
RAY:
  address: "auto"
  namespace: "video_unstanding_dev"

# Actor Pool配置
ACTOR_POOLS:
  video_pool_size: 3        # 视频处理Actor数量
  search_pool_size: 2       # 向量检索Actor数量
  
# 中间件配置
KAFKA:
  bootstrap_servers: ["localhost:9092"]
  topic: "video_unstanding_topic"
  
REDIS:
  host: "localhost"
  port: 6379
  db: 0
```

#### 生产环境 (`config_prod.yaml`)
```yaml
# 生产环境配置
RAY:
  address: "auto"
  namespace: "video_unstanding_prod"

ACTOR_POOLS:
  video_pool_size: 6        # 更多Actor支持高并发
  search_pool_size: 4
```

### Ray Serve配置

#### 资源配置
```yaml
ray_actor_options:
  num_cpus: 2               # CPU核心数
  num_gpus: 0               # GPU数量
  memory: 2147483648        # 内存限制 (2GB)
  object_store_memory: **********  # 对象存储内存
```

#### 扩缩容配置
```yaml
autoscaling_config:
  min_replicas: 1           # 最小副本数
  max_replicas: 1           # 最大副本数
  target_ongoing_requests: 64    # 目标并发请求数
  max_ongoing_requests: 64       # 最大并发请求数
  upscale_delay_s: 30           # 扩容延迟
  downscale_delay_s: 600        # 缩容延迟
```

#### 负载控制
```yaml
max_queued_requests: 100    # 最大排队请求数
health_check_period_s: 1800 # 健康检查周期
graceful_shutdown_timeout_s: 20  # 优雅关闭超时
```

## 📚 API文档

### 🔄 事件追踪机制

#### Event ID 数据流
系统实现了完整的事件追踪链路，从Kafka消息到数据库存储和搜索返回：

```
Kafka Message (taskId) 
    ↓
Server.consume (event_id = taskId)
    ↓  
VideoProcessor.process(esn, event_id, url)
    ↓
├─ VideoProcessor.process_video(esn, event_id, url)
│   └─ Milvus.video_collection (包含event_id)
│
└─ VideoProcessor.process_frame(esn, event_id, url) [v1.4.0架构就绪]
    └─ Milvus.frame_collection (包含event_id)
    ↓
VectorSearchActor.combined_search()
    └─ 返回包含event_id的完整搜索结果 [v1.4.0优化]
```

#### Event ID 用途
- **唯一标识**: 每个视频处理任务的唯一标识符
- **状态追踪**: 跟踪任务从队列到完成的整个生命周期
- **关联查询**: 通过event_id关联视频数据和帧数据
- **调试追踪**: 便于问题排查和性能分析
- **搜索关联**: v1.4.0起，所有搜索结果都包含event_id，支持完整的事件关联查询

#### 搜索功能完整性 (v1.4.0)

| 功能特性 | v1.3.0 | v1.4.0 | 改进说明 |
|---------|--------|--------|----------|
| 事件追踪 | ✅ 基础event_id | ✅ 完整event_id返回 | 搜索结果包含event_id |
| API响应格式 | ⚠️ 复杂嵌套 | ✅ 简化扁平 | 移除entity包装，直接返回核心字段 |
| 向量维度 | ❌ 维度混淆 | ✅ 正确配置 | 文本1536维，图像512维 |
| 帧搜索架构 | ❌ 缺少event_id | ✅ 架构就绪 | 支持未来启用 |
| 字段统一性 | ⚠️ 字段名不一致 | ✅ 统一命名 | video_url/source_url → url |

**核心改进:**
- **简化响应格式**: 移除复杂的entity嵌套结构，直接返回核心字段
- **事件追踪支持**: 每个搜索结果都包含event_id，支持端到端追踪
- **多维度检索**: 支持标题、摘要、帧三种维度的语义搜索
- **字段命名统一**: 统一使用`url`字段名，提升API一致性
- **性能优化**: 减少数据传输量，提升响应速度

### 向量检索API

#### 搜索视频内容
```http
POST /kaadas_ai/video_unstanding/search
Content-Type: application/json

{
  "esn": "ESN001",
  "query_text": "快递员送包裹",
  "limit": 5,
  "drop_ratio": 0.2,
  "search_types": ["title", "summary", "frame"]
}
```

**参数说明:**
- `esn`: 设备序列号，用于过滤特定设备的数据
- `query_text`: 要搜索的文本查询，支持自然语言描述
- `limit`: 每种搜索类型返回的最大结果数量（默认5）
- `drop_ratio`: 相似度阈值，低于此值的结果将被过滤（默认0.2）
- `search_types`: 搜索类型数组，可选值：
  - `"title"`: 基于视频标题的语义搜索
  - `"summary"`: 基于视频摘要的语义搜索  
  - `"frame"`: 基于视频帧的图像语义搜索

**返回字段说明（v1.4.0简化）:**

**视频搜索结果（title/summary）:**
- `video_id`: 视频唯一标识符（INT64）
- `score`: 相似度分数（FLOAT，0-1）
- `esn`: 设备序列号（VARCHAR）
- `event_id`: 事件追踪ID，对应Kafka消息中的taskId（INT64）⭐
- `url`: 视频文件URL（VARCHAR）

**帧搜索结果（frame）:**
- `frame_id`: 帧唯一标识符（INT64）
- `score`: 相似度分数（FLOAT，0-1）
- `esn`: 设备序列号（VARCHAR）
- `event_id`: 事件追踪ID（INT64）⭐
- `url`: 视频文件URL（VARCHAR）
- `timestamp`: 视频内时间点（FLOAT秒）

**注意**: 搜索结果中包含`event_id`字段，该字段对应Kafka消息中的`taskId`，用于实现完整的事件追踪链路。

**响应示例:**
```json
{
  "results": [
    {
      "total_matches": 5,
      "title_matches": {
        "total_summary": "发现1个快递员正常送包裹事件，安全风险：无风险。",
        "total": 1,
        "data": [
          {
            "score": 0.95,
            "video_id": "450123456789012345",
            "video_url": "https://example.com/video_001.m3u8",
            "esn": "ESN001",
            "event_id": 12345,
            "title": "快递员送包裹",
            "summary": "顺丰快递员正常上门送货",
            "timestamp": "2024-12-27T14:30:00+08:00",
            "num_persons": 1,
            "persons": ["快递员"],
            "security_risk": "无风险",
            "recommendation": "正常配送活动"
          }
        ]
      },
      "summary_matches": {
        "total_summary": "发现2个外卖配送事件，均为正常活动，安全风险：无风险。",
        "total": 2,
        "data": [
          {
            "score": 0.88,
            "video_id": "450123456789012346",
            "video_url": "https://example.com/video_002.m3u8",
            "esn": "ESN001",
            "event_id": 12346,
            "title": "外卖配送记录",
            "summary": "外卖员正常上门送餐",
            "timestamp": "2024-12-27T19:15:00+08:00",
            "num_persons": 1,
            "persons": ["外卖员"],
            "security_risk": "无风险",
            "recommendation": "正常外卖配送"
          }
        ]
      },
      "frame_matches": {
        "total_summary": "发现2个相关场景，时间点分布在下午，安全风险：无风险。",
        "total": 2,
        "data": [
          {
            "frame_id": "450123456789012550",
            "score": 0.82,
            "esn": "ESN001",
            "event_id": 12347,
            "url": "https://example.com/video_003.m3u8",
            "timestamp": 125.5
          }
        ]
      }
    }
  ]
}
```

#### 获取服务统计信息
```http
GET /kaadas_ai/video_unstanding/search/stats
```

**响应示例:**
```json
{
  "video_count": 1500,
  "frame_count": 45000,
  "video_actor_pool_size": 3,
  "video_actor_pool_busy": 1,
  "search_actor_pool_size": 2, 
  "search_actor_pool_busy": 0
}
```

#### 生产Kafka消息（测试专用）
```http
POST /kaadas_ai/video_unstanding/produce
Content-Type: application/json

{
  "task_id": 12345,
  "file_url": "https://example.com/video.m3u8",
  "esn": "ESN001",
  "start_time": "2024-08-15T14:30:00Z",
  "retry_count": 0
}
```

**参数说明:**
- `task_id`: 任务ID，将作为event_id用于追踪
- `file_url`: 视频文件URL
- `esn`: 设备序列号
- `start_time`: 任务开始时间（可选）
- `retry_count`: 重试次数（可选，默认0）

**响应示例:**
```json
{
  "status": "success",
  "message": "Message sent to Kafka",
  "task_id": 12345,
  "event_id": 12345
}
```

#### 重置集合（维护专用）
```http
POST /kaadas_ai/video_unstanding/reset-collections
Content-Type: application/json

{
  "confirm": true
}
```

**参数说明:**
- `confirm`: 确认重置操作（必须为true）

**响应示例:**
```json
{
  "status": "success", 
  "message": "Collections reset successfully",
  "video_collection": "video_collection_name",
  "frame_collection": "frame_collection_name"
}
```

**注意**: 此操作将删除所有现有数据并重新创建包含event_id字段的集合架构。

### 错误处理

#### 常见错误码

| 状态码 | 说明 | 解决方案 |
|--------|------|----------|
| 503 | 服务过载(背压控制) | 降低请求频率或增加 max_queued_requests |
| 500 | 内部服务错误 | 检查日志和依赖服务状态 |
| 404 | 路径不存在 | 检查API路径和服务部署状态 |
| 400 | 参数错误 | 检查请求参数格式和必填字段 |
| 422 | 数据验证失败 | 检查search_types等枚举值是否正确 |

#### 特定错误情况

**集合架构不匹配:**
```json
{
  "error": "DataNotMatchException: Attempt to insert an unexpected field `event_id` to collection without enabling dynamic field"
}
```
**解决方案:** 使用 `/reset-collections` API重置集合架构

**Event ID追踪失败:**
```json
{
  "error": "Event ID tracking failed: missing task_id in Kafka message"
}
```
**解决方案:** 确保Kafka消息包含有效的task_id字段

**向量索引未就绪:**
```json
{
  "error": "Index not found for field: title_embedding"
}
```
**解决方案:** 等待索引创建完成或手动重建索引

## 🧪 测试指南

### 单元测试

```bash
# 运行向量检索测试
python test_vector_search.py

# 运行模型处理测试  
python test_model_processor.py

# 测试新的搜索功能
python -c "
import asyncio
from test_vector_search import VectorSearchClient

async def test_new_search():
    async with VectorSearchClient() as client:
        # 测试多类型搜索
        result = await client.search_videos(
            esn='TEST001',
            query_text='快递员送包裹',
            limit=3,
            drop_ratio=0.2
        )
        print('搜索结果:', result)
        
        # 测试服务状态
        stats = await client.get_search_stats()
        print('服务状态:', stats)

asyncio.run(test_new_search())
"
```

### 性能测试

#### 并发检索测试
```python
import asyncio
from test_vector_search import VectorSearchClient

async def performance_test():
    async with VectorSearchClient() as client:
        # 测试100个并发请求
        tasks = []
        for i in range(100):
            task = client.search_videos(f"ESN_{i%10}", f"测试查询_{i}")
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successes = sum(1 for r in results if not isinstance(r, Exception))
        print(f"成功率: {successes/100*100}%")

asyncio.run(performance_test())
```

### 压力测试

```bash
# 使用 Apache Bench 进行压力测试
ab -n 1000 -c 10 -H "Content-Type: application/json" \
   -p search_payload.json \
   http://localhost:8009/kaadas_ai/video_unstanding/search
```

## 📊 监控和日志

### 日志配置

日志文件位置: `/logs/video_unstanding/video_unstanding_YYYYMMDD.log`

```python
# 日志配置示例
logging_config = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "rotation": "100MB",
    "retention": 5
}
```

### 监控指标

#### 系统指标
- **CPU使用率**: Actor池资源消耗
- **内存使用率**: 服务内存占用情况  
- **队列长度**: 排队请求数量
- **处理延迟**: 平均响应时间

#### 业务指标
- **请求成功率**: API调用成功比例
- **检索准确率**: 向量检索质量
- **吞吐量**: 每秒处理请求数
- **Actor利用率**: Actor Pool使用情况
- **事件追踪完整性**: event_id从Kafka到数据库的传递成功率

### 指标查看

```bash
# 查看Ray Dashboard
open http://localhost:8265

# 查看服务状态
curl http://localhost:8009/kaadas_ai/video_unstanding/search/stats

# 查看系统资源
htop
nvidia-smi  # 如果使用GPU
```

## 🔧 故障排除

### 常见问题

#### 1. HTTP 503 背压错误
```
Request dropped due to backpressure (num_queued_requests=X, max_queued_requests=X)
```

**解决方案:**
- 增加 `max_queued_requests` 配置值
- 减少并发请求数量
- 增加Actor Pool大小

#### 2. 服务启动失败
```
ConnectionError: Ray cluster not found
```

**解决方案:**
```bash
# 重启Ray集群
ray stop
ray start --head

# 检查端口占用
netstat -tulpn | grep 8009
```

#### 3. 向量检索错误
```
pymilvus.exceptions.ConnectionNotExistException
```

**解决方案:**
```bash
# 检查Milvus服务状态

# 重启Milvus服务

```

#### 4. Kafka连接问题
```
KafkaTimeoutError: Unable to bootstrap from server
```

**解决方案:**
```bash
# 检查Kafka状态

# 检查网络连通性
telnet localhost 9092
```

### 调试技巧

#### 启用详细日志
```bash
export RAY_DEDUP_LOGS=0
export PYTHONPATH=/workspace/video_unstanding:$PYTHONPATH
```

#### 查看Actor状态
```python
import ray
# 连接到Ray集群
ray.init(address="auto")

# 查看所有Actor
actors = ray.util.state.list_actors()
for actor in actors:
    print(f"Actor: {actor['class_name']}, State: {actor['state']}")
```

## 🔧 技术细节

### 数据库架构 (v1.4.0)
- **event_id字段类型**: INT64，支持完整事件追踪
- **索引配置**: AUTOINDEX + COSINE距离，自动优化检索性能
- **集合TTL**: 30天（2592000秒），自动清理过期数据
- **支持的embedding字段**: 
  - `title_embedding`: 标题语义向量
  - `summary_embedding`: 摘要语义向量  
  - `embedding`: 帧图像向量

### 向量维度配置 (v1.4.0优化)
- **文本嵌入**（title/summary搜索）：
  - 维度：1536维
  - 模型：阿里云百炼 text-embedding-v4
  - 用途：视频标题和摘要的语义理解
- **图像嵌入**（frame搜索）：
  - 维度：512维  
  - 模型：Chinese-CLIP
  - 用途：视频帧的图像-文本匹配
- **配置分离**: 避免维度混淆，确保搜索准确性

### API响应优化 (v1.4.0)
- **简化结构**: 移除entity嵌套，直接返回核心字段
- **核心字段**: video_id/frame_id, score, esn, event_id, url
- **字段统一**: 统一使用`url`替代`video_url`/`source_url`
- **性能提升**: 减少数据传输量，提升响应速度
- **易用性**: 扁平化结构，便于客户端解析

## 📞 业务方Kafka回调功能 (v1.5.0)

### 视频分析完成后的Kafka消息通知

系统在完成视频分析并成功插入向量数据库后，会自动向Kafka发送回调消息，业务方可以通过订阅Kafka主题来接收完整的视频语义分析结果：

#### 核心功能
- **Kafka消息**: 通过Kafka主题发送回调消息，支持异步解耦
- **完整数据**: 传递所有分析结果字段，包括人员、快递员、外卖员、包裹、宠物等
- **可靠传输**: 利用Kafka的消息持久化和重试机制确保消息不丢失
- **多消费者**: 支持多个业务方同时消费同一消息
- **监控友好**: 可通过Kafka监控工具跟踪消息处理状态

#### 消息发送时机
- ✅ 视频分析完成
- ✅ 数据成功插入Milvus向量数据库
- ✅ OSS存储完成（如果启用）
- 🚀 立即发送Kafka消息

#### Kafka主题配置

系统会自动向配置的Kafka输出主题发送回调消息：

```yaml
# Kafka配置
kafka:
  bootstrap_servers: "localhost:9092"
  input_topic: "video_unstanding_topic"        # 输入主题，接收视频处理任务
  output_topic: "video_unstanding_result_topic" # 输出主题，发送分析结果回调消息
  group_id: "video_unstanding_service_group"
```

**主题说明:**
- **input_topic**: 系统消费视频处理任务的主题
- **output_topic**: 系统发送分析结果回调消息的主题，业务方需要订阅此主题

#### Kafka消息格式

**主题**: `video_unstanding_result_topic`
**消息格式**: JSON

**消息结构**:
```json
{
  "messageType": "video_analysis_result",
  "timestamp": "2024-12-27T14:30:00.123456",
  "eventId": 12345,
  "esn": "ESN001",
  "videoUrl": "https://example.com/video.m3u8",
  "analysisResult": {
    "title": "快递员送包裹",
    "timestamp": "2024-12-27 14:30:00",
    "numPersons": 1,
    "persons": [
      {
        "gender": "男性",
        "age": 30,
        "description": "快递员，穿蓝色制服",
        "suspicious": false,
        "reason": "",
        "time_appeared": "14:30:00"
      }
    ],
    "couriers": [
      {
        "company": "顺丰速运",
        "uniform": "蓝色制服",
        "has_package": true,
        "description": "正常送件",
        "suspicious": false,
        "reason": "",
        "time_appeared": "14:30:00"
      }
    ],
    "foodDeliverers": [],
    "packages": [
      {
        "type": "快递",
        "size": "medium",
        "description": "纸箱包裹",
        "time_appeared": "14:30:00"
      }
    ],
    "pets": [],
    "summary": "快递员正常送包裹到门口",
    "securityRisk": "无风险",
    "recommendation": "注意及时取走包裹"
  },
  "processingInfo": {
    "ossPath": "oss://path/to/analysis/result",
    "processingTime": "2024-12-27T14:30:05.789012"
  }
}
```

**字段说明:**
- `messageType`: 消息类型，固定为 "video_analysis_result"
- `timestamp`: 消息发送时间戳
- `eventId`: 事件ID，对应原始任务的taskId
- `esn`: 设备序列号
- `videoUrl`: 视频文件URL
- `analysisResult`: 完整的视频分析结果
- `processingInfo`: 处理相关信息，包括OSS路径等

#### 业务方消费示例

**Python消费者示例**:
```python
from aiokafka import AIOKafkaConsumer
import json
import asyncio

async def consume_callback_messages():
    consumer = AIOKafkaConsumer(
        'video_unstanding_result_topic',
        bootstrap_servers='localhost:9092',
        group_id='business_service_group',
        auto_offset_reset='latest'
    )
    
    await consumer.start()
    try:
        async for message in consumer:
            try:
                # 解析回调消息
                callback_data = json.loads(message.value.decode('utf-8'))
                
                if callback_data.get('messageType') == 'video_analysis_result':
                    event_id = callback_data.get('eventId')
                    esn = callback_data.get('esn')
                    analysis_result = callback_data.get('analysisResult', {})
                    
                    print(f"收到视频分析结果: eventId={event_id}, esn={esn}")
                    print(f"分析结果: {analysis_result.get('summary')}")
                    print(f"安全风险: {analysis_result.get('securityRisk')}")
                    
                    # 业务处理逻辑
                    await process_video_analysis_result(callback_data)
                    
            except Exception as e:
                print(f"处理回调消息失败: {e}")
                
    finally:
        await consumer.stop()

async def process_video_analysis_result(callback_data):
    """处理视频分析结果的业务逻辑"""
    # 这里添加具体的业务处理代码
    pass
```

#### 测试Kafka回调功能

```bash
# 运行Kafka回调功能测试
python test_kafka_callback.py

# 测试消息生产
python -c "
import asyncio
import aiohttp
import json

async def test():
    async with aiohttp.ClientSession() as session:
        test_message = {
            'task_id': 999999,
            'file_url': 'https://example.com/test.m3u8',
            'esn': 'TEST_ESN',
            'retry_count': 0
        }
        async with session.post('http://localhost:8009/kaadas_ai/video_unstanding/produce', json=test_message) as resp:
            result = await resp.json()
            print('测试消息发送结果:', result)

asyncio.run(test())
"
```

#### 监控和日志

Kafka回调相关日志会记录在主日志文件中：

```
2024-12-27 14:30:45 - video_processor - INFO - Callback message sent to Kafka topic video_unstanding_result_topic for event_id: 12345
2024-12-27 14:30:46 - video_processor - INFO - Callback message sent to Kafka successfully for event_id: 12345
```

#### 优势对比

**Kafka消息 vs HTTP回调**:

| 特性 | Kafka消息 | HTTP回调 |
|------|-----------|----------|
| 可靠性 | ✅ 消息持久化，不会丢失 | ❌ 网络异常可能丢失 |
| 扩展性 | ✅ 支持多消费者 | ❌ 单点接收 |
| 解耦性 | ✅ 完全异步解耦 | ⚠️ 需要业务方在线 |
| 监控性 | ✅ Kafka原生监控 | ⚠️ 需要自建监控 |
| 重试机制 | ✅ Kafka自动重试 | ⚠️ 需要自实现 |
| 性能 | ✅ 高吞吐量 | ⚠️ 受网络影响 |

## 🤖 智能总结功能 (v1.5.0)

### LLM驱动的搜索结果总结

系统集成了基于大语言模型（LLM）的智能总结功能，能够对搜索结果进行智能分析和总结，生成 `all_summary` 字段：

#### 核心功能
- **智能分析**: 自动分析所有搜索匹配结果的内容
- **安全评估**: 识别和总结安全风险等级
- **模式识别**: 发现搜索结果中的共同特征和模式
- **简洁表达**: 将复杂的多个搜索结果总结为易读的文本

#### 总结策略

**1. 内容聚合**
- 收集所有标题匹配、摘要匹配和帧匹配结果
- 按相似度分数排序，优先分析高分结果
- 自动处理最多50个结果，避免token超限

**2. 风险分析**
- 自动识别安全风险等级（无风险/低风险/中风险/高风险）
- 特别标注可疑行为和异常事件
- 提供安全建议和注意事项

**3. 内容总结**
- 概括主要事件类型和时间分布
- 识别人员类型（快递员、外卖员、普通人员等）
- 总结行为模式和异常情况

#### 配置说明

在配置文件中设置LLM总结服务：

```yaml
# LLM摘要服务配置（用于总结搜索结果）
llm_summary_service:
  provider: "openai"
  api_key: "your_api_key"
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
  model: "qwen-max"      # 使用通义千问进行文本总结
  max_tokens: 512        # 总结的最大长度
  temperature: 0.3       # 较低的温度确保总结的一致性
```

#### 总结示例

**查询**: "快递员送包裹"

**智能总结结果**:
```
白天有2次快递员正常送包裹，晚上有1次外卖员送餐，均为正常配送活动。安全风险：无风险。
```

#### 降级机制

当LLM服务不可用时，系统提供基础的统计总结：

```
发现10个相关事件，总体安全风险：无风险。
```

#### 测试总结功能

```bash
# 运行LLM总结功能测试
python test_llm_summary.py

# 测试不同场景的总结效果
python -c "
import asyncio
from llm_summary_service import LLMSummaryService

async def test():
    service = LLMSummaryService()
    # 测试连接
    ok = await service.test_connection()
    print('LLM服务状态:', '正常' if ok else '异常')

asyncio.run(test())
"
```

## 🗄️ OSS数据存储 (v1.5.0)

### 存储架构

系统采用阿里云OSS作为数据湖存储解决方案，为AI模型微调和数据分析提供完整的数据管道。

**路径示例:**
- 视频分析: `video-analysis/raw/2025-06-27/ESN001/12345/analysis.json.gz`
- 搜索日志: `search-logs/queries/2025-06-27/query_q_20250627_143052_796.json.gz`
- 训练数据: `training-data/datasets/delivery_scenarios/v1.0.0/data.jsonl.gz`
- 每日备份: `backups/daily/2025-06-27/`

```
video-understanding-data/
├── video-analysis/           # 视频分析结果
│   └── raw/
│       └── {YYYY-MM-DD}/{esn}/{event_id}/
│           ├── analysis.json.gz      # 分析结果（JSON格式）
│           ├── metadata.json.gz      # 元数据信息
│           └── reasoning.txt.gz      # AI推理过程
├── search-logs/             # 搜索日志数据  
│   └── queries/
│       └── {YYYY-MM-DD}/
│           └── query_{query_id}.json.gz
├── training-data/           # 微调训练数据
│   └── datasets/
│       └── {dataset_name}/{version}/
│           ├── data.jsonl.gz         # 训练数据（JSONL格式）
│           └── metadata.json.gz      # 数据集元信息
└── backups/                 # 备份数据
    └── daily/{YYYY-MM-DD}/
```

### 数据格式规范

#### 视频分析数据 (`analysis.json`)
```json
{
  "event_id": 12345,
  "esn": "ESN001", 
  "video_url": "https://example.com/video.m3u8",
  "timestamp": "2024-12-27T14:30:00+08:00",
  "analysis_result": {
    "title": "快递员送包裹",
    "summary": "顺丰快递员正常送件",
    "persons": [...],
    "couriers": [...],
    "security_risk": "无风险"
  },
  "processing_info": {
    "processing_time": "2024-12-27T14:30:45+08:00",
    "mode": "cloud",
    "milvus_insert_result": "success",
    "vector_dimensions": {
      "title_embedding": 1536,
      "summary_embedding": 1536
    }
  },
  "file_info": {
    "analysis_version": "v1.5.0"
  }
}
```

#### 搜索日志数据 (`query_*.json`)
```json
{
  "query_id": "q_20241227_143000_001",
  "timestamp": "2024-12-27T14:30:00+08:00",
  "session_id": "sess_001",
  "request": {
    "esn": "ESN001",
    "query_text": "快递员送包裹",
    "search_types": ["title", "summary"]
  },
  "response": {
    "total_matches": 5,
    "results_summary": {...}
  },
  "performance": {
    "search_duration_seconds": 0.256,
    "parallel_tasks": 2
  }
}
```

#### 训练数据集 (`data.jsonl`)
```jsonl
{"query": "快递员送包裹", "esn": "ESN001", "response": {...}, "feedback": "positive"}
{"query": "外卖员送餐", "esn": "ESN002", "response": {...}, "feedback": "positive"}
```

### OSS配置

在配置文件中添加OSS设置：

```yaml
# OSS存储配置（用于保存视频分析结果和训练数据）
OSS:
  enabled: true              # 是否启用OSS存储功能，可设为false禁用
  access_key_id: "YOUR_ACCESS_KEY_ID"
  access_key_secret: "YOUR_ACCESS_KEY_SECRET" 
  endpoint: "https://oss-cn-shenzhen.aliyuncs.com"
  bucket_name: "video-understanding-data"
  region: "oss-cn-shenzhen"
  storage_class: "STANDARD"
  acl: "PRIVATE"
```

#### OSS控制配置

- **enabled**: 控制是否启用OSS存储功能
  - `true`: 启用OSS存储，所有分析结果和日志将保存到云端
  - `false`: 禁用OSS存储，跳过所有OSS操作，不影响主要功能
  - 默认值: `true`（如果未配置此项）

**禁用OSS的场景**:
- 开发和测试环境，不需要云端存储
- 成本控制，临时关闭云端存储
- 网络受限环境，无法访问OSS服务
- 仅需要向量检索功能，不需要数据分析

### OSS API接口

#### 获取存储统计信息
```http
GET /kaadas_ai/video_unstanding/oss/stats
```

**响应示例:**
```json
{
  "video_analysis_count": 1500,
  "search_logs_count": 8500,
  "training_datasets_count": 12,
  "last_updated": "2024-12-27T14:30:00+08:00"
}
```

#### 创建训练数据集
```http
POST /kaadas_ai/video_unstanding/oss/create-training-dataset
Content-Type: application/json

{
  "dataset_name": "delivery_scenarios",
  "version": "v1.0.0",
  "description": "快递和外卖场景训练数据",
  "start_date": "2024-12-01",
  "end_date": "2024-12-27",
  "esn_filter": ["ESN001", "ESN002"],
  "min_score": 0.8
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "训练数据集创建成功: delivery_scenarios v1.0.0",
  "dataset_path": "training-data/datasets/delivery_scenarios/v1.0.0",
  "data_count": 1250
}
```

#### 每日数据备份
```http
POST /kaadas_ai/video_unstanding/oss/backup-daily
Content-Type: application/json

{
  "date": "2024-12-27"
}
```

### 数据生命周期管理

- **自动压缩**: 所有数据使用gzip压缩，节省存储空间
- **分层存储**: 30天后自动转为低频访问存储
- **数据清理**: 90天后自动删除搜索日志，分析结果保留1年
- **备份策略**: 每日自动备份关键数据到独立目录

### 微调数据准备

OSS存储的数据可直接用于AI模型微调：

1. **数据收集**: 从视频分析结果和搜索日志中提取有价值的数据
2. **数据清洗**: 过滤低质量数据，确保训练数据质量
3. **格式转换**: 转换为模型训练所需的格式（如JSONL）
4. **版本管理**: 支持数据集版本控制，便于实验对比
5. **元数据管理**: 完整记录数据集创建参数和统计信息

### 测试OSS功能

```bash
# 运行OSS配置控制测试
python test_oss_config.py

# 运行OSS集成测试
python test_oss_integration.py

# 测试OSS状态
python -c "
from oss_manager import OSSDataManager

manager = OSSDataManager()
print('OSS启用状态:', manager.enabled)
if manager.enabled:
    stats = manager.get_storage_stats()
    print('OSS统计:', stats)
else:
    print('OSS已禁用，跳过存储操作')
"
```

## 🚀 性能调优

### 1. Actor Pool优化

#### 调整Pool大小
```yaml
# 根据CPU核心数调整
video_pool_size: <CPU核心数>
search_pool_size: <CPU核心数 / 2>
```

#### 内存优化
```yaml
ray_actor_options:
  memory: 4294967296        # 增加到4GB
  object_store_memory: 8589934592  # 增加对象存储
```

### 2. 并发控制优化

```yaml
# 高并发场景配置
max_ongoing_requests: 128    # 增加并发数
max_queued_requests: 500     # 增加队列容量
target_ongoing_requests: 100 # 提高目标并发
```

### 3. 网络优化

```yaml
# HTTP配置优化
http_options:
  host: "0.0.0.0"
  port: 8009
  keep_alive_timeout_s: 30   # 增加keep-alive时间
```

### 4. 数据库优化

#### Milvus优化
```yaml
milvus_config:
  index_type: "IVF_FLAT"     # 选择合适的索引类型
  metric_type: "L2"          # 距离度量方式
  nlist: 1024                # 索引参数调优
  # event_id字段已添加，支持事件追踪查询
```

#### Redis优化
```yaml
redis_config:
  maxmemory: "2gb"           # 设置最大内存
  maxmemory_policy: "allkeys-lru"  # 内存回收策略
```

## 📝 更新日志

### v1.5.0 (2025-12-27)
- 🗄️ **新增OSS数据存储功能**，支持视频分析结果和搜索日志的云端存储
- 🤖 **集成LLM智能总结功能**，对搜索结果进行智能分析和总结，生成`all_summary`字段
- 📞 **重构回调机制为Kafka消息**，将HTTP回调改为Kafka消息推送，提升可靠性和扩展性
- 📊 实现完整的数据湖架构，为AI模型微调提供数据支持
- 🧠 新增基于通义千问的搜索结果智能总结，支持安全风险评估和模式识别
- 🎯 新增训练数据集创建功能，支持从历史数据生成训练集
- 📁 设计分层存储结构，按时间和类型组织数据
- 🔄 集成搜索日志记录，自动保存用户查询和响应数据
- 💾 支持数据压缩和生命周期管理，优化存储成本
- 🛡️ 添加数据备份和恢复机制，确保数据安全
- 📈 新增OSS统计API，实时监控存储使用情况
- 🚦 实现降级机制，确保LLM服务不可用时仍能提供基础总结
- 🔗 **Kafka消息通知**：视频分析完成后自动发送Kafka消息，支持多消费者和消息持久化
- 🚀 **异步解耦设计**：业务方通过订阅Kafka主题接收分析结果，提升系统解耦性

### v1.4.0 (2025-06-27)
- ✨ 大幅简化API响应格式，移除复杂的entity嵌套结构
- 🔧 统一字段命名规范，video_url/source_url统一为url
- 📊 帧集合架构升级，添加event_id字段支持（为将来启用做准备）
- 🐛 修复向量维度配置问题，确保文本嵌入和图像嵌入使用正确维度
- 📈 优化响应性能，减少数据传输量，提升API调用效率

### v1.3.0 (2025-06-26)
- ✨ 新增event_id字段支持，实现完整的事件追踪链路
- 🔧 优化Kafka消息处理，支持taskId到event_id的映射
- 📊 增强Milvus数据库架构，添加事件关联查询能力
- 🐛 修复视频处理流程中的参数传递问题
- 📚 更新API文档，添加event_id字段说明

### v1.2.0 (2025-05-25)
- ✨ 新增独立Actor Pool架构
- 🔧 优化背压控制机制
- 📈 提升向量检索性能
- 🐛 修复内存泄漏问题

### v1.1.0 (2025-04-20)
- ✨ 增加环境配置分离
- 🔧 优化Ray Serve配置
- 📊 添加监控指标收集

### v1.0.0 (2025-01-15)
- 🎉 首次发布
- ✨ 基础视频理解功能
- 🔍 向量检索API

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: Video Understanding Team
- **邮箱**: <EMAIL>
- **文档**: [在线文档](https://docs.video-understanding.com)
- **问题反馈**: [GitHub Issues](https://github.com/company/video_unstanding/issues)

---

**注意**: 本文档会持续更新，请关注最新版本以获取准确信息。
