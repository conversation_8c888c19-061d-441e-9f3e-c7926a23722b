import os
import yaml
import logging
from typing import List, Dict, Any, Optional
from openai import AsyncOpenAI

class LLMSummaryService:
    """LLM摘要服务，用于对搜索结果进行智能总结"""
    
    def __init__(self, config_path: str = None):
        self.logger = logging.getLogger(__name__)
        self.load_config(config_path)
        
        # 初始化LLM客户端
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        self.logger.info(f"LLM Summary Service initialized - Model: {self.model}")
    
    def load_config(self, path: str = None):
        """加载LLM摘要服务配置"""
        if not path:
            deploy_mode = os.getenv("DEPLOY_MODE", "dev")
            path = f"config_{deploy_mode}.yaml"
        
        self.logger.info(f"Loading LLM summary config from: {path}")
        
        with open(path, "r") as f:
            config = yaml.safe_load(f)
        
        llm_config = config.get("llm_summary_service", {})
        self.api_key = llm_config.get("api_key")
        self.base_url = llm_config.get("base_url")
        self.model = llm_config.get("model", "qwen-max")
        self.max_tokens = llm_config.get("max_tokens", 512)
        self.temperature = llm_config.get("temperature", 0.3)
        
        if not all([self.api_key, self.base_url]):
            raise ValueError("LLM summary service configuration is incomplete. Please check api_key and base_url.")
        
        self.logger.info(f"LLM summary config loaded - Model: {self.model}")
    
    async def summarize_by_type(self, matches: List[Dict[str, Any]], match_type: str, query_text: str, esn: str) -> str:
        """
        为特定类型的搜索结果生成独立总结
        
        Args:
            matches: 特定类型的搜索结果列表
            match_type: 匹配类型 ("title", "summary", "frame")
            query_text: 用户的查询文本
            esn: 设备序列号
            
        Returns:
            该类型的总结文本
        """
        try:
            if not matches:
                return f"未发现{self._get_type_name(match_type)}相关事件。"
            
            # 构建该类型的提示词
            prompt = self._build_type_specific_prompt(matches, match_type, query_text, esn)
            
            # 调用LLM进行总结
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": f"你是一个专业的智能门锁{self._get_type_name(match_type)}分析助手。请用简洁、准确的中文总结，重点突出安全相关信息。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens // 2,  # 单个类型总结使用较少token
                temperature=self.temperature
            )
            
            summary_text = response.choices[0].message.content.strip()
            
            self.logger.info(f"Successfully generated {match_type} summary for query: {query_text}, matches: {len(matches)}")
            return summary_text
            
        except Exception as e:
            self.logger.error(f"Failed to generate {match_type} summary: {str(e)}")
            return self._generate_type_fallback_summary(matches, match_type, query_text)

    async def summarize_search_results(self, search_results: Dict[str, Any], query_text: str, esn: str) -> str:
        """
        对搜索结果进行智能总结
        
        Args:
            search_results: 搜索结果字典，包含title_matches, summary_matches, frame_matches
            query_text: 用户的查询文本
            esn: 设备序列号
            
        Returns:
            总结文本
        """
        try:
            # 收集所有的summary文本
            summaries = []
            
            # 从标题匹配结果中收集summary
            for match in search_results.get("title_matches", []):
                if "summary" in match and match["summary"]:
                    summaries.append({
                        "type": "标题匹配",
                        "score": match.get("score", 0),
                        "summary": match["summary"],
                        "event_id": match.get("event_id", "unknown")
                    })
            
            # 从摘要匹配结果中收集summary
            for match in search_results.get("summary_matches", []):
                if "summary" in match and match["summary"]:
                    summaries.append({
                        "type": "摘要匹配",
                        "score": match.get("score", 0),
                        "summary": match["summary"],
                        "event_id": match.get("event_id", "unknown")
                    })
            
            # 从帧匹配结果中收集summary（如果有的话）
            for match in search_results.get("frame_matches", []):
                if "summary" in match and match["summary"]:
                    summaries.append({
                        "type": "帧匹配",
                        "score": match.get("score", 0),
                        "summary": match["summary"],
                        "event_id": match.get("event_id", "unknown")
                    })
            
            if not summaries:
                return "暂无搜索结果可供总结。"
            
            # 按相似度分数排序
            summaries.sort(key=lambda x: x.get("score", 0), reverse=True)
            
            # 构建提示词
            prompt = self._build_summary_prompt(summaries, query_text, esn)
            
            # 调用LLM进行总结
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的智能门锁监控分析助手，负责对视频分析结果进行总结。请用简洁、准确的中文回答，重点突出安全相关信息。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            summary_text = response.choices[0].message.content.strip()
            
            self.logger.info(f"Successfully generated summary for query: {query_text}, ESN: {esn}, input summaries: {len(summaries)}")
            return summary_text
            
        except Exception as e:
            self.logger.error(f"Failed to generate summary: {str(e)}")
            # 返回一个基础的总结作为降级方案
            return self._generate_fallback_summary(search_results, query_text)
    
    def _build_summary_prompt(self, summaries: List[Dict[str, Any]], query_text: str, esn: str) -> str:
        """构建总结提示词"""
        
        # 限制总结数量以避免token超限
        max_summaries = 50
        if len(summaries) > max_summaries:
            summaries = summaries[:max_summaries]
        
        prompt = f"""
请对以下智能门锁监控搜索结果进行总结：

**查询条件：**
- 查询内容：{query_text}
- 设备编号：{esn}
- 搜索到的视频数量：{len(summaries)}

**搜索结果详情：**
"""
        
        for i, item in enumerate(summaries, 1):
            prompt += f"""
{i}. 【{item['type']}】(相似度: {item['score']:.3f}, 事件ID: {item['event_id']})
   内容：{item['summary']}
"""
        
        prompt += """

**总结要求：**
1. 只关注实际发生的事件内容，不要提及"标题匹配"、"摘要匹配"、"帧匹配"等技术术语
2. 重点说明：谁来了、什么时间、做了什么事情
3. 安全风险评估：无风险/低风险/中风险/高风险
4. 如果发现可疑行为，请特别说明具体的可疑点
5. 总结长度控制在20-50字以内，语言简洁直白
6. 避免使用"记录"、"数据"、"搜索结果"等技术词汇

请生成用户友好的总结：
"""
        
        return prompt
    
    def _get_type_name(self, match_type: str) -> str:
        """获取匹配类型的中文名称"""
        type_names = {
            "title": "标题匹配",
            "summary": "内容匹配", 
            "frame": "场景匹配"
        }
        return type_names.get(match_type, "未知类型")
    
    def _build_type_specific_prompt(self, matches: List[Dict[str, Any]], match_type: str, query_text: str, esn: str) -> str:
        """构建特定类型的总结提示词"""
        
        # 限制数量以避免token超限
        max_matches = 20
        if len(matches) > max_matches:
            matches = matches[:max_matches]
        
        type_name = self._get_type_name(match_type)
        
        prompt = f"""
请对以下{type_name}搜索结果进行专门总结：

**查询条件：**
- 查询内容：{query_text}
- 设备编号：{esn}
- {type_name}结果数量：{len(matches)}

**{type_name}详情：**
"""
        
        for i, match in enumerate(matches, 1):
            summary = match.get("summary", "")
            score = match.get("score", 0)
            event_id = match.get("event_id", "unknown")
            
            if match_type == "frame":
                timestamp = match.get("timestamp", 0)
                prompt += f"""
{i}. (相似度: {score:.3f}, 事件ID: {event_id}, 时间点: {timestamp}s)
   场景：{summary if summary else "视频帧场景"}
"""
            else:
                title = match.get("title", "")
                prompt += f"""
{i}. (相似度: {score:.3f}, 事件ID: {event_id})
   标题：{title}
   内容：{summary}
"""
        
        prompt += f"""

**总结要求：**
1. 专门针对{type_name}的结果进行总结
2. 重点说明：谁来了、什么时间、做了什么事情
3. 如果发现可疑行为，请特别说明具体的可疑点
4. 总结长度控制在15-30字以内，语言简洁直白，使用口语化表达
5. 避免使用"匹配"、"搜索结果"等技术词汇
6. 对于场景匹配，不要提及时间点，只说场景

请生成{type_name}的专门总结：
"""
        
        return prompt
    
    def _generate_type_fallback_summary(self, matches: List[Dict[str, Any]], match_type: str, query_text: str) -> str:
        """生成特定类型的降级总结"""
        try:
            if not matches:
                return f"未发现{self._get_type_name(match_type)}相关事件。"
            
            # 简单统计风险等级
            risk_counts = {"无风险": 0, "低风险": 0, "中风险": 0, "高风险": 0}
            
            for match in matches:
                summary = match.get("summary", "")
                if "高风险" in summary:
                    risk_counts["高风险"] += 1
                elif "中风险" in summary:
                    risk_counts["中风险"] += 1
                elif "低风险" in summary:
                    risk_counts["低风险"] += 1
                else:
                    risk_counts["无风险"] += 1
            
            # 确定风险等级
            if risk_counts["高风险"] > 0:
                risk_level = "高风险"
            elif risk_counts["中风险"] > 0:
                risk_level = "中风险"
            elif risk_counts["低风险"] > 0:
                risk_level = "低风险"
            else:
                risk_level = "无风险"
            
            type_name = self._get_type_name(match_type)
            return f"发现{len(matches)}个{type_name}事件，风险等级：{risk_level}。"
            
        except Exception as e:
            self.logger.error(f"Failed to generate {match_type} fallback summary: {str(e)}")
            return f"发现{len(matches)}个{self._get_type_name(match_type)}事件。"
    
    def _generate_fallback_summary(self, search_results: Dict[str, Any], query_text: str) -> str:
        """生成降级总结（当LLM调用失败时使用）"""
        try:
            total_matches = search_results.get("total_matches", 0)
            title_count = len(search_results.get("title_matches", []))
            summary_count = len(search_results.get("summary_matches", []))
            frame_count = len(search_results.get("frame_matches", []))
            
            # 简单统计风险等级
            risk_counts = {"无风险": 0, "低风险": 0, "中风险": 0, "高风险": 0}
            
            all_matches = (search_results.get("title_matches", []) + 
                          search_results.get("summary_matches", []) + 
                          search_results.get("frame_matches", []))
            
            for match in all_matches:
                summary = match.get("summary", "")
                if "高风险" in summary:
                    risk_counts["高风险"] += 1
                elif "中风险" in summary:
                    risk_counts["中风险"] += 1
                elif "低风险" in summary:
                    risk_counts["低风险"] += 1
                else:
                    risk_counts["无风险"] += 1
            
            # 确定总体风险等级
            if risk_counts["高风险"] > 0:
                overall_risk = "高风险"
            elif risk_counts["中风险"] > 0:
                overall_risk = "中风险"
            elif risk_counts["低风险"] > 0:
                overall_risk = "低风险"
            else:
                overall_risk = "无风险"
            
            # 生成用户友好的总结
            if total_matches > 0:
                fallback_summary = f"发现{total_matches}个相关事件，总体安全风险：{overall_risk}。"
            else:
                fallback_summary = f"未发现与'{query_text}'相关的事件。"
            
            return fallback_summary.strip()
            
        except Exception as e:
            self.logger.error(f"Failed to generate fallback summary: {str(e)}")
            total = search_results.get('total_matches', 0)
            if total > 0:
                return f"发现{total}个相关事件。"
            else:
                return f"未发现与'{query_text}'相关的事件。"
    
    async def test_connection(self) -> bool:
        """测试LLM服务连接"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": "请回复'连接测试成功'"
                    }
                ],
                max_tokens=20,
                temperature=0
            )
            
            result = response.choices[0].message.content.strip()
            self.logger.info(f"LLM connection test result: {result}")
            return "连接测试成功" in result or "成功" in result
            
        except Exception as e:
            self.logger.error(f"LLM connection test failed: {str(e)}")
            return False 
