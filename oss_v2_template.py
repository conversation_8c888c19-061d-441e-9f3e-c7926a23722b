import ray
from ray import serve
from ray.util import <PERSON><PERSON><PERSON>
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
import logging
import logging.handlers
import os
from pathlib import Path
import yaml
import time
import json
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, field_validator
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from .concat_actor import ConcatActor
from .redis_actor import RedisActor  # 只导入RedisActor
from .utils import init_ray
import pytz
import uuid
import asyncio
import traceback
import alibabacloud_oss_v2 as oss # 阿里云新V2 OSS SDK
import math
import gc
import aiohttp
import tempfile
import redis.asyncio as redis
import socket
import ffmpeg
import glob
from ray.util.queue import Queue
from starlette.requests import Request
from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED, EVENT_JOB_MISSED
from typing_extensions import Annotated


# 配置日志
def setup_logging():
    """配置日志处理"""
    # 创建日志目录
    log_dir = "/var/log/concat_service"
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir, exist_ok=True)
        except Exception as e:
            print(f"无法创建日志目录 {log_dir}，尝试使用临时目录: {e}")
            import tempfile
            log_dir = os.path.join(tempfile.gettempdir(), "concat_service")
            os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名（包含日期）
    shanghai_tz = pytz.timezone('Asia/Shanghai')
    current_date = datetime.now(shanghai_tz).strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"concat_service_{current_date}.log")
    
    # 创建自定义的formatter，添加时区信息
    class ShangHaiTimeFormatter(logging.Formatter):
        def converter(self, timestamp):
            dt = datetime.fromtimestamp(timestamp)
            shanghai_tz = pytz.timezone('Asia/Shanghai')
            return dt.astimezone(shanghai_tz)
            
        def formatTime(self, record, datefmt=None):
            dt = self.converter(record.created)
            if datefmt:
                return dt.strftime(datefmt)
            return dt.strftime("%Y-%m-%d %H:%M:%S %z")
    
    # 使用自定义的formatter
    formatter = ShangHaiTimeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # 配置文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # 配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # 配置特定模块的日志级别
    logging.getLogger('aiokafka').setLevel(logging.INFO)
    logging.getLogger('kafka').setLevel(logging.INFO)
    logging.getLogger('ray').setLevel(logging.INFO)
    logging.getLogger('apscheduler').setLevel(logging.INFO)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.INFO)
    
    # 创建应用专用的日志记录器
    logger = logging.getLogger('concat_service')
    logger.setLevel(logging.INFO)
    
    # 记录初始化信息
    logger.info(f"日志配置完成，日志文件路径: {log_file}")
    logger.info(f"当前时区: {shanghai_tz.zone}")
    
    return logger

logger = setup_logging()
logger.info("Starting Concat Video Service...")

# 设置Ray日志去重
os.environ['RAY_DEDUP_LOGS'] = '0'

# 创建 FastAPI 应用
app = FastAPI()

# 添加请求模型
class TimeRangeRequest(BaseModel):
    start_time: str  # 格式: "YYYY-MM-DD-HH:MM:SS"
    end_time: str    # 格式: "YYYY-MM-DD-HH:MM:SS"
    
class ConcatRequest(BaseModel):
    date: Optional[str] = None
    esn: Optional[str] = None

    @field_validator('date')
    @classmethod
    def validate_date(cls, v: Optional[str]) -> Optional[str]:
        if v is None:
            return v
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError("日期格式必须是'YYYY-MM-DD'")

    class Config:
        json_schema_extra = {
            "example": {
                "date": "2023-05-01",
                "esn": "00TEST123456789"
            }
        }

class DailyRequest(BaseModel):
    date: Optional[str] = None

    @field_validator('date')
    @classmethod
    def validate_date(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('Invalid date format. Use YYYY-MM-DD')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "date": "2025-01-13"
            }
        }

class ConcatTimerangeRequest(BaseModel):
    start_time: str  # 格式: "HH:MM:SS"
    end_time: str    # 格式: "HH:MM:SS"
    date: Optional[str] = None

    @field_validator('date')
    @classmethod
    def validate_date(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            try:
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('Invalid date format. Use YYYY-MM-DD')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "start_time": "00:00:00",
                "end_time": "23:59:59",
                "date": "2025-01-13"
            }
        }

class BatchGenerateTestVideosRequest(BaseModel):
    esn_prefix: str  # ESN前缀，比如 "TEST"
    date: str  # 日期，格式 YYYY-MM-DD
    video_count: int  # 每个ESN生成的视频数量
    esn_count: int = 5  # 生成的ESN数量，默认5个
    video_duration: int = 10  # 每个视频的时长（秒），默认10秒
    
    @field_validator('date')
    @classmethod
    def validate_date(cls, v: str) -> str:
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('Invalid date format. Use YYYY-MM-DD')
    
    @field_validator('video_count')
    @classmethod
    def validate_video_count(cls, v: int) -> int:
        if v <= 0 or v > 100:
            raise ValueError('Video count must be between 1 and 100')
        return v
    
    @field_validator('esn_count')
    @classmethod
    def validate_esn_count(cls, v: int) -> int:
        if v <= 0 or v > 50:
            raise ValueError('ESN count must be between 1 and 50')
        return v
    
    @field_validator('video_duration')
    @classmethod
    def validate_video_duration(cls, v: int) -> int:
        if v <= 0 or v > 300:
            raise ValueError('Video duration must be between 1 and 300 seconds')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "esn_prefix": "TEST",
                "date": "2025-01-13",
                "video_count": 10,
                "esn_count": 5,
                "video_duration": 15
            }
        }

@serve.deployment
@serve.ingress(app)
class ConcatVideoService:
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化拼接视频服务"""
        self.config_path = self.get_config_path() if config is None else config
        self.config = self.load_config(self.config_path)
        init_ray(self.config['Ray'])  # 初始化 Ray

        # 初始化scheduler属性
        self.scheduler = AsyncIOScheduler()
        self.health_status = {"status": "initializing", "last_check": time.time()}
        self.last_health_check = time.time()

        self.shanghai_tz = pytz.timezone('Asia/Shanghai')
        self._setup_actor_pool()
        
        # 设置Redis分布式队列
        self.redis_queue_key = "concat:task_queue"  # Redis中的全局队列键名
        self.redis_instances_key = "concat:active_instances"  # Redis中存储活跃实例的键
        
        # 生成实例ID - 基于主机名生成稳定ID，确保重启后一致
        # 使用主机名作为基础，确保同一台机器重启后使用相同的ID
        hostname = socket.gethostname()
        
        # 检查配置中是否指定了实例ID前缀或固定ID
        instance_id_prefix = self.config.get('instance', {}).get('id_prefix', 'concat')
        fixed_instance_id = self.config.get('instance', {}).get('fixed_id')
        
        if fixed_instance_id:
            # 如果配置了固定ID，直接使用它
            self.instance_id = fixed_instance_id
            logger.info(f"使用配置的固定实例ID: {self.instance_id}")
        else:
            # 否则基于主机名生成稳定ID
            # 对主机名进行哈希以获得一个简短且稳定的ID
            import hashlib
            host_hash = hashlib.md5(hostname.encode()).hexdigest()[:8]
            self.instance_id = f"{instance_id_prefix}:{hostname}:{host_hash}"
            logger.info(f"基于主机名生成稳定实例ID: {self.instance_id}")
        
        # 设置连接Actor池
        self._setup_connections() 
        
        # 设置指标收集
        self._setup_metrics()

        # 设置定时任务
        self._setup_scheduler()
        
        # 初始化 FastAPI 生命周期管理
        self._serve_asgi_lifespan = app.router.lifespan_context
        
        # 启动实例心跳任务
        self.heartbeat_task = asyncio.create_task(self._instance_heartbeat())
        logger.info(f"实例心跳任务已启动，实例ID: {self.instance_id}")
        
        # 启动指标上报任务
        self.metrics_task = asyncio.create_task(self.report_metrics_for_scaling())
        logger.info("指标上报任务已启动")
        
        # 启动锁监控任务
        self.lock_monitor_task = asyncio.create_task(self._monitor_scheduler_locks())
        logger.info("锁监控任务已启动")
        
        # 启动清理任务和任务处理循环
        self.cleanup_task = asyncio.create_task(self._startup_cleanup())
        logger.info("启动清理任务已启动")
        
        # 启动任务监控器
        self.monitor_task = asyncio.create_task(self._monitor_stuck_tasks())
        logger.info("任务监控器已启动")

        # 启动超时任务监控器
        self.timeout_monitor_task = asyncio.create_task(self._monitor_timeout_tasks())
        logger.info("超时任务监控器已启动")

        # 启动实例心跳
        self.heartbeat_task = asyncio.create_task(self._instance_heartbeat())
        logger.info("实例心跳已启动")

        # 注册优雅关闭处理器（Ray Serve兼容版本）
        self._setup_graceful_shutdown()
        
        # 启动任务处理循环
        self.processor_task = asyncio.create_task(self._process_from_queue())
        logger.info("全局队列处理器已启动")

        # 设置关闭标志
        self.shutdown_flag = False

        # 记录初始化完成
        logger.info(f"Concat视频服务初始化完成，配置路径: {self.config_path}")
        self.status_update_interval = 60  # 状态更新间隔时间（秒）
        self.last_status_update = time.time()

        # 根据 num_actors 设置并发限制
        num_actors = self.config['Ray'].get('num_actors', 5)  # 默认为5
        self.max_concurrency = self.config['Ray'].get('max_concurrency', num_actors * 2)
        logger.info(f"设置最大并发任务数为: {self.max_concurrency} (基于 {num_actors} 个 ConcatActor)")

        # 添加OSS桶初始化 (使用新的V2 SDK)
        credentials_provider = oss.credentials.StaticCredentialsProvider(
            access_key_id=self.config['oss']['alibaba_cloud_access_key_id'],
            access_key_secret=self.config['oss']['alibaba_cloud_access_key_secret']
        )

        cfg = oss.config.load_default()
        cfg.credentials_provider = credentials_provider
        cfg.region = self.config['oss']['region']
        cfg.endpoint = self.config['oss']['endpoint']

        self.oss_client = oss.Client(cfg)
        self.bucket_name = self.config['oss']['bucket']



    def _setup_connections(self):
        """设置连接管理器"""
        try:
            # 创建Redis专用连接Actor池
            num_redis_actors = self.config['Ray'].get('num_redis_actors', 2)  # 默认创建2个Redis actor
            redis_actors_pool = [
                RedisActor.options(
                    name=f"ConcatRedisActor_{i}",
                    namespace="concat_service"
                ).remote(self.config) 
                for i in range(num_redis_actors)
            ]
            self.redis_actors_pool = ActorPool(redis_actors_pool)
            
            # 启动Redis连接
            list(self.redis_actors_pool.map(
                lambda actor, value: actor.start.remote(), 
                [None] * num_redis_actors
            ))
            
            logger.info(f"已创建{num_redis_actors}个Redis连接管理器实例")
        except Exception as e:
            logger.error(f"设置连接管理器时出错: {e}")
            raise

    async def get_conn_actor(self, max_retries=10, retry_interval=2, backoff_factor=2, actor_type="redis"):
        """获取连接管理器Actor (支持Redis和Concat类型)
        
        Args:
            max_retries: 最大重试次数，默认10次
            retry_interval: 初始重试间隔时间(秒)，默认2秒
            backoff_factor: 退避因子，每次重试后等待时间会乘以这个因子，默认2
            actor_type: Actor类型，支持"redis"或"concat"
            
        Returns:
            对应类型的Actor实例
            
        Raises:
            TimeoutError: 当超过最大重试次数仍无法获取空闲Actor时抛出
        """
        # 根据类型选择对应的Actor池
        if actor_type == "concat":
            actor_pool = self.actor_pool
        elif actor_type == "redis":
            actor_pool = self.redis_actors_pool
        else:
            raise ValueError(f"不支持的Actor类型: {actor_type}")
        
        # 尝试获取空闲的Actor
        current_interval = retry_interval  # 初始等待时间
        
        for attempt in range(max_retries):
            conn_actor = actor_pool.pop_idle()  # 从连接池中获取空闲Actor
            
            if conn_actor is not None:
                # 找到空闲Actor，直接返回
                return conn_actor
            
            # 如果所有Actor都繁忙，记录日志并等待
            logger.info(f"所有{actor_type}类型Actor都繁忙，等待重试 ({attempt+1}/{max_retries})，等待时间: {current_interval}秒")
            await asyncio.sleep(current_interval)  # 异步等待一段时间后重试
            
            # 使用指数退避增加等待时间
            current_interval = current_interval * backoff_factor
        
        # 超过最大重试次数仍无法获取空闲Actor
        error_msg = f"无法获取空闲的{actor_type}类型Actor，已重试{max_retries}次"
        logger.error(error_msg)
        raise TimeoutError(error_msg)
    
    async def get_redis_actor(self, max_retries=10, retry_interval=2, backoff_factor=2):
        """获取Redis连接管理器Actor (便捷方法)
        
        Returns:
            Redis连接管理器Actor实例
        """
        return await self.get_conn_actor(max_retries, retry_interval, backoff_factor)
    
    async def push_conn_actor(self, conn_actor, actor_type="redis"):
        """将连接管理器Actor推回连接池 (支持Redis和Concat类型)
        
        Args:
            conn_actor: 要推回的Actor实例
            actor_type: Actor类型，支持"redis"或"concat"
        """
        try:
            # 根据类型选择对应的Actor池
            if actor_type == "concat":
                self.actor_pool.push(conn_actor)
            elif actor_type == "redis":
                self.redis_actors_pool.push(conn_actor)
            else:
                raise ValueError(f"不支持的Actor类型: {actor_type}")
        except Exception as e:
            logger.error(f"推回{actor_type}类型Actor时出错: {e}")
            raise

    # 简化的队列操作方法
    async def _redis_queue_size(self):
        """获取全局队列大小"""
        conn_actor = await self.get_redis_actor()
        try:
            size = await conn_actor.redis_llen.remote(self.redis_queue_key)
            return size
        finally:
            await self.push_conn_actor(conn_actor)
    
    async def _redis_queue_empty(self):
        """检查全局队列是否为空"""
        size = await self._redis_queue_size()
        return size == 0
    
    async def _redis_queue_push(self, message):
        """
        将任务消息推入Redis队列，并使用原子操作确保任务的唯一性。
        这可以避免重复任务导致队列长度指标失真，从而保证弹性扩缩容的准确性。
        此逻辑对所有通过此函数入队的任务（包括 daily_concat 和 progressive_merge）都生效。
        """
        if not isinstance(message, dict) or "task_id" not in message:
            logger.error(f"无效的消息格式，必须是包含'task_id'的字典: {message}")
            return

        task_id = message.get("task_id")
        conn_actor = await self.get_redis_actor()
        if not conn_actor:
            logger.error("无法获取Redis Actor，无法推送任务到队列")
            return

        lock_acquired = False
        try:
            # 关键：原子性操作
            processing_tasks_key = "video:processing_tasks"
            # 使用不同的锁键名避免与Actor处理锁冲突
            task_lock_key = f"video:queue_locks:{task_id}"

            # 1. 使用 SETNX 原子性地检查并设置锁。如果任务已存在，此操作会失败。
            # 锁的有效期为2小时（7200秒），防止任务失败后锁一直存在。
            lock_acquired = await conn_actor.redis_setnx.remote(
                task_lock_key, self.instance_id, 7200
            )

            if not lock_acquired:
                logger.info(f"任务 {task_id} 已在处理或排队中，跳过重复添加。")
                return

            # 2. 成功获取锁后，将任务推入队列，并将任务ID添加到状态集合中。
            message_json = json.dumps(message)
            await conn_actor.redis_lpush.remote(self.redis_queue_key, message_json)
            await conn_actor.redis_sadd.remote(processing_tasks_key, task_id, 7200)

            logger.info(f"任务 {task_id} 已成功推入队列。")
            
            queue_size = await conn_actor.redis_llen.remote(self.redis_queue_key)
            logger.info(f"当前任务队列大小: {queue_size}")

        except Exception as e:
            logger.error(f"将消息推入Redis队列时出错: {e}")
            if lock_acquired:
                try:
                    # 发生错误时，尝试回滚锁和状态
                    task_lock_key = f"video:queue_locks:{task_id}"
                    await conn_actor.redis_delete.remote(task_lock_key)
                    await conn_actor.redis_srem.remote("video:processing_tasks", task_id)
                    logger.info(f"因发生错误，已回滚任务 {task_id} 的锁。")
                except Exception as rollback_e:
                    logger.error(f"回滚任务 {task_id} 的锁失败: {rollback_e}")
        finally:
            if conn_actor:
                await self.push_conn_actor(conn_actor)
    
    async def _redis_queue_pop(self):
        """从全局队列获取消息，如果队列为空则返回None"""
        conn_actor = await self.get_redis_actor()
        try:
            # 使用RPOP从全局队列右侧(尾部)取出消息，保证原子性
            message_json = await conn_actor.redis_rpop.remote(self.redis_queue_key)
            if message_json:
                # 将JSON字符串反序列化为字典
                return json.loads(message_json)
            return None
        finally:
            await self.push_conn_actor(conn_actor)

    async def _instance_heartbeat(self):
        """维护实例心跳，定期更新实例存活状态"""
        await asyncio.sleep(5)  # 等待系统启动稳定
        logger.info(f"开始实例心跳任务: {self.instance_id}")
        
        while True:
            try:
                conn_actor = await self.get_redis_actor()
                
                # 获取当前实例列表
                active_instances = await conn_actor.redis_smembers.remote(self.redis_instances_key)
                
                # 向Redis注册当前实例
                await conn_actor.redis_sadd.remote(
                    self.redis_instances_key,  # 活跃实例集合键
                    self.instance_id,  # 实例ID
                    300  # 设置5分钟过期时间，实例心跳每分钟更新一次
                )
                
                # 清理僵尸实例 - 检查所有实例的最后心跳时间
                await self._cleanup_zombie_instances(conn_actor, active_instances)
                
                # 获取当前活跃实例数量和配置的actor数量
                current_count = len(active_instances) + (0 if self.instance_id in active_instances else 1)
                config_actors = self.config['Ray'].get('num_actors', 2)
                
                # 定期记录日志，但不要太频繁
                if time.time() - self.last_status_update >= self.status_update_interval:
                    logger.info(f"集群状态: {current_count}个活跃服务实例, 当前实例ID: {self.instance_id}, 每实例{config_actors}个工作者(Actor)")
                    self.last_status_update = time.time()
                
                # 更新实例状态信息
                instance_status_key = f"concat:instance:{self.instance_id}:status"
                status_data = {
                    "instance_id": self.instance_id,
                    "last_heartbeat": time.time(),
                    "active_tasks": len(self.processing_tasks) if hasattr(self, 'processing_tasks') else 0,
                    "processing_times": self.processing_times[-5:] if hasattr(self, 'processing_times') and self.processing_times else [],
                    "avg_processing_time": (sum(self.processing_times) / len(self.processing_times)) 
                                        if hasattr(self, 'processing_times') and self.processing_times else 0,
                    "num_actors": config_actors  # 添加Actor数量到状态信息中
                }
                await conn_actor.redis_set.remote(
                    instance_status_key,
                    json.dumps(status_data),
                    300  # 5分钟过期
                )
                
                # 每分钟心跳一次
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"更新实例心跳时出错: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(10)  # 出错时等待较短时间再重试
            finally:
                await self.push_conn_actor(conn_actor)

    async def _cleanup_zombie_instances(self, conn_actor, active_instances):
        """清理僵尸实例，移除长时间未发送心跳的实例"""
        try:
            # 获取配置参数
            zombie_timeout = self.config.get('metrics', {}).get('zombie_instance_timeout', 600)  # 默认10分钟
            cleanup_interval = self.config.get('metrics', {}).get('zombie_cleanup_interval', 300)  # 默认5分钟一次
            
            # 检查是否到了清理时间
            current_time = time.time()
            if not hasattr(self, 'last_zombie_cleanup') or (current_time - self.last_zombie_cleanup) >= cleanup_interval:
                logger.info("开始检查僵尸实例...")
                self.last_zombie_cleanup = current_time
                
                zombie_instances = []
                for instance_id in active_instances:
                    # 检查实例状态
                    instance_status_key = f"concat:instance:{instance_id}:status"
                    status_json = await conn_actor.redis_get.remote(instance_status_key)
                    
                    if not status_json:
                        # 没有状态信息的实例被视为僵尸实例
                        logger.warning(f"实例 {instance_id} 没有状态信息，将被标记为僵尸实例")
                        zombie_instances.append(instance_id)
                        continue
                    
                    try:
                        status = json.loads(status_json)
                        last_heartbeat = status.get('last_heartbeat', 0)
                        
                        # 如果最后心跳时间超过超时时间，将实例标记为僵尸实例
                        if (current_time - last_heartbeat) > zombie_timeout:
                            logger.warning(f"实例 {instance_id} 的最后心跳时间是 {datetime.fromtimestamp(last_heartbeat).strftime('%Y-%m-%d %H:%M:%S')}，"
                                        f"超过 {zombie_timeout} 秒，将被标记为僵尸实例")
                            zombie_instances.append(instance_id)
                    except Exception as e:
                        logger.error(f"解析实例 {instance_id} 的状态信息时出错: {e}")
                        zombie_instances.append(instance_id)
                
                # 移除僵尸实例
                if zombie_instances:
                    logger.warning(f"发现 {len(zombie_instances)} 个僵尸实例，准备清理: {zombie_instances}")
                    for instance_id in zombie_instances:
                        # 从活跃实例集合中移除
                        await conn_actor.redis_srem.remote(self.redis_instances_key, instance_id)
                        
                        # 删除实例状态信息 - 使用redis_expire设置1秒过期时间
                        instance_status_key = f"concat:instance:{instance_id}:status"
                        await conn_actor.redis_expire.remote(instance_status_key, 1)
                        
                        logger.info(f"已清理僵尸实例: {instance_id}")
                    
                    # 记录清理结果
                    logger.info(f"僵尸实例清理完成，共清理 {len(zombie_instances)} 个实例")
                else:
                    logger.info("没有发现僵尸实例")
        except Exception as e:
            logger.error(f"清理僵尸实例时出错: {e}")
            logger.error(traceback.format_exc())

    async def _get_active_instance_count(self):
        """获取当前活跃的实例数量"""
        try:
            conn_actor = await self.get_redis_actor()
            active_instances = await conn_actor.redis_smembers.remote(self.redis_instances_key)
            
            instance_count = len(active_instances)
            # 确保至少返回1，避免除以零错误
            return max(instance_count, 1)
        except Exception as e:
            logger.error(f"获取活跃实例数时出错: {e}")
            # 出错时返回保守的配置值
            return self.config['Ray'].get('num_actors', 2)
        finally:
            await self.push_conn_actor(conn_actor)

    async def _total_queue_size(self):
        """获取全局队列的大小，用于指标上报"""
        # 直接返回全局队列大小，因为现在只有一个队列
        return await self._redis_queue_size()

    async def report_metrics_for_scaling(self):
        """上报用于自动伸缩的自定义指标"""
        await asyncio.sleep(30)  # 等待系统启动稳定
        logger.info("开始上报自动扩缩容指标")
        
        # 从配置中获取指标相关参数
        metrics_config = self.config.get('metrics', {})
        report_interval = metrics_config.get('report_interval', 60)  # 默认每60秒上报一次
        logging_interval = metrics_config.get('logging_interval', 300)  # 默认每5分钟记录一次详细日志
        history_size = metrics_config.get('history_size', 100)  # 历史记录大小
        default_avg_time = metrics_config.get('default_avg_time', 60.0)  # 默认平均处理时间
        endpoint_url = metrics_config.get('endpoint_url', None)  # 指标上报的API端点
        
        # 确保处理时间历史记录大小符合配置
        if hasattr(self, 'processing_times') and len(self.processing_times) > history_size:
            self.processing_times = self.processing_times[-history_size:]
        
        logger.info(f"指标配置: 上报间隔={report_interval}秒, 日志间隔={logging_interval}秒, 历史大小={history_size}")
        
        while True:
            try:
                # 1. 获取全局队列大小
                queue_size = await self._redis_queue_size()
                
                # 2. 计算平均处理时间 (秒)
                if not hasattr(self, 'processing_times') or not self.processing_times:
                    # 没有处理记录时使用配置中的默认值
                    avg_time = default_avg_time
                    logger.debug(f"没有处理时间记录，使用默认值 {avg_time} 秒")
                else:
                    avg_time = sum(self.processing_times) / len(self.processing_times)
                
                # 3. 获取当前上海时间
                now = datetime.now(self.shanghai_tz)
                
                # 4. 计算距离今天午夜的秒数（用于合并最终视频）
                midnight = now.replace(hour=23, minute=0, second=0) # 23:00:00　隔一个小时，让系统有足够时间处理并且能让弹性更好的发挥
                time_until_midnight = (midnight - now).total_seconds()
                
                # 4.1 获取渐进式合并的剩余时间（用于渐进式合并）
                progressive_interval_minutes = self.config['schedule']['progressive_merge']['interval_minutes']
                
                # 获取下一次渐进式合并的时间
                next_progressive_merge_time = None
                if hasattr(self, 'scheduler'):
                    try:
                        progressive_job = self.scheduler.get_job('progressive_merge')
                        if progressive_job and progressive_job.next_run_time:
                            next_progressive_merge_time = progressive_job.next_run_time
                    except Exception as e:
                        logger.error(f"获取渐进式合并任务下次执行时间出错: {e}")
                
                # 计算距离下次渐进式合并的秒数
                if next_progressive_merge_time:
                    time_until_progressive_merge = (next_progressive_merge_time - now).total_seconds()
                else:
                    # 如果无法获取下次执行时间，使用配置的间隔时间
                    time_until_progressive_merge = progressive_interval_minutes * 60
                
                logger.debug(f"距离午夜: {time_until_midnight:.2f}秒, 距离下次渐进式合并: {time_until_progressive_merge:.2f}秒")
                
                # 5. 获取活跃实例数量
                instance_count = await self._get_active_instance_count()
                
                # 6. 获取每个实例的处理器数量
                num_processors_per_instance = self.config['Ray'].get('num_actors', 2)
                
                # 7. 计算总处理器数量 (实例数 × 每个实例的处理器数)
                total_processors = instance_count * num_processors_per_instance
                
                # 8. 预估处理完队列中所有任务所需的时间，考虑所有实例的并行处理能力
                estimated_completion = (queue_size * avg_time) / total_processors if total_processors > 0 else 0
                
                # 9. 计算容量比率 (比值小于1表示处理能力不足)
                # 对于渐进式合并任务，使用距离下次合并的时间
                # 对于合并最终视频，使用距离午夜的时间
                progressive_capacity_ratio = time_until_progressive_merge / max(estimated_completion, 1) if estimated_completion > 0 else 10.0
                daily_capacity_ratio = time_until_midnight / max(estimated_completion, 1) if estimated_completion > 0 else 10.0
                
                # 取较小的容量比率作为整体容量比率（更保守的估计）
                capacity_ratio = min(progressive_capacity_ratio, daily_capacity_ratio)
                
                # 10. 确定扩缩容决策值: 容量比率小于0.9时为3(扩容)，大于1.1时为1(缩容)，介于两者之间为2(保持不变)
                if capacity_ratio < 0.9:
                    scaling_decision = 3  # 扩容
                elif capacity_ratio > 1.1:
                    scaling_decision = 1  # 缩容
                else:
                    scaling_decision = 2  # 保持不变
                
                # 10. 计算其他自定义指标
                custom_metrics = {}
                for metric_name, metric_formula in metrics_config.get('custom_metrics', {}).items():
                    try:
                        # 使用eval安全地计算自定义指标公式
                        # 为公式提供本地变量和数学函数
                        vars_dict = {
                            'queue_size': queue_size,
                            'avg_time': avg_time,
                            'instance_count': instance_count,
                            'total_processors': total_processors,
                            'estimated_completion': estimated_completion,
                            'capacity_ratio': capacity_ratio,
                            'time_until_midnight': time_until_midnight,
                            'time_until_progressive_merge': time_until_progressive_merge,
                            'progressive_capacity_ratio': progressive_capacity_ratio,
                            'daily_capacity_ratio': daily_capacity_ratio,
                            # 添加必要的数学函数
                            'min': min,
                            'max': max,
                            'abs': abs,
                            'round': round,
                            'pow': pow,
                            'sum': sum
                        }
                        # 使用受限的全局命名空间，只包含必要的数学函数
                        custom_metrics[metric_name] = eval(metric_formula, {"__builtins__": {}}, vars_dict)
                        logger.debug(f"计算自定义指标 {metric_name}: {metric_formula} = {custom_metrics[metric_name]}")
                    except Exception as e:
                        logger.error(f"计算自定义指标 {metric_name} 时出错: {e}")
                        custom_metrics[metric_name] = 0
                
                # 准备上报的指标
                metrics_data = [
                    {"name": "queue_size", "value": queue_size},
                    {"name": "avg_processing_time", "value": avg_time},
                    {"name": "time_until_midnight", "value": time_until_midnight},
                    {"name": "time_until_progressive_merge", "value": time_until_progressive_merge},
                    {"name": "estimated_completion", "value": estimated_completion},
                    {"name": "capacity_ratio", "value": capacity_ratio},
                    {"name": "progressive_capacity_ratio", "value": progressive_capacity_ratio},
                    {"name": "daily_capacity_ratio", "value": daily_capacity_ratio},
                    {"name": "active_instances", "value": instance_count},
                    {"name": "total_processors", "value": total_processors},
                    {"name": "scaling_decision", "value": scaling_decision}  # 新增扩缩容决策指标
                ]
                
                # 添加自定义指标
                for name, value in custom_metrics.items():
                    metrics_data.append({"name": name, "value": value})
                
                # 记录定期日志
                if time.time() - self.last_status_update >= logging_interval:
                    scaling_text = "1=缩容" if scaling_decision < 2 else ("2=保持不变" if scaling_decision == 2 else "3=扩容")
                    metrics_log = (
                        f"扩缩容指标 - 队列大小: {queue_size}, 平均处理时间: {avg_time:.2f}秒, "
                        f"预估完成时间: {estimated_completion:.2f}秒, 距离午夜: {time_until_midnight:.2f}秒, "
                        f"容量比率: {capacity_ratio:.2f}, 活跃实例数: {instance_count}, "
                        f"扩缩容决策: {scaling_decision} ({scaling_text})"
                    )
                    
                    # 添加自定义指标到日志
                    if custom_metrics:
                        metrics_log += ", 自定义指标: " + ", ".join([f"{k}={v:.2f}" for k, v in custom_metrics.items()])
                    
                    logger.info(metrics_log)
                    self.last_status_update = time.time()
                
                # 如果配置了上报端点，发送指标
                if endpoint_url:
                    try:
                        async with aiohttp.ClientSession() as session:
                            async with session.post(
                                endpoint_url,
                                json=metrics_data,
                                timeout=5
                            ) as response:
                                if response.status != 200:
                                    logger.error(f"上报指标失败: {await response.text()}")
                                else:
                                    logger.debug(f"成功上报 {len(metrics_data)} 个指标")
                    except Exception as e:
                        logger.error(f"调用指标API出错: {str(e)}")
                
                # 存储指标到Redis，便于其他组件查询
                if metrics_config.get('store_in_redis', False):
                    try:
                        conn_actor = await self.get_redis_actor()
                        
                        # 设置指标键，包含时间戳以便追踪历史
                        metrics_key = f"concat:metrics:{int(time.time())}"
                        
                        # 存储当前指标
                        await conn_actor.redis_set.remote(
                            metrics_key,
                            json.dumps(metrics_data),
                            metrics_config.get('redis_metrics_ttl', 86400)  # 默认1天过期
                        )
                        
                        # 存储最新指标的快照，便于快速访问
                        await conn_actor.redis_set.remote(
                            "concat:metrics:latest",
                            json.dumps({
                                "timestamp": time.time(),
                                "data": metrics_data
                            }),
                            metrics_config.get('redis_metrics_ttl', 86400)
                        )
                        
                    except Exception as e:
                        logger.error(f"存储指标到Redis时出错: {e}")
                    finally:
                        await self.push_conn_actor(conn_actor)
                
                # 等待一段时间再次上报
                await asyncio.sleep(report_interval)
            
            except Exception as e:
                logger.error(f"上报扩缩容指标时出错: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(10)

    def _setup_metrics(self):
        """设置指标收集"""
        # 从配置中获取指标配置
        metrics_config = self.config.get('metrics', {})
        
        # 初始化请求计数器
        self.request_counter = 0
        
        # 初始化处理时间列表，限制大小
        history_size = metrics_config.get('history_size', 30)
        self.processing_times = []
        
        # 初始化最后指标更新时间
        self.last_metrics_time = time.time()
        
        # 初始化状态更新时间
        self.last_status_update = time.time()
        
        # 为系统资源监控添加额外的指标
        self.system_metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': []
        }
        
        # 如果配置中启用了系统资源监控，创建监控任务
        if metrics_config.get('monitor_system_resources', False):
            self.system_monitor_task = asyncio.create_task(self._monitor_system_resources())
            logger.info("系统资源监控任务已启动")
            
        logger.info(f"指标收集已设置，历史大小: {history_size}")
    
    async def _monitor_system_resources(self):
        """监控系统资源使用情况"""
        # 使用psutil获取系统资源使用情况
        try:
            import psutil
            metrics_config = self.config.get('metrics', {})
            interval = metrics_config.get('system_monitor_interval', 60)  # 默认每分钟
            history_size = metrics_config.get('system_metrics_history', 10)  # 保留10个历史记录
            
            logger.info(f"系统资源监控已启动，间隔: {interval}秒")
            
            while True:
                try:
                    # 获取CPU使用率
                    cpu_percent = psutil.cpu_percent(interval=1)
                    
                    # 获取内存使用率
                    memory = psutil.virtual_memory()
                    memory_percent = memory.percent
                    
                    # 获取磁盘使用率
                    disk = psutil.disk_usage('/')
                    disk_percent = disk.percent
                    
                    # 更新指标
                    self.system_metrics['cpu_usage'].append(cpu_percent)
                    self.system_metrics['memory_usage'].append(memory_percent)
                    self.system_metrics['disk_usage'].append(disk_percent)
                    
                    # 限制历史记录大小
                    for key in self.system_metrics:
                        if len(self.system_metrics[key]) > history_size:
                            self.system_metrics[key] = self.system_metrics[key][-history_size:]
                    
                    # 定期记录系统资源使用情况
                    if time.time() - self.last_metrics_time >= 300:  # 每5分钟
                        logger.info(
                            f"系统资源使用情况 - CPU: {cpu_percent}%, "
                            f"内存: {memory_percent}%, "
                            f"磁盘: {disk_percent}%"
                        )
                    
                    # 如果资源使用超过阈值，记录警告
                    cpu_threshold = metrics_config.get('cpu_warning_threshold', 90)
                    memory_threshold = metrics_config.get('memory_warning_threshold', 90)
                    disk_threshold = metrics_config.get('disk_warning_threshold', 90)
                    
                    if cpu_percent > cpu_threshold:
                        logger.warning(f"CPU使用率高: {cpu_percent}%")
                    if memory_percent > memory_threshold:
                        logger.warning(f"内存使用率高: {memory_percent}%")
                    if disk_percent > disk_threshold:
                        logger.warning(f"磁盘使用率高: {disk_percent}%")
                    
                    await asyncio.sleep(interval)
                except Exception as e:
                    logger.error(f"监控系统资源时出错: {e}")
                    await asyncio.sleep(interval)
        except ImportError:
            logger.warning("无法导入psutil模块，系统资源监控已禁用")
        except Exception as e:
            logger.error(f"系统资源监控任务出错: {e}")
            logger.error(traceback.format_exc())
    async def _process_from_queue(self):
        """从全局队列中消费任务并分发给ConcatActor"""
        logger.info("开始从全局队列消费任务...")
        
        active_tasks = {}  # 用于跟踪正在处理的任务 (future -> metadata)
        
        while True:
            try:
                # --------------------- 并发限制逻辑 ---------------------
                # 检查当前活跃的任务数，如果达到或超过了设定的最大并发数，则暂停拉取新任务。
                # 这样做可以防止系统因任务过多而过载，实现"背压"机制。
                while len(active_tasks) >= self.max_concurrency:
                    if len(active_tasks) > 0:
                        logger.info(
                            f"达到最大并发数 ({self.max_concurrency})，暂停拉取新任务。"
                            f"当前活跃任务: {len(active_tasks)}。等待1秒..."
                        )
                        # 等待任意一个任务完成
                        done, _ = await asyncio.wait(
                            list(active_tasks.keys()), 
                            return_when=asyncio.FIRST_COMPLETED,
                            timeout=1.0 # 设置一个超时，避免永久阻塞
                        )
                        # 清理已完成的任务
                        for future in done:
                            self._record_processing_time(future, active_tasks.pop(future))
                    else:
                        # 如果没有活跃任务，但仍然满足循环条件（例如max_concurrency=0），则短暂休眠避免死循环
                        await asyncio.sleep(1)
                # ---------------------------------------------------------

                # 从Redis队列中获取任务
                message_json = await self._redis_queue_pop()
                if not message_json:
                    await asyncio.sleep(1)
                    continue
                
                # 记录任务开始时间
                task_metadata = {
                    "start_time": time.time(),
                    "task_id": message_json.get("task_id", "unknown"),
                    "message": message_json
                }
                
                logger.info(f"从Redis队列获取到任务: {task_metadata['task_id']}")
                
                # 创建异步任务处理消息
                task = asyncio.create_task(self._process_task(message_json, task_metadata))
                
                # 添加任务完成回调，记录处理时间
                task.add_done_callback(
                    lambda future, meta=task_metadata: self._record_processing_time(future, meta)
                )
                
                # 将任务添加到正在处理的任务集合
                active_tasks[task] = task_metadata
                
            except Exception as e:
                logger.error(f"队列处理器出错: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(1)

    def _record_processing_time(self, future, metadata):
        """记录任务处理时间"""
        try:
            if not future.cancelled():
                try:
                    # 尝试获取结果，如果有异常会在这里抛出
                    future.result()
                    
                    # 计算处理时间
                    processing_time = time.time() - metadata["start_time"]
                    task_id = metadata["task_id"]
                    
                    # 保存处理时间到历史记录
                    self.processing_times.append(processing_time)
                    
                    # 保持历史记录在限制范围内
                    max_history = 30
                    while len(self.processing_times) > max_history:
                        self.processing_times.pop(0)
                    
                    logger.info(f"任务 {task_id} 处理完成，耗时 {processing_time:.2f} 秒")
                except Exception:
                    # 任务执行中出现异常，不记录处理时间
                    pass
        except Exception as e:
            logger.error(f"记录处理时间时出错: {e}")

    async def _process_task(self, message, metadata):
        """处理单个任务"""
        task_id = message.get("task_id", "unknown")
        conn_actor = None
        
        try:
            # 获取任务类型和重试次数
            task_type = message.get("task_type")
            retry_count = message.get("retry_count", 0)
            max_retries = 3  # 默认最大重试3次
            
            # 检查是否超过最大重试次数
            if retry_count >= max_retries:
                logger.warning(f"任务已达到最大重试次数({max_retries})，不再重试: {message}")
                return {"status": "error", "message": f"任务已达到最大重试次数({max_retries})，不再处理"}
            
            if task_type == "concat_daily":
                # 处理日常合并任务
                date = message.get("date")
                esn = message.get("esn")
                is_catchup = message.get("is_catchup", False)
                logger.info(f"处理日常合并任务: date={date}, esn={esn}, 重试次数={retry_count}, 兜底任务={is_catchup}")

                # 调用适当的方法处理任务
                result = await self._process_concat_daily(date, esn, is_catchup)
                logger.info(f"日常合并任务完成: {result}")
                return result
                
            elif task_type == "concat_timerange":
                # 处理时间范围合并任务
                start_time = message.get("start_time")
                end_time = message.get("end_time")
                date = message.get("date")
                esn = message.get("esn")
                logger.info(f"处理时间范围合并任务: start_time={start_time}, end_time={end_time}, date={date}, esn={esn}, 重试次数={retry_count}")
                
                # 调用适当的方法处理任务
                result = await self._process_concat_timerange(start_time, end_time, date, esn)
                logger.info(f"时间范围合并任务完成: {result}")
                return result
                
            elif task_type == "progressive_merge":
                # 处理渐进式合并任务
                esn = message.get("esn")
                date = message.get("date")
                time_window = message.get("time_window")
                logger.info(f"处理渐进式合并任务: esn={esn}, date={date}, time_window={time_window}, task_id={task_id}, 重试次数={retry_count}")
                
                # 调用适当的方法处理任务
                result = await self._process_progressive_merge(esn, date, time_window)
                logger.info(f"渐进式合并任务完成: {result}")
                return result
            else:
                logger.warning(f"未知任务类型: {task_type}")
                return {"status": "error", "message": f"未知任务类型: {task_type}"}
                
        except Exception as e:
            logger.error(f"处理任务出错: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}
        finally:
            # 任务完成后，清理任务锁和任务集合
            try:
                conn_actor = await self.get_redis_actor()
                processing_tasks_key = "video:processing_tasks"
                task_lock_key = f"video:queue_locks:{task_id}"
                
                # 删除任务锁（原子性锁）
                await conn_actor.redis_delete.remote(task_lock_key)
                
                # 从正在处理的任务集合中移除任务ID
                await conn_actor.redis_srem.remote(processing_tasks_key, task_id)
                
                logger.debug(f"已清理任务锁和集合中的任务ID: {task_id}")
            except Exception as e:
                logger.error(f"清理任务锁和ID时出错: {e}")
            finally:
                if conn_actor:
                    await self.push_conn_actor(conn_actor)

    async def _process_concat_daily(self, date, esn, is_catchup=False):
        """处理日常合并任务的具体实现"""
        try:
            # 从ConcatActor池获取一个任务
            result = None

            # 检查是否有空闲actor
            if not self.actor_pool._idle_actors:
                logger.warning(f"没有空闲Actor可用，将任务重新加入队列: date={date}, esn={esn}, 兜底任务={is_catchup}")
                # 重新将任务加入队列，使用确定性任务ID
                task_id = f"concat_daily:{date}:{esn}:catchup" if is_catchup else f"concat_daily:{date}:{esn}"
                task = {
                    "task_id": task_id,
                    "task_type": "concat_daily",
                    "date": date,
                    "esn": esn,
                    "create_time": time.time(),
                    "retry_count": 0,  # 初始重试计数
                    "is_catchup": is_catchup
                }
                await self._redis_queue_push(task)
                return {"status": "queued", "message": "任务已重新加入队列等待处理"}

            for actor in self.actor_pool._idle_actors:
                try:
                    # 在开始处理任务前，记录任务与实例的绑定关系
                    task_id = f"concat_daily:{date}:{esn}:catchup" if is_catchup else f"concat_daily:{date}:{esn}"
                    conn_actor = await self.get_redis_actor()
                    try:
                        await conn_actor.redis_hset.remote(
                            f"task_instance:{task_id}",
                            "instance_id",
                            self.instance_id,
                            3600  # 1小时过期
                        )
                        await conn_actor.redis_hset.remote(
                            f"task_instance:{task_id}",
                            "start_time",
                            time.time(),
                            3600  # 1小时过期
                        )
                    finally:
                        await self.push_conn_actor(conn_actor)

                    # 为兜底任务传递特殊标识
                    if is_catchup:
                        result = await actor.concat_video_oss_catchup.remote(date=date, esn=esn)
                    else:
                        result = await actor.concat_video_oss.remote(date=date, esn=esn)
                    break
                except Exception as e:
                    logger.error(f"Actor处理失败，尝试下一个: {e}")
                    continue

            if result is None:
                logger.warning(f"所有Actor处理失败，将任务重新加入队列: date={date}, esn={esn}, 兜底任务={is_catchup}")
                # 重新将任务加入队列，增加重试次数，使用确定性任务ID
                task_id = f"concat_daily:{date}:{esn}:catchup" if is_catchup else f"concat_daily:{date}:{esn}"
                task = {
                    "task_id": task_id,
                    "task_type": "concat_daily",
                    "date": date,
                    "esn": esn,
                    "create_time": time.time(),
                    "retry_count": 1,  # 设置重试计数
                    "is_catchup": is_catchup
                }
                await self._redis_queue_push(task)
                return {"status": "queued", "message": "处理失败，任务已重新加入队列"}

            return result
        except Exception as e:
            logger.error(f"处理日常合并任务出错: {e}")
            raise

    async def _process_concat_timerange(self, start_time, end_time, date, esn):
        """处理时间范围合并任务的具体实现"""
        try:
            # 生成确定性任务ID，支持去重
            # 格式: concat_timerange:{date}:{start_time}:{end_time}:{esn}
            task_id = f"concat_timerange:{date}:{start_time}:{end_time}:{esn or 'all'}"
            
            # 检查是否有空闲actor
            if not self.actor_pool._idle_actors:
                logger.warning(f"没有空闲Actor可用，将时间范围任务重新加入队列: start_time={start_time}, end_time={end_time}, date={date}, esn={esn}")
                # 重新将任务加入队列，使用确定性任务ID
                task = {
                    "task_id": task_id,
                    "task_type": "concat_timerange",
                    "start_time": start_time,
                    "end_time": end_time,
                    "date": date,
                    "esn": esn,
                    "create_time": time.time(),
                    "retry_count": 0  # 初始重试计数
                }
                await self._redis_queue_push(task)
                return {"status": "queued", "message": "任务已重新加入队列等待处理"}
            
            # 从ConcatActor池获取一个任务
            result = None
            for actor in self.actor_pool._idle_actors:
                try:
                    result = await actor.concat_video_by_timerange_oss.remote(
                        task_id=task_id,
                        start_time=start_time,
                        end_time=end_time,
                        date=date,
                        esn=esn
                    )
                    break
                except Exception as e:
                    logger.error(f"Actor处理失败，尝试下一个: {e}")
                    continue
                    
            if result is None:
                logger.warning(f"所有Actor处理失败，将时间范围任务重新加入队列: start_time={start_time}, end_time={end_time}, date={date}, esn={esn}")
                # 重新将任务加入队列，增加重试次数，使用确定性任务ID
                task = {
                    "task_id": task_id,
                    "task_type": "concat_timerange",
                    "start_time": start_time,
                    "end_time": end_time,
                    "date": date,
                    "esn": esn,
                    "create_time": time.time(),
                    "retry_count": 1  # 设置重试计数
                }
                await self._redis_queue_push(task)
                return {"status": "queued", "message": "处理失败，任务已重新加入队列"}
                
            return result
        except Exception as e:
            logger.error(f"处理时间范围合并任务出错: {e}")
            raise

    async def _process_progressive_merge(self, esn, date, time_window):
        """处理渐进式合并任务的具体实现"""
        try:
            # 检查是否有空闲actor
            if not self.actor_pool._idle_actors:
                logger.warning(f"没有空闲Actor可用，渐进式合并任务失败: esn={esn}, date={date}, time_window={time_window}")
                return {"status": "error", "message": "没有空闲Actor可用"}
            
            # 从ConcatActor池获取一个actor执行渐进式合并
            result = None
            for actor in self.actor_pool._idle_actors:
                try:
                    result = await actor.progressive_merge_videos.remote(esn)
                    break
                except Exception as e:
                    logger.error(f"Actor处理渐进式合并任务失败，尝试下一个: {e}")
                    continue
                    
            if result is None:
                logger.warning(f"所有Actor处理渐进式合并任务失败: esn={esn}, date={date}, time_window={time_window}")
                return {"status": "error", "message": "所有Actor处理失败"}
                
            return result
        except Exception as e:
            logger.error(f"处理渐进式合并任务出错: {e}")
            raise

    async def _scheduled_concat_daily_videos(self):
        """供调度器调用的内部方法，将任务添加到全局队列"""
        max_retries = 3
        retry_delay = 5  # 初始重试延迟（秒）
        
        try:
            # 默认处理前一天的视频，使用上海时区
            date = (datetime.now(self.shanghai_tz) - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 获取所有需要处理的ESN
            esn_set = set()
            base_prefix = f"{self.config['video']['output_directory']}/"
            
            # 添加重试机制获取ESN列表
            for attempt in range(max_retries):
                try:
                    # 使用 list_objects_v2_paginator 获取所有目录
                    paginator = self.oss_client.list_objects_v2_paginator()
                    all_esns = set()
                    for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                        # 检查page.common_prefixes是否为None
                        if page.common_prefixes is None:
                            logger.debug(f"Empty common_prefixes for prefix: {base_prefix}")
                            continue
                        for prefix_obj in page.common_prefixes:
                            # 从目录名中提取ESN
                            # 假设格式为 "base_prefix/ESN/"
                            esn_prefix = prefix_obj.prefix[len(base_prefix):]
                            esn = esn_prefix.strip('/')
                            if esn:  # 排除空ESN
                                all_esns.add(esn)
                    
                    # 🚀 优化：过滤掉没有指定日期目录或目录为空的ESN
                    logger.info(f"找到 {len(all_esns)} 个ESN目录，开始检查是否有 {date} 的视频文件...")
                    for esn in all_esns:
                        esn_date_prefix = f"{base_prefix}{esn}/{date}/"
                        try:
                            # 快速检查该ESN在指定日期下是否有文件
                            first_check = self.oss_client.list_objects_v2(
                                oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=esn_date_prefix, max_keys=1)
                            )
                            if first_check.contents:
                                esn_set.add(esn)
                                logger.debug(f"✅ ESN {esn} 有 {date} 的文件，加入处理队列")
                            else:
                                logger.debug(f"📂 ESN {esn} 在 {date} 无文件，跳过")
                        except Exception as e:
                            # 如果检查出错，保守起见还是加入队列
                            logger.warning(f"⚠️ 检查 ESN {esn} 时出错: {e}，保守加入队列")
                            esn_set.add(esn)
                    
                    logger.info(f"经过过滤，{len(esn_set)} 个ESN需要处理: {esn_set}")
                    break
                except Exception as e:
                    logger.error(f"获取ESN列表出错 (尝试 {attempt+1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
                    else:
                        raise
            
            if not esn_set:
                logger.warning("没有找到需要处理的ESN")
                return
            
            # 将ESN列表转换为列表并排序
            esn_list = sorted(list(esn_set))
            
            # 为每个ESN创建任务并添加到全局队列
            for esn in esn_list:
                # 生成确定性的任务ID，支持去重
                # 格式: concat_daily:{date}:{esn}
                # 例如: concat_daily:2024-01-15:ESN123456
                task_id = f"concat_daily:{date}:{esn}"
                
                task = {
                    "task_id": task_id,
                    "task_type": "concat_daily",
                    "date": date,
                    "esn": esn,
                    "create_time": time.time()
                }
                
                # 直接添加到全局队列
                await self._redis_queue_push(task)
                logger.info(f"已将日常合并任务添加到全局队列: ESN={esn}, date={date}")
            
            logger.info(f"已将 {len(esn_list)} 个日常合并任务添加到全局队列")
                
        except Exception as e:
            logger.error(f"调度日常合并任务时出错: {e}")
            logger.error(traceback.format_exc())
            
    async def _scheduled_check_and_recover_missed_videos(self):
        """
        一个独立的、延迟执行的兜底调度任务，用于检查并恢复被遗漏的每日合并任务。
        """
        try:
            logger.info("开始执行最终合并任务的兜底检查，确保没有遗漏...")
            
            # 同样，处理前一天的视频
            date = (datetime.now(self.shanghai_tz) - timedelta(days=1)).strftime('%Y-%m-%d')
            base_prefix = f"{self.config['video']['output_directory']}/"

            # 1. 获取所有当天有源视频的ESN (应处理清单)
            esn_set = set()
            paginator = self.oss_client.list_objects_v2_paginator()
            all_esns = set()
            for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                if page.common_prefixes:
                    for prefix_obj in page.common_prefixes:
                        esn = prefix_obj.prefix.split('/')[1]
                        all_esns.add(esn)
            
            for esn in all_esns:
                esn_date_prefix = f"{base_prefix}{esn}/{date}/"
                check_request = oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=esn_date_prefix, max_keys=1)
                response = self.oss_client.list_objects_v2(check_request)
                if response.contents:
                    esn_set.add(esn)

            # 2. 获取所有已成功生成合并视频的ESN (已完成清单)
            esns_successfully_merged = set()
            esns_with_files_only = set()  # 仅有文件但可能任务未完成的ESN

            for esn in all_esns:
                highlight_dir_prefix = f"{base_prefix}{esn}/{date}-highlight/"
                check_request = oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=highlight_dir_prefix, max_keys=1)
                response = self.oss_client.list_objects_v2(check_request)
                if response.contents:
                    esns_with_files_only.add(esn)

                    # 进一步检查Redis中的任务完成状态
                    task_id = f"concat_daily:{date}:{esn}"
                    catchup_task_id = f"concat_daily:{date}:{esn}:catchup"

                    # 检查是否有正在处理的任务
                    conn_actor = await self.get_redis_actor()
                    try:
                        # 检查任务锁
                        lock_key = f"video:processing_tasks:{task_id}"
                        catchup_lock_key = f"video:processing_tasks:{catchup_task_id}"

                        task_locked = await conn_actor.redis_exists.remote(lock_key)
                        catchup_locked = await conn_actor.redis_exists.remote(catchup_lock_key)

                        # 检查任务是否在处理队列中
                        in_processing = await conn_actor.redis_sismember.remote("video:processing_tasks", task_id)
                        catchup_in_processing = await conn_actor.redis_sismember.remote("video:processing_tasks", catchup_task_id)

                        # 如果没有任务在处理中，且有输出文件，则认为已完成
                        if not (task_locked or catchup_locked or in_processing or catchup_in_processing):
                            esns_successfully_merged.add(esn)
                            logger.debug(f"ESN {esn} 已完成：有输出文件且无正在处理的任务")
                        else:
                            logger.debug(f"ESN {esn} 有文件但任务仍在处理中：task_locked={task_locked}, catchup_locked={catchup_locked}, in_processing={in_processing}, catchup_in_processing={catchup_in_processing}")
                    finally:
                        await self.push_conn_actor(conn_actor)

            logger.info(f"兜底检查：找到 {len(esn_set)} 个应处理的ESN，{len(esns_with_files_only)} 个有输出文件的ESN，{len(esns_successfully_merged)} 个确认已完成的ESN。")

            # 3. 找出被遗漏的ESN
            missed_esns = esn_set - esns_successfully_merged

            # 详细记录兜底检查的决策过程
            logger.info(f"兜底检查详细分析:")
            logger.info(f"  - 应处理ESN总数: {len(esn_set)}")
            logger.info(f"  - 有输出文件ESN数: {len(esns_with_files_only)}")
            logger.info(f"  - 确认完成ESN数: {len(esns_successfully_merged)}")
            logger.info(f"  - 被遗漏ESN数: {len(missed_esns)}")

            if len(esns_with_files_only) > len(esns_successfully_merged):
                still_processing = esns_with_files_only - esns_successfully_merged
                logger.info(f"  - 有文件但仍在处理的ESN数: {len(still_processing)}")
                logger.info(f"  - 仍在处理的ESN: {sorted(list(still_processing))}")

            # 4. 为被遗漏的ESN重新创建任务
            if missed_esns:
                logger.warning(f"兜底检查发现 {len(missed_esns)} 个被遗漏的ESN，将为它们重新创建合并任务: {sorted(list(missed_esns))}")

                # 为每个遗漏的ESN记录详细信息
                for esn in sorted(list(missed_esns)):
                    # 检查该ESN的源文件数量
                    esn_date_prefix = f"{base_prefix}{esn}/{date}/"
                    try:
                        check_request = oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=esn_date_prefix, max_keys=100)
                        response = self.oss_client.list_objects_v2(check_request)
                        source_file_count = len(response.contents) if response.contents else 0
                        logger.warning(f"  - ESN {esn}: 发现 {source_file_count} 个源文件需要处理")
                    except Exception as e:
                        logger.error(f"  - ESN {esn}: 检查源文件时出错: {e}")
                        source_file_count = "未知"

                    task_id = f"concat_daily:{date}:{esn}:catchup"

                    task = {
                        "task_id": task_id,
                        "task_type": "concat_daily",
                        "date": date,
                        "esn": esn,
                        "create_time": time.time(),
                        "source": "catchup_safety_net",
                        "is_catchup": True,  # 标识这是兜底任务
                        "retry_count": 0,
                        "source_file_count": source_file_count  # 记录源文件数量用于监控
                    }

                    await self._redis_queue_push(task)
                    logger.warning(f"已为遗漏的ESN {esn} 创建兜底合并任务（源文件数: {source_file_count}），并推入队列。")
            else:
                logger.info("兜底检查完成，没有发现被遗漏的ESN。")

        except Exception as e:
            logger.error(f"执行兜底检查时发生严重错误: {e}")
            logger.error(traceback.format_exc())
            
    @staticmethod
    def get_config_path():
        """获取配置文件路径"""
        deploy_mode = os.getenv("DEPLOY_MODE", "dev")
        logger.info(f"Deploy mode: {deploy_mode}")
        config_name = f"config_{deploy_mode}.yaml"
        return os.path.join(os.path.dirname(__file__), config_name)

    @classmethod
    def load_config(cls, config_path: str) -> Dict:
        """加载配置"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)

    def _setup_actor_pool(self):
        """设置 Actor 池"""
        num_actors = self.config.get('Ray', {}).get('num_actors', 2)
        actors = [ConcatActor.remote(self.config) for _ in range(num_actors)]
        self.actor_pool = ActorPool(actors)
        logger.info(f"Actor pool initialized with {num_actors} actors")
    
    async def _scheduled_progressive_videos(self):
        """供调度器调用的内部方法，将渐进式合并任务添加到全局队列"""
        try:
            # 检查时间是否在允许的范围内，使用上海时区
            current_time = datetime.now(self.shanghai_tz)
            current_hour = current_time.hour
            blackout_start = self.config['schedule']['progressive_merge']['blackout_start_hour']
            blackout_end = self.config['schedule']['progressive_merge']['blackout_end_hour']
            
            # 检查是否在黑名单时间内（处理跨天的情况）
            is_blackout_time = False
            if blackout_start > blackout_end:  # 跨天的情况
                is_blackout_time = current_hour >= blackout_start or current_hour < blackout_end
            else:  # 同一天的情况
                is_blackout_time = blackout_start <= current_hour < blackout_end
                
            if is_blackout_time:
                logger.info(f"Current time is within the blackout period: {blackout_start} - {blackout_end}, skipping progressive merge")
                return
            
            # 获取当前日期和时间窗口信息，用于生成唯一任务ID
            current_date = current_time.strftime('%Y-%m-%d')
            interval_minutes = self.config['schedule']['progressive_merge']['interval_minutes']
            
            # 计算当前时间窗口标识（基于调度间隔）
            # 例如：如果间隔是30分钟，则0-29分钟为窗口0，30-59分钟为窗口1
            window_index = current_time.hour * 60 + current_time.minute // interval_minutes
            time_window = f"{current_time.hour:02d}{(current_time.minute // interval_minutes) * interval_minutes:02d}"
            
            logger.info(f"生成渐进式合并任务 - 日期: {current_date}, 时间窗口: {time_window}, 窗口索引: {window_index}")
            
            # 获取所有需要处理的ESN，并进行智能过滤
            esn_set = set()
            base_prefix = f"{self.config['video']['output_directory']}/"

            # 获取配置参数
            progressive_config = self.config['schedule']['progressive_merge']
            enable_prefiltering = progressive_config.get('enable_prefiltering', True)
            prefilter_check_files = progressive_config.get('prefilter_check_files', 5)

            # 获取所有ESN目录
            all_esns = set()
            paginator = self.oss_client.list_objects_v2_paginator()
            for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                if page.common_prefixes:
                    for prefix_obj in page.common_prefixes:
                        esn = prefix_obj.prefix.split('/')[1]
                        all_esns.add(esn)

            if not enable_prefiltering:
                # 如果禁用预过滤，直接处理所有ESN
                esn_set = all_esns
                logger.info(f"预过滤已禁用，将处理所有 {len(esn_set)} 个ESN")
            else:
                # 启用预过滤，检查ESN是否有当前时间窗口的视频文件
                logger.info(f"找到 {len(all_esns)} 个ESN目录，开始检查是否有 {current_date} 时间窗口 {time_window} 的视频文件...")

                # 计算当前时间窗口的时间范围，用于检查ESN是否有相关视频
                interval_minutes = progressive_config['interval_minutes']
                end_time = current_time
                start_time = end_time - timedelta(minutes=interval_minutes)

                # 如果跨天了，只处理今天的部分
                if start_time.date() < end_time.date():
                    start_time = end_time.replace(hour=0, minute=0, second=0, microsecond=0)

                start_ts = int(start_time.timestamp() * 1000000) #改成微秒级
                end_ts = int(end_time.timestamp() * 1000000)
                logger.info(f"用于过滤时间窗口的开始和结束时间: {start_time} - {end_time}")

                # 🚀 优化：过滤掉没有当前时间窗口视频文件的ESN
                for esn in all_esns:
                    esn_date_prefix = f"{base_prefix}{esn}/{current_date}/"
                    try:
                        # 1. 使用 delimiter='/' 列出当天的所有时间戳目录
                        dir_paginator = self.oss_client.list_objects_v2_paginator()
                        list_dirs_request = oss.ListObjectsV2Request(
                            bucket=self.bucket_name,
                            prefix=esn_date_prefix,
                            delimiter='/'
                        )

                        has_relevant_files = False
                        for page in dir_paginator.iter_page(list_dirs_request):
                            if page.common_prefixes:
                                for dir_prefix in page.common_prefixes:
                                    try:
                                        # 2. 提取并过滤时间戳目录
                                        ts_dir_str = dir_prefix.prefix.strip('/').split('/')[-1]
                                        ts_dir_int = int(ts_dir_str)

                                        # 3. 使用数字比较来过滤，找到一个在范围内的目录就足够了
                                        if start_ts <= ts_dir_int <= end_ts:
                                            # 检查这个目录是否为空
                                            first_check = self.oss_client.list_objects_v2(
                                                oss.ListObjectsV2Request(
                                                    bucket=self.bucket_name,
                                                    prefix=dir_prefix.prefix,
                                                    max_keys=1
                                                )
                                            )
                                            if first_check.contents:
                                                has_relevant_files = True
                                                break  # 找到一个非空目录就跳出内层循环
                                    except (ValueError, IndexError):
                                        continue  # 忽略无法解析的目录
                            if has_relevant_files:
                                break  # 已经确认有文件，跳出外层分页循环

                        if has_relevant_files:
                            esn_set.add(esn)
                            logger.debug(f"✅ ESN {esn} 有时间窗口 {time_window} 的文件，加入处理队列")
                        else:
                            logger.debug(f"📂 ESN {esn} 在时间窗口 {time_window} 无相关文件，跳过")

                    except Exception as e:
                        # 如果检查出错，保守起见还是加入队列，但记录警告
                        logger.warning(f"⚠️ 检查 ESN {esn} 时间窗口文件时出错: {e}，保守加入队列")
                        esn_set.add(esn)

                logger.info(f"经过时间窗口过滤，{len(esn_set)} 个ESN需要处理渐进式合并: {sorted(list(esn_set))}")

            if not esn_set:
                logger.info(f"当前时间窗口 {time_window} 没有ESN需要处理渐进式合并")
                return
            
            # 将ESN列表转换为列表并排序
            esn_list = sorted(list(esn_set))
            
            # 为每个ESN创建任务并添加到全局队列
            for esn in esn_list:
                # 生成精确的任务ID，支持去重
                # 格式: progressive_merge:{date}:{time_window}:{esn}
                # 例如: progressive_merge:2024-01-15:1030:ESN123456
                task_id = f"progressive_merge:{current_date}:{time_window}:{esn}"
                
                task = {
                    "task_id": task_id,
                    "task_type": "progressive_merge",
                    "esn": esn,
                    "date": current_date,
                    "time_window": time_window,
                    "window_index": window_index,
                    "create_time": time.time()
                }
                
                # 直接添加到全局队列
                await self._redis_queue_push(task)
                logger.info(f"已将渐进式合并任务添加到全局队列: ESN={esn}, task_id={task_id}")
            
            logger.info(f"已将 {len(esn_set)} 个渐进式合并任务添加到全局队列")
            
        except Exception as e:
            logger.error(f"调度渐进式合并任务时出错: {e}")
            logger.error(traceback.format_exc())

    def _setup_scheduler(self):
        """设置定时任务"""
        # 设置每天午夜执行的合并任务
        daily_config = self.config['schedule']['daily_concat']
        daily_job = self.scheduler.add_job(
            self._scheduled_task_with_lock,  # 使用带锁的执行方法
            CronTrigger(
                hour=daily_config['hour'],
                minute=daily_config['minute'],
                timezone=self.shanghai_tz
            ),
            id='daily_concat',
            name='Daily Video Concatenation',
            kwargs={
                'task_func': self._scheduled_concat_daily_videos,
                'lock_name': 'concat_daily_lock'
            }
        )
        
        # 为兜底检查添加一个新的、延迟执行的调度任务
        recovery_config = self.config['schedule'].get('daily_recovery_check', {'hour': 1, 'minute': 0})
        recovery_job = self.scheduler.add_job(
            self._scheduled_task_with_lock,
            CronTrigger(
                hour=recovery_config['hour'],
                minute=recovery_config['minute'],
                timezone=self.shanghai_tz
            ),
            id='daily_recovery_check',
            name='Daily Recovery Check for Missed Videos',
            kwargs={
                'task_func': self._scheduled_check_and_recover_missed_videos,
                'lock_name': 'daily_recovery_check_lock'
            }
        )
        
        # 设置渐进式合并任务
        progressive_config = self.config['schedule']['progressive_merge']
        progressive_job = self.scheduler.add_job(
            self._scheduled_task_with_lock,  # 使用带锁的执行方法
            IntervalTrigger(
                minutes=progressive_config['interval_minutes'],
                timezone=self.shanghai_tz
            ),
            id='progressive_merge',
            name='Progressive Video Merge',
            kwargs={
                'task_func': self._scheduled_progressive_videos,
                'lock_name': 'progressive_merge_lock'
            }
        )
        
        # 添加监听器
        def job_listener(event):
            if hasattr(event, 'exception') and event.exception:
                logger.error(f"Job {event.job_id} failed: {str(event.exception)}")
            else:
                logger.info(f"Job {event.job_id} completed successfully")
                # 获取下次运行时间
                try:
                    job = self.scheduler.get_job(event.job_id)
                    if job and job.next_run_time:
                        logger.info(f"Next run for job {event.job_id} scheduled at: {job.next_run_time}")
                except Exception as e:
                    logger.error(f"Error getting next run time for job {event.job_id}: {str(e)}")
                
        from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
        self.scheduler.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
        
        # 启动调度器
        try:
            self.scheduler.start()
            logger.info("Scheduler started successfully")
            
            # 打印所有已调度任务的信息
            for job in self.scheduler.get_jobs():
                logger.info(f"Scheduled job: {job.name} (ID: {job.id})")
                logger.info(f"  Next run time: {job.next_run_time}")
                logger.info(f"  Trigger: {job.trigger}")
            
            # 记录每个任务的下次运行时间
            logger.info(f"Daily concat job scheduled: Next run at {daily_job.next_run_time}")
            logger.info(f"Progressive merge job scheduled: Next run at {progressive_job.next_run_time}")
            logger.info(f"Daily recovery check job scheduled: Next run at {recovery_job.next_run_time}")
        except Exception as e:
            logger.error(f"Failed to start scheduler: {str(e)}")
            raise

    async def _scheduled_task_with_lock(self, task_func, lock_name):
        """使用分布式锁执行调度任务，确保集群中只有一个实例执行任务
        
        Args:
            task_func: 要执行的任务函数
            lock_name: 分布式锁名称
        """
        conn_actor = await self.get_conn_actor()
        try:
            # 生成带有过期时间的锁键名
            lock_key = f"concat:scheduler:{lock_name}"
            lock_expire = 3600  # 1小时，足够任务执行完毕
            
            # 尝试获取锁，使用Redis的SETNX原子操作
            lock_acquired = await conn_actor.redis_setnx.remote(
                lock_key, 
                self.instance_id,
                lock_expire
            )
            
            if lock_acquired:
                logger.info(f"实例 {self.instance_id} 获取调度锁 {lock_name}，开始执行任务")
                try:
                    # 只有获取到锁的实例执行调度任务
                    await task_func()
                except Exception as e:
                    logger.error(f"执行调度任务 {lock_name} 出错: {e}")
                    logger.error(traceback.format_exc())
                finally:
                    # 任务完成后尝试释放锁（只释放自己的锁）
                    current_holder = await conn_actor.redis_get.remote(lock_key)
                    if current_holder == self.instance_id:
                        await conn_actor.redis_delete.remote(lock_key)
                        logger.info(f"实例 {self.instance_id} 释放调度锁 {lock_name}")
            else:
                # 如果未获得锁，记录日志但不执行任务
                current_holder = await conn_actor.redis_get.remote(lock_key)
                logger.info(f"调度任务 {lock_name} 由其他实例 {current_holder} 执行，本实例跳过")
        finally:
            await self.push_conn_actor(conn_actor)

    async def check_health(self):
        """健康检查方法
        
        检查：
        1. Actor Pool 状态
        2. 调度器状态
        3. 通过 Actor Pool 检查：
           - OSS 连接状态
           - Redis 连接状态
           - 磁盘空间状态
           - FFmpeg 可用性
        
        如果任何检查失败，将抛出异常
        """
        errors = []
        
        # 检查 Actor Pool 状态
        try:
            if not hasattr(self, 'actor_pool'):
                error_msg = "Actor pool is not initialized"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                # 对每个 actor 进行 ping 测试
                futures = []
                for actor in self.actor_pool._idle_actors:
                    # 将 ObjectRef 转换为 Future
                    ref = actor.ping.remote()
                    future = asyncio.wrap_future(
                        asyncio.get_event_loop().create_future()
                    )
                    # 使用 ray.get 在后台获取结果
                    asyncio.create_task(
                        self._get_actor_result(ref, future)
                    )
                    futures.append(future)
                
                try:
                    await asyncio.gather(*futures)
                    logger.info("Actor pool health check passed")
                except Exception as e:
                    error_msg = f"Actor pool health check failed: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    # 如果检查失败，尝试重新初始化 actor pool
                    try:
                        self._setup_actor_pool()
                        logger.info("Actor pool reinitialized after health check failure")
                    except Exception as reinit_error:
                        logger.error(f"Failed to reinitialize actor pool: {str(reinit_error)}")
        except Exception as e:
            error_msg = f"Failed to check actor pool health: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # 检查调度器状态
        try:
            if not hasattr(self, 'scheduler'):
                error_msg = "Scheduler is not initialized"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                # 检查调度器是否正在运行
                if not self.scheduler.running:
                    error_msg = "Scheduler is not running"
                    logger.error(error_msg)
                    errors.append(error_msg)
                else:
                    # 检查所有任务的状态
                    jobs = self.scheduler.get_jobs()
                    if not jobs:
                        error_msg = "No jobs found in scheduler"
                        logger.error(error_msg)
                        errors.append(error_msg)
                    else:
                        # 检查每个任务的下次运行时间
                        for job in jobs:
                            if not job.next_run_time:
                                error_msg = f"Job {job.id} has no next run time scheduled"
                                logger.error(error_msg)
                                errors.append(error_msg)
                        logger.info("Scheduler health check passed")
        except Exception as e:
            error_msg = f"Failed to check scheduler health: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # 检查Redis连接状态
        try:
            # 从Redis Actor池获取一个Actor进行检查
            if hasattr(self, 'redis_actors_pool') and self.redis_actors_pool._idle_actors:
                redis_actor = next(iter(self.redis_actors_pool._idle_actors))
                health_ref = redis_actor.check_health.remote()
                health_future = asyncio.wrap_future(
                    asyncio.get_event_loop().create_future()
                )
                asyncio.create_task(
                    self._get_actor_result(health_ref, health_future)
                )
                try:
                    health_status = await health_future
                    if not health_status.get('redis', False):
                        error_msg = "Redis connection check failed"
                        logger.error(error_msg)
                        errors.append(error_msg)
                    else:
                        logger.info("Redis connection health check passed")
                except Exception as e:
                    error_msg = f"Redis health check failed: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            else:
                error_msg = "Redis actor pool is not available"
                logger.error(error_msg)
                errors.append(error_msg)
        except Exception as e:
            error_msg = f"Failed to check Redis connection: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # 检查磁盘空间状态
        try:
            temp_dir = self.config['temp']['base_dir']
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir, exist_ok=True)
            
            # 获取磁盘使用情况
            disk_usage = os.statvfs(temp_dir)
            free_space_mb = (disk_usage.f_bavail * disk_usage.f_frsize) / (1024 * 1024)  # 转换为MB
            min_required_space_mb = self.config.get('temp', {}).get('min_free_space_mb', 1024)  # 默认要求1GB空间
            
            if free_space_mb < min_required_space_mb:
                error_msg = f"Insufficient disk space: {free_space_mb:.2f}MB free, minimum required: {min_required_space_mb}MB"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                logger.info(f"Disk space check passed: {free_space_mb:.2f}MB free")
        except Exception as e:
            error_msg = f"Failed to check disk space: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # 检查FFmpeg可用性
        try:
            import subprocess
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode != 0:
                error_msg = "FFmpeg is not available"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                logger.info("FFmpeg check passed")
        except Exception as e:
            error_msg = f"Failed to check FFmpeg: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # 检查GPU资源状态（如果配置了使用GPU）
        if self.config.get('gpu', {}).get('enabled', False):
            try:
                # 获取一个空闲的actor来检查GPU状态
                if self.actor_pool._idle_actors:
                    actor = next(iter(self.actor_pool._idle_actors))
                    ref = actor.check_gpu_memory_sufficient.remote()
                    future = asyncio.wrap_future(
                        asyncio.get_event_loop().create_future()
                    )
                    asyncio.create_task(
                        self._get_actor_result(ref, future)
                    )
                    try:
                        gpu_sufficient = await future
                        if not gpu_sufficient:
                            error_msg = "Insufficient GPU memory"
                            logger.error(error_msg)
                            errors.append(error_msg)
                        else:
                            logger.info("GPU resource check passed")
                    except Exception as e:
                        error_msg = f"GPU check failed: {str(e)}"
                        logger.error(error_msg)
                        errors.append(error_msg)
            except Exception as e:
                error_msg = f"Failed to check GPU status: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)

        # 如果有任何错误，抛出异常
        if errors:
            self.health_status = {
                "status": "unhealthy",
                "last_check": time.time(),
                "message": '; '.join(errors)
            }
            raise RuntimeError(f"Health check failed: {'; '.join(errors)}")
        
        # 更新健康状态
        self.health_status = {
            "status": "healthy",
            "last_check": time.time(),
            "message": "All health checks passed successfully"
        }
        self.last_health_check = time.time()
        
        logger.info("All health checks passed successfully")

    async def _get_actor_result(self, ref, future):
        """辅助方法：在后台获取 actor 调用的结果"""
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: ray.get(ref)
            )
            future.set_result(result)
        except Exception as e:
            future.set_exception(e)
    
    @app.get("/api/v1/video/health")
    async def health(self) -> Dict:
        """获取服务健康状态
        
        示例请求:
            GET /api/v1/video/health
            
        示例响应:
            {
                "status": "healthy",
                "last_check": **********,
                "message": "Service is healthy"
            }
        """
        # 如果超过60秒没有进行健康检查，立即执行一次
        if time.time() - self.last_health_check > 60:
            try:
                await self.check_health()
            except Exception:
                pass
        return self.health_status
    
    @app.post("/api/v1/video/concat")
    async def concat_daily_videos(self, request: ConcatRequest) -> Dict:
        """
        根据日期和可选的ESN，拼接每日视频，用于api调用
        """
        max_retries = 3
        retry_delay = 5  # 初始重试延迟（秒）
        batch_size = self.config['Ray']['num_actors']  # 每批处理的ESN数量
        
        try:
            # 检查服务是否健康
            if self.health_status["status"] != "healthy":
                return {
                    "status": "error",
                    "message": "Service is unhealthy, please check health status"
                }

            # 处理请求参数
            date = request.date
            specific_esn = request.esn
            
            if date is None:
                # 默认处理前一天的视频，使用上海时区
                date = (datetime.now(self.shanghai_tz) - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 获取所有需要处理的ESN
            esn_set = set()
            base_prefix = f"{self.config['video']['output_directory']}/"
            
            # 添加重试机制获取ESN列表
            for attempt in range(max_retries):
                try:
                    # 使用 list_objects_v2_paginator 获取所有目录
                    paginator = self.oss_client.list_objects_v2_paginator()
                    for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                        for prefix_obj in page.common_prefixes:
                            # 从目录名中提取ESN
                            # 假设格式为 "base_prefix/ESN/"
                            esn_prefix = prefix_obj.prefix[len(base_prefix):]
                            esn = esn_prefix.strip('/')
                            if esn:  # 排除空ESN
                                esn_set.add(esn)
                    
                    logger.info(f"找到 {len(esn_set)} 个ESN: {esn_set}")
                    break
                except Exception as e:
                    logger.error(f"获取ESN列表出错 (尝试 {attempt+1}/{max_retries}): {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
                    else:
                        raise
            
            if not esn_set:
                logger.warning("没有找到需要处理的ESN")
                return {
                    "status": "error",
                    "message": "没有找到需要处理的ESN"
                }
            
            # 将ESN列表转换为列表并排序
            esn_list = sorted(list(esn_set))
            total_esns = len(esn_list)
            successful_results = []
            failed_esns = []
            pending_esns = []  # 存储需要等待的ESN和已合并文件列表
            
            logger.info(f"开始处理 {total_esns} 个ESN，每批处理 {batch_size} 个")
            
            # 分批处理ESN
            for i in range(0, total_esns, batch_size):
                batch = esn_list[i:i + batch_size]
                batch_num = i // batch_size + 1
                total_batches = (total_esns + batch_size - 1) // batch_size
                
                logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个ESN")
                
                # 首先检查每个ESN的任务状态
                current_batch = []
                for esn in batch:
                    logger.info(f"检查ESN {esn} 的任务状态")
                    # 检查ESN的任务状态，同时获取已处理的渐进式合并列表
                    status = list(self.actor_pool.map(
                        lambda actor, task: actor.check_esn_tasks.remote(task[0], task[1]),
                        [(esn, date)]
                    ))[0]
                    logger.info(f"ESN {esn} 的任务状态: {status}")
                    if status.get("has_processing_yolo"):
                        logger.info(f"ESN {esn} 还有YOLO任务在处理中，暂时跳过")
                        pending_esns.append((esn, status.get("merged_files", [])))  # 保存已合并文件列表
                    else:
                        current_batch.append((esn, status.get("merged_files", [])))  # 保存已合并文件列表
                
                if current_batch:
                    # 创建任务列表，处理可以处理的ESN，并传入已合并文件列表
                    tasks = [(esn, date, merged_files) for esn, merged_files in current_batch]
                    
                    try:
                        batch_results = list(self.actor_pool.map(
                            lambda actor, task: actor.concat_video_oss.remote(task[1], task[0], task[2]),  # 传入date、esn和merged_files
                            tasks
                        ))
                        
                        # 处理每个ESN的结果
                        for esn, result in zip(current_batch, batch_results):
                            if result and isinstance(result, dict):
                                if (result.get("results") or result.get("status") == "success"):
                                    if isinstance(result.get("results"), list):
                                        successful_results.extend(result["results"])
                                    else:
                                        successful_results.append(result)
                                    logger.info(f"ESN {esn} 处理成功")
                                else:
                                    failed_esns.append({
                                        "esn": esn[0],
                                        "error": result.get("message", "Unknown error"),
                                        "result": result
                                    })
                            else:
                                failed_esns.append({
                                    "esn": esn[0],
                                    "error": "Invalid result format",
                                    "result": result
                                })
                    
                    except Exception as e:
                        logger.error(f"处理批次 {batch_num} 时发生错误: {str(e)}")
                        failed_esns.extend([{"esn": esn[0], "error": str(e)} for esn, _ in current_batch])
                    
                    await asyncio.sleep(2)
            
            # 处理pending的ESN
            if pending_esns:
                logger.info(f"开始处理等待队列中的 {len(pending_esns)} 个ESN")
                max_wait_time = self.config['schedule']['daily_concat']['max_wait_time']
                start_wait = time.time()
                
                while pending_esns and (time.time() - start_wait < max_wait_time):
                    # 复制一份列表，因为我们可能会修改它
                    current_pending = pending_esns.copy()
                    pending_esns.clear()
                    
                    for esn, merged_files in current_pending:
                        # 重新检查状态
                        status = list(self.actor_pool.map(
                            lambda actor, task: actor.check_esn_tasks.remote(task[0], task[1]),
                            [(esn, date)]
                        ))[0]

                        logger.info(f"ESN {esn} 当前状态: {status}")
                        if not status.get("has_processing_yolo"):
                            # 可以处理这个ESN了，使用最新的已合并文件列表
                            try:
                                result = list(self.actor_pool.map(
                                    lambda actor, task: actor.concat_video_oss.remote(task[1], task[0], task[2]),
                                    [(esn, date, status.get("merged_files", []))]
                                ))[0]
                                
                                if result and isinstance(result, dict):
                                    if (result.get("results") or result.get("status") == "success"):
                                        successful_results.append(result)
                                        logger.info(f"等待队列中的ESN {esn} 处理成功")
                                    else:
                                        failed_esns.append({
                                            "esn": esn,
                                            "error": result.get("message", "Unknown error"),
                                            "result": result
                                        })
                            except Exception as e:
                                failed_esns.append({"esn": esn, "error": str(e)})
                        else:
                            # 仍然在处理中，放回pending队列，保留已合并文件列表
                            pending_esns.append((esn, status.get("merged_files", [])))
                    
                    if pending_esns:
                        await asyncio.sleep(10)
                
                # 如果还有未处理完的ESN
                if pending_esns:
                    logger.warning(f"达到最大等待时间，仍有 {len(pending_esns)} 个ESN未处理完成，直接使用原始的方法进行最后的合并。")
                    results = list(self.actor_pool.map(
                        lambda actor, task: actor.concat_video_oss.remote(date, task[0], task[1]),
                        pending_esns
                    ))
                    successful_results.extend([r for r in results if r and r.get('status') == 'success'])
                    failed_esns.extend([{"esn": esn, "error": "Timeout waiting for YOLO tasks"} for esn, _ in pending_esns])
            
            # 统计最终结果
            total_processed = len(successful_results)
            total_failed = len(failed_esns)
            
            logger.info(f"视频合并任务完成，处理了 {total_esns} 个ESN")
            logger.info(f"成功数: {total_processed}")
            logger.info(f"失败数: {total_failed}")
            
            if failed_esns:
                logger.error("失败的ESN列表:")
                for failed in failed_esns:
                    logger.error(f"ESN: {failed['esn']}, 错误: {failed['error']}")
            
            return {
                "status": "success" if total_processed > 0 else "error",
                "processed_esn": total_processed,
                "total_esn": total_esns,
                "failed_esn": total_failed,
                "failed_details": failed_esns,
                "results": successful_results,
                "date": date
            }
            
        except Exception as e:
            error_msg = f"Error in concat daily videos: {str(e)}"
            logger.error(error_msg)
            logger.error(f"详细错误: {traceback.format_exc()}")
            return {
                "status": "error",
                "message": error_msg
            }
    
    @app.post("/api/v1/video/progressive_merge")
    async def progressive_merge(self) -> Dict:
        """执行渐进式合并"""
        try:
            # 检查服务是否健康
            if self.health_status["status"] != "healthy":
                return {
                    "status": "error",
                    "message": "Service is unhealthy, please check health status"
                }
            
            # 检查时间是否在允许的范围内，使用上海时区
            current_hour = datetime.now(self.shanghai_tz).hour
            blackout_start = self.config['schedule']['progressive_merge']['blackout_start_hour']
            blackout_end = self.config['schedule']['progressive_merge']['blackout_end_hour']
            
            # 检查是否在黑名单时间内（处理跨天的情况）
            is_blackout_time = False
            if blackout_start > blackout_end:  # 跨天的情况
                is_blackout_time = current_hour >= blackout_start or current_hour < blackout_end
            else:  # 同一天的情况
                is_blackout_time = blackout_start <= current_hour < blackout_end
                
            if is_blackout_time:
                logger.info(f"Current time is within the blackout period: {blackout_start} - {blackout_end}, skipping progressive merge")
                return {
                    "status": "skipped",
                    "message": f"当前时间在{blackout_start}:00-{blackout_end}:00时间段内，跳过渐进式合并"
                }
            
            task_id = str(uuid.uuid4())
            logger.info(f"Starting progressive merge task {task_id}")
            
            # 获取所有需要处理的ESN
            esn_set = set()
            base_prefix = f"{self.config['video']['output_directory']}/"
            
            # 获取所有ESN目录
            paginator = self.oss_client.list_objects_v2_paginator()
            for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                for prefix_obj in page.common_prefixes:
                    esn = prefix_obj.prefix.split('/')[1]
                    esn_set.add(esn)
            
            logger.info(f"Found {len(esn_set)} ESNs to process")
            
            if not esn_set:
                error_msg = f"No ESNs found for progressive merge"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "message": error_msg,
                    "task_id": task_id,
                    "create_time": time.time()
                }
            
            # 创建任务列表，每个ESN作为一个独立任务
            tasks = [(esn,) for esn in esn_set]  # 每个任务是一个元组，只包含ESN
            
            # 使用actor_pool.map并行处理每个ESN
            results = list(self.actor_pool.map(
                lambda actor, task: actor.progressive_merge_videos.remote(task[0]),  # 传入ESN参数
                tasks
            ))
            
            # 过滤出成功的结果
            successful_results = [r for r in results if r and r.get("status") == "success"]
            
            logger.info(f"Progressive merge completed for task {task_id}")
            return {
                "status": "success",
                "task_id": task_id,
                "total_esn": len(esn_set),
                "processed_esn": len(successful_results),
                "results": results,
                "create_time": time.time()
            }
            
        except Exception as e:
            error_msg = f"Error processing task {task_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "task_id": task_id,
                "create_time": time.time()
            }
    
    @app.post("/api/v1/video/concat-by-timerange")
    async def concat_timerange_videos(self, request: ConcatTimerangeRequest) -> Dict:
        """基于时间范围合并视频"""
        try:
            # 检查服务是否健康
            if self.health_status["status"] != "healthy":
                return {
                    "status": "error",
                    "message": "Service is unhealthy, please check health status"
                }
            
            # 从请求中获取参数
            start_time = request.start_time
            end_time = request.end_time
            date = request.date
            task_id = str(uuid.uuid4())
            
            if date is None:
                # 默认处理前一天的视频
                date = (datetime.now(self.shanghai_tz) - timedelta(days=1)).strftime('%Y-%m-%d')
            
            logger.info(f"Starting timerange concat task {task_id} for date {date}, "
                     f"time range: {start_time}-{end_time}")
            
            # 获取所有需要处理的ESN
            esn_set = set()
            base_prefix = f"{self.config['video']['output_directory']}/"
            
            # 获取所有ESN目录
            paginator = self.oss_client.list_objects_v2_paginator()
            for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                for prefix_obj in page.common_prefixes:
                    esn = prefix_obj.prefix.split('/')[1]
                    esn_set.add(esn)
            
            logger.info(f"Found {len(esn_set)} ESNs to process")
            
            if not esn_set:
                error_msg = f"No ESNs found for date {date}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "message": error_msg,
                    "task_id": task_id,
                    "create_time": time.time()
                }
            
            # 创建任务列表，每个ESN作为一个独立任务
            tasks = [(esn, task_id, start_time, end_time, date) for esn in esn_set]
            
            # 使用actor_pool.map并行处理每个ESN
            results = list(self.actor_pool.map(
                lambda actor, task: actor.concat_video_by_timerange_oss.remote(
                    task_id=task[1],
                    start_time=task[2],
                    end_time=task[3],
                    date=task[4],
                    need_callback=False, #时间段范围合并不需要回调
                    esn=task[0]  # 传递ESN参数
                ),
                tasks
            ))
            
            # 过滤出成功的结果
            successful_results = [r for r in results if r and r.get("status") == "success"]
            
            if not successful_results:
                error_msg = f"处理任务 {task_id} 失败：没有成功的处理结果"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "message": error_msg,
                    "task_id": task_id,
                    "create_time": time.time()
                }
            
            logger.info(f"Timerange video processing completed for task {task_id}")
            return {
                "status": "success",
                "task_id": task_id,
                "date": date,
                "time_range": {
                    "start": start_time,
                    "end": end_time
                },
                "total_esn": len(esn_set),
                "processed_esn": len(successful_results),
                "results": results,
                "create_time": time.time()
            }
            
        except Exception as e:
            error_msg = f"Error processing task {task_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "task_id": task_id,
                "create_time": time.time()
            }
    
    @app.get("/api/v1/video/scheduler-status")
    async def scheduler_status(self) -> Dict:
        """获取调度器状态
        
        示例请求:
            GET /api/v1/video/scheduler-status
            
        示例响应:
            {
                "status": "running",
                "jobs": [
                    {
                        "id": "daily_concat",
                        "name": "Daily Video Concatenation",
                        "next_run_time": "2024-12-31 23:59:59",
                        "trigger": "cron[hour='23', minute='59']"
                    },
                    {
                        "id": "progressive_merge",
                        "name": "Progressive Video Merge",
                        "next_run_time": "2024-12-31 11:30:00",
                        "trigger": "interval[0:30:00]"
                    }
                ]
            }
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": str(job.next_run_time),
                    "trigger": str(job.trigger)
                })
            
            return {
                "status": "running" if self.scheduler.running else "stopped",
                "jobs": jobs
            }
        except Exception as e:
            logger.error(f"Error getting scheduler status: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }
    

    # 查看oss的一些操作是否支持的接口
    @app.get("/api/v1/video/oss-operations")
    async def oss_operations(self) -> Dict:
        """查看OSS操作支持的接口"""
        try:
            base_prefix = f"{self.config['video']['output_directory']}/"
            # 获取OSS支持的所有操作
            client = self.oss_client
            operations = []
            
            # 获取所有ESN目录
            paginator = self.oss_client.list_objects_v2_paginator()
            for page in paginator.iter_page(oss.ListObjectsV2Request(bucket=self.bucket_name, prefix=base_prefix, delimiter='/')):
                for prefix_obj in page.common_prefixes:
                    operations.append("oss.ListObjectsRequest")
            
            return {
                "status": "success",
                "operations": operations
            }
        except Exception as e:
            logger.error(f"Error getting oss operations: {str(e)}")

    @app.post("/api/v1/video/test/generate-batch")
    async def generate_batch_test_videos(self, request: BatchGenerateTestVideosRequest) -> Dict:
        """
        批量生成测试视频并推送到OSS（异步并行处理）
        
        Args:
            request: 批量生成请求参数
            
        Returns:
            生成结果信息
        """
        try:
            logger.info(f"开始批量生成测试视频: {request.model_dump()}")
            
            # 获取一个可用的Actor
            actor_ref = await self.get_conn_actor(actor_type="concat")
            
            try:
                # 调用Actor的批量生成方法（异步并行处理）
                result = await actor_ref.generate_batch_test_videos.remote(
                    esn_prefix=request.esn_prefix,
                    date=request.date,
                    video_count=request.video_count,
                    esn_count=request.esn_count,
                    video_duration=request.video_duration
                )
                
                logger.info(f"批量生成测试视频完成，共生成 {result.get('successful_videos', 0)} 个视频")
                
                return {
                    "status": "success",
                    "message": f"成功生成 {request.esn_count} 个ESN，每个ESN {request.video_count} 个视频",
                    "result": result
                }
                
            except Exception as e:
                logger.error(f"批量生成测试视频失败: {str(e)}")
                return {
                    "status": "error",
                    "message": f"批量生成测试视频失败: {str(e)}"
                }
            finally:
                # 归还Actor到池中
                await self.push_conn_actor(actor_ref, actor_type="concat")
                
        except Exception as e:
            logger.error(f"批量生成测试视频接口调用失败: {str(e)}")
            return {
                "status": "error",
                "message": f"批量生成测试视频接口调用失败: {str(e)}"
            }

    async def __del__(self):
        """清理资源，在服务关闭时调用"""
        try:
            logger.info("正在清理Concat视频服务资源...")
            
            # 取消所有异步任务
            for task_name, task in [
                ('heartbeat_task', getattr(self, 'heartbeat_task', None)),
                ('metrics_task', getattr(self, 'metrics_task', None)),
                ('processor_task', getattr(self, 'processor_task', None)),
                ('system_monitor_task', getattr(self, 'system_monitor_task', None)),
                ('lock_monitor_task', getattr(self, 'lock_monitor_task', None)),
                ('timeout_monitor_task', getattr(self, 'timeout_monitor_task', None))
            ]:
                if task:
                    logger.info(f"取消任务: {task_name}")
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            # 关闭调度器
            if hasattr(self, 'scheduler') and self.scheduler:
                if self.scheduler.running:
                    try:
                        self.scheduler.shutdown()
                        logger.info("调度器已关闭")
                    except Exception as e:
                        logger.error(f"关闭调度器时出错: {e}")
            
            # 从活跃实例集合中移除当前实例
            try:
                conn_actor = await self.get_redis_actor()
                await conn_actor.redis_srem.remote(self.redis_instances_key, self.instance_id)
                logger.info(f"从活跃实例集合中移除: {self.instance_id}")
                await self.push_conn_actor(conn_actor)
            except Exception as e:
                logger.error(f"从活跃实例集合中移除时出错: {e}")
            
            # 调用父类的 __del__ 方法
            if hasattr(self, '_serve_asgi_lifespan'):
                try:
                    await self._serve_asgi_lifespan.__aexit__(None, None, None)
                except Exception as e:
                    logger.error(f"调用ASGI生命周期退出时出错: {e}")
            
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"在__del__方法中清理资源时出错: {e}")
            logger.error(traceback.format_exc())

    async def _monitor_scheduler_locks(self):
        """监控调度锁，清理可能的死锁和僵尸实例持有的锁"""
        while True:
            try:
                await asyncio.sleep(120)  # 每2分钟检查一次，更频繁地清理死锁

                conn_actor = await self.get_redis_actor()
                try:
                    # 获取当前活跃的实例列表
                    active_instances = await conn_actor.redis_smembers.remote(self.redis_instances_key)
                    active_instances_set = set(active_instances) if active_instances else set()

                    # 获取所有调度锁
                    scheduler_lock_keys = await conn_actor.redis_keys.remote("concat:scheduler:*")

                    for lock_key in scheduler_lock_keys:
                        try:
                            # 获取锁的持有者
                            lock_holder = await conn_actor.redis_get.remote(lock_key)

                            # 检查锁的剩余过期时间
                            ttl = await conn_actor.redis_ttl.remote(lock_key)

                            should_clean = False
                            reason = ""

                            # 情况1：锁已过期或永不过期
                            if ttl < 0:
                                should_clean = True
                                reason = "锁已过期或异常"

                            # 情况2：锁持有者不在活跃实例列表中（僵尸实例）
                            elif lock_holder and lock_holder not in active_instances_set:
                                should_clean = True
                                reason = f"锁持有者 {lock_holder} 不在活跃实例列表中"

                            # 情况3：锁存在时间过长（超过30分钟），可能是死锁
                            elif ttl > 0 and (3600 - ttl) > 1800:  # 锁设置1小时过期，已存在超过30分钟
                                should_clean = True
                                reason = f"锁存在时间过长 ({3600 - ttl} 秒)"

                            if should_clean:
                                await conn_actor.redis_delete.remote(lock_key)
                                logger.warning(f"已清理调度锁 {lock_key}: {reason}")

                                # 如果是因为僵尸实例导致的，额外记录信息
                                if "不在活跃实例列表中" in reason:
                                    logger.info(f"当前活跃实例: {sorted(active_instances_set)}")

                        except Exception as lock_error:
                            logger.error(f"处理调度锁 {lock_key} 时出错: {lock_error}")
                            # 出错的锁也清理掉，避免一直存在问题
                            try:
                                await conn_actor.redis_delete.remote(lock_key)
                                logger.warning(f"清理出错的调度锁: {lock_key}")
                            except:
                                pass

                finally:
                    await self.push_conn_actor(conn_actor)

            except Exception as e:
                logger.error(f"监控调度锁时出错: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(60)  # 出错时等待较短时间再重试

    async def _monitor_timeout_tasks(self):
        """监控超时任务并清理其临时文件"""
        while True:
            try:
                await asyncio.sleep(60)  # 每1分钟检查一次

                conn_actor = await self.get_redis_actor()
                try:
                    # 获取所有正在处理的任务
                    processing_tasks = await conn_actor.redis_smembers.remote("video:processing_tasks")
                    current_time = time.time()

                    # 根据任务类型设置不同的超时阈值
                    progressive_config = self.config.get('schedule', {}).get('progressive_merge', {})
                    daily_config = self.config.get('schedule', {}).get('daily_concat', {})
                    
                    progressive_timeout = progressive_config.get('timeout_minutes', 1800)
                    daily_timeout = daily_config.get('max_wait_time', 1800)
                    default_timeout = 1800     # 其他任务30分钟超时

                    for task_id in processing_tasks:
                        try:
                            # 获取任务锁的值，其中包含了任务开始的时间戳
                            lock_key = f"video:processing_tasks:{task_id}"
                            start_time_str = await conn_actor.redis_get.remote(lock_key)

                            if not start_time_str:
                                # 如果锁不存在，可能任务已完成，跳过
                                continue
                            
                            start_time = float(start_time_str)
                            run_time = current_time - start_time

                            # 根据任务类型确定超时阈值
                            if task_id.startswith("progressive_merge:"):
                                timeout_threshold = progressive_timeout
                                task_type = "渐进式合并"
                            elif task_id.startswith("concat_daily:"):
                                timeout_threshold = daily_timeout
                                task_type = "日常合并"
                            else:
                                timeout_threshold = default_timeout
                                task_type = "其他"

                            # 真正的超时判断
                            if run_time > timeout_threshold:
                                self.logger.warning(f"发现超时{task_type}任务: {task_id} (已运行 {run_time:.2f} 秒，超过阈值 {timeout_threshold} 秒)，开始清理...")

                                # 使用临时文件管理器清理任务相关文件
                                try:
                                    from .temp_file_manager import TempFileManager
                                    temp_manager = TempFileManager(
                                        redis_actor=conn_actor,
                                        config=self.config,
                                        logger=logger
                                    )

                                    cleaned_count = await temp_manager.cleanup_task_files(task_id)
                                    logger.info(f"清理超时{task_type}任务 {task_id} 的临时文件: {cleaned_count} 个")

                                except Exception as cleanup_error:
                                    logger.error(f"清理超时{task_type}任务 {task_id} 的临时文件失败: {cleanup_error}")

                                # 清理任务状态
                                await conn_actor.redis_srem.remote("video:processing_tasks", task_id)
                                await conn_actor.redis_delete.remote(lock_key)
                                logger.info(f"已清理超时{task_type}任务状态: {task_id}")

                                # 记录统计信息
                                if task_id.startswith("progressive_merge:"):
                                    try:
                                        parts = task_id.split(":")
                                        if len(parts) >= 4:
                                            esn = parts[3]
                                            date = parts[1]
                                            time_window = parts[2]
                                            logger.info(f"📊 渐进式合并超时统计: ESN={esn}, 日期={date}, 时间窗口={time_window}")
                                    except Exception:
                                        pass

                        except (ValueError, TypeError) as e:
                            self.logger.error(f"处理任务 {task_id} 时时间戳格式错误: {e}")
                            continue
                        except Exception as task_error:
                            logger.error(f"处理超时任务 {task_id} 时出错: {task_error}")
                            continue

                finally:
                    await self.push_conn_actor(conn_actor)

            except Exception as e:
                logger.error(f"监控超时任务时出错: {e}")
                logger.error(traceback.format_exc())
                await asyncio.sleep(60)  # 出错时等待较短时间再重试

    async def _startup_cleanup(self):
        """服务启动时的清理工作，清理遗留的锁、状态和临时文件"""
        try:
            logger.info("开始启动清理，检查遗留的任务锁和临时文件...")
            
            conn_actor = await self.get_redis_actor()
            try:
                # 清理遗留的任务锁（处理锁和队列锁）
                processing_lock_pattern = "video:processing_tasks:*"
                queue_lock_pattern = "video:queue_locks:*"
                processing_lock_keys = await conn_actor.redis_keys.remote(processing_lock_pattern)
                queue_lock_keys = await conn_actor.redis_keys.remote(queue_lock_pattern)
                lock_keys = processing_lock_keys + queue_lock_keys
                
                cleaned_locks = 0
                cleaned_orphan_tasks = 0
                
                # 清理过期或孤儿锁
                for lock_key in lock_keys:
                    try:
                        # 检查锁的TTL
                        ttl = await conn_actor.redis_ttl.remote(lock_key)
                        
                        # 之前的逻辑过于激进，会错误地恢复正在运行的长任务。
                        # 新逻辑：只清理永不过期的锁，将超时恢复的判断完全交给 _monitor_timeout_tasks。
                        if ttl == -1:
                            if lock_key.startswith("video:processing_tasks:"):
                                task_id = lock_key.replace("video:processing_tasks:", "")
                            elif lock_key.startswith("video:queue_locks:"):
                                task_id = lock_key.replace("video:queue_locks:", "")
                            else:
                                logger.warning(f"未知的锁键格式: {lock_key}")
                                continue

                            logger.warning(f"启动时发现永不过期的锁 {task_id}，强制清理。")
                            await conn_actor.redis_delete.remote(lock_key)
                            cleaned_locks += 1
                            # 同样从状态集合中移除，确保一致性
                            await conn_actor.redis_srem.remote("video:processing_tasks", task_id)
                            
                    except Exception as e:
                        logger.error(f"处理锁键 {lock_key} 时出错: {e}")
                        # 出错时也清理，避免问题锁一直存在
                        try:
                            await conn_actor.redis_delete.remote(lock_key)
                            cleaned_locks += 1
                        except:
                            pass
                
                # 清理状态集合中的孤儿任务ID
                processing_tasks_key = "video:processing_tasks"
                processing_task_ids = await conn_actor.redis_smembers.remote(processing_tasks_key)
                
                for task_id in processing_task_ids:
                    task_lock_key = f"video:processing_tasks:{task_id}"
                    lock_exists = await conn_actor.redis_exists.remote(task_lock_key)
                    
                    if not lock_exists:
                        # 状态存在但锁不存在，清理孤儿状态
                        await conn_actor.redis_srem.remote(processing_tasks_key, task_id)
                        cleaned_orphan_tasks += 1
                        logger.info(f"清理孤儿任务状态: {task_id}")
                
                logger.info(f"启动清理完成: 清理了 {cleaned_locks} 个异常锁, {cleaned_orphan_tasks} 个孤儿状态。")

                # 清理遗留的调度锁（特别是僵尸实例持有的锁）
                try:
                    logger.info("开始清理遗留的调度锁...")

                    # 获取当前活跃的实例列表
                    active_instances = await conn_actor.redis_smembers.remote(self.redis_instances_key)
                    active_instances_set = set(active_instances) if active_instances else set()

                    # 获取所有调度锁
                    scheduler_lock_keys = await conn_actor.redis_keys.remote("concat:scheduler:*")
                    cleaned_scheduler_locks = 0

                    for lock_key in scheduler_lock_keys:
                        try:
                            # 获取锁的持有者
                            lock_holder = await conn_actor.redis_get.remote(lock_key)

                            should_clean = False
                            reason = ""

                            # 检查锁的TTL
                            ttl = await conn_actor.redis_ttl.remote(lock_key)

                            if ttl < 0:
                                should_clean = True
                                reason = "锁已过期或异常"
                            elif lock_holder and lock_holder not in active_instances_set:
                                should_clean = True
                                reason = f"锁持有者 {lock_holder} 不在活跃实例列表中"

                            if should_clean:
                                await conn_actor.redis_delete.remote(lock_key)
                                cleaned_scheduler_locks += 1
                                logger.warning(f"清理遗留调度锁 {lock_key}: {reason}")

                        except Exception as lock_error:
                            logger.error(f"处理调度锁 {lock_key} 时出错: {lock_error}")
                            # 出错的锁也清理掉
                            try:
                                await conn_actor.redis_delete.remote(lock_key)
                                cleaned_scheduler_locks += 1
                            except:
                                pass

                    logger.info(f"调度锁清理完成: 清理了 {cleaned_scheduler_locks} 个遗留调度锁")

                except Exception as scheduler_cleanup_error:
                    logger.error(f"清理调度锁时出错: {scheduler_cleanup_error}")

                # 清理遗留的临时文件
                try:
                    logger.info("开始清理遗留的临时文件...")
                    from .temp_file_manager import TempFileManager

                    temp_manager = TempFileManager(
                        redis_actor=conn_actor,
                        config=self.config,
                        logger=logger
                    )

                    cleanup_result = await temp_manager.startup_cleanup()
                    if 'error' not in cleanup_result:
                        logger.info(f"临时文件清理完成: Redis记录文件 {cleanup_result.get('redis_files', 0)} 个, "
                                  f"孤儿文件 {cleanup_result.get('orphan_files', 0)} 个")
                    else:
                        logger.error(f"临时文件清理失败: {cleanup_result['error']}")

                except Exception as temp_cleanup_error:
                    logger.error(f"临时文件清理出错: {temp_cleanup_error}")

            finally:
                await self.push_conn_actor(conn_actor)
                
        except Exception as e:
            logger.error(f"启动清理失败: {e}")
            logger.error(traceback.format_exc())

    async def _recover_orphaned_task(self, task_id: str) -> bool:
        """尝试恢复孤儿任务
        
        Args:
            task_id: 任务ID，格式如 concat_daily:2024-01-15:ESN123456
            
        Returns:
            是否成功恢复任务
        """
        try:
            # 解析任务ID
            parts = task_id.split(":")
            if len(parts) < 3:
                logger.warning(f"无法解析任务ID格式: {task_id}")
                return False
            
            task_type = parts[0]
            
            if task_type == "concat_daily":
                # 午夜合并任务: concat_daily:date:esn
                if len(parts) >= 3:
                    date = parts[1]
                    esn = parts[2]
                    
                    # 重新生成任务
                    task = {
                        "task_id": task_id,
                        "task_type": "concat_daily",
                        "date": date,
                        "esn": esn,
                        "create_time": time.time(),
                        "retry_count": 0,
                        "recovered": True  # 标记为恢复的任务
                    }
                    
                    # 重新加入队列，但跳过去重检查（因为是恢复任务）
                    await self._redis_queue_push_force(task)
                    logger.info(f"恢复午夜合并任务: {task_id}")
                    return True
                    
            elif task_type == "progressive_merge":
                # 渐进式合并任务: progressive_merge:date:time_window:esn
                if len(parts) >= 4:
                    date = parts[1]
                    time_window = parts[2]
                    esn = parts[3]
                    
                    task = {
                        "task_id": task_id,
                        "task_type": "progressive_merge", 
                        "date": date,
                        "esn": esn,
                        "time_window": time_window,
                        "create_time": time.time(),
                        "retry_count": 0,
                        "recovered": True
                    }
                    
                    await self._redis_queue_push_force(task)
                    logger.info(f"恢复渐进式合并任务: {task_id}")
                    return True
                    
            elif task_type == "concat_timerange":
                # 时间范围合并任务: concat_timerange:date:start_time:end_time:esn
                if len(parts) >= 5:
                    date = parts[1]
                    start_time = parts[2]
                    end_time = parts[3]
                    esn = parts[4]
                    
                    task = {
                        "task_id": task_id,
                        "task_type": "concat_timerange",
                        "start_time": start_time,
                        "end_time": end_time,
                        "date": date,
                        "esn": esn,
                        "create_time": time.time(),
                        "retry_count": 0,
                        "recovered": True
                    }
                    
                    await self._redis_queue_push_force(task)
                    logger.info(f"恢复时间范围合并任务: {task_id}")
                    return True
            
            logger.warning(f"不支持的任务类型或格式错误: {task_id}")
            return False
            
        except Exception as e:
            logger.error(f"恢复任务失败 {task_id}: {e}")
            return False

    async def _redis_queue_push_force(self, message):
        """强制将任务推入队列，跳过去重检查（用于任务恢复）"""
        try:
            conn_actor = await self.get_redis_actor()
            try:
                message_json = json.dumps(message)
                await conn_actor.redis_lpush.remote(self.redis_queue_key, message_json)
                logger.info(f"强制推入队列（恢复任务）: {message.get('task_id', 'unknown')}")
            finally:
                await self.push_conn_actor(conn_actor)
        except Exception as e:
            logger.error(f"强制推入队列失败: {e}")
            raise

    async def _monitor_stuck_tasks(self):
        """监控卡住的任务，定期检查和恢复"""
        logger.info("启动任务监控器...")

        while True:
            try:
                await asyncio.sleep(180)  # 每3分钟检查一次，更频繁地检测任务状态
                
                conn_actor = await self.get_redis_actor()
                try:
                    # 检查所有处理中的任务锁（包括处理锁和队列锁）
                    processing_lock_pattern = "video:processing_tasks:*"
                    queue_lock_pattern = "video:queue_locks:*"
                    processing_lock_keys = await conn_actor.redis_keys.remote(processing_lock_pattern)
                    queue_lock_keys = await conn_actor.redis_keys.remote(queue_lock_pattern)
                    lock_keys = processing_lock_keys + queue_lock_keys
                    
                    stuck_tasks = 0
                    recovered_tasks = 0
                    
                    for lock_key in lock_keys:
                        try:
                            # 检查锁的剩余时间
                            ttl = await conn_actor.redis_ttl.remote(lock_key)
                            
                            if ttl > 0:
                                lock_age = 900 - ttl  # 锁已存在的时间（15分钟TTL）
                                
                                # 如果锁存在超过10分钟（接近过期），认为任务可能卡住了
                                if lock_age > 600:  # 10分钟
                                    # 提取任务ID（支持两种锁类型）
                                    if lock_key.startswith("video:processing_tasks:"):
                                        task_id = lock_key.replace("video:processing_tasks:", "")
                                    elif lock_key.startswith("video:queue_locks:"):
                                        task_id = lock_key.replace("video:queue_locks:", "")
                                    else:
                                        continue
                                    stuck_tasks += 1

                                    logger.warning(f"检测到疑似卡住的任务: {task_id}，已运行 {lock_age} 秒")

                                    # 缩短任务超时时间，更快地检测到异常中断的任务
                                    if lock_age > 600:  # 10分钟（从15分钟缩短）
                                        logger.warning(f"检测到可能异常中断的任务: {task_id}，已运行 {lock_age} 秒")

                                        # 检查任务是否真的异常中断（通过检查实例健康状态）
                                        try:
                                            # 尝试获取任务的实例信息
                                            instance_info = await conn_actor.redis_hget.remote(f"task_instance:{task_id}", "instance_id")

                                            if instance_info:
                                                # 检查实例是否还活着
                                                instance_heartbeat = await conn_actor.redis_get.remote(f"instance_heartbeat:{instance_info}")
                                                current_time = time.time()

                                                if not instance_heartbeat or (current_time - float(instance_heartbeat)) > 300:  # 5分钟无心跳
                                                    logger.error(f"实例 {instance_info} 已失联，强制恢复任务: {task_id}")
                                                    force_recover = True
                                                else:
                                                    logger.info(f"实例 {instance_info} 仍活跃，任务 {task_id} 可能正在正常处理")
                                                    force_recover = False
                                            else:
                                                # 没有实例信息，可能是旧任务，强制恢复
                                                logger.warning(f"任务 {task_id} 没有实例信息，强制恢复")
                                                force_recover = True

                                            if force_recover:
                                                # 删除锁
                                                await conn_actor.redis_delete.remote(lock_key)
                                                # 从处理集合中移除
                                                await conn_actor.redis_srem.remote("video:processing_tasks", task_id)
                                                # 清理实例信息
                                                await conn_actor.redis_delete.remote(f"task_instance:{task_id}")

                                                # 重新将任务加入队列，增加重试计数
                                                if task_id.startswith("concat_daily:"):
                                                    parts = task_id.split(":")
                                                    if len(parts) >= 3:
                                                        date = parts[1]
                                                        esn = parts[2]
                                                        is_catchup = len(parts) > 3 and parts[3] == "catchup"

                                                        retry_task = {
                                                            "task_id": task_id,
                                                            "task_type": "concat_daily",
                                                            "date": date,
                                                            "esn": esn,
                                                            "create_time": time.time(),
                                                            "retry_count": 1,  # 标记为重试任务
                                                            "recovered": True,  # 标记为恢复任务
                                                            "is_catchup": is_catchup,  # 保持兜底任务标识
                                                            "recovery_reason": "instance_lost"
                                                        }
                                                        await self._redis_queue_push_force(retry_task)
                                                        recovered_tasks += 1
                                                        logger.warning(f"已恢复失联实例的任务到队列: {task_id}")
                                        except Exception as recovery_error:
                                            logger.error(f"恢复任务 {task_id} 时出错: {recovery_error}")
                                    
                        except Exception as e:
                            logger.error(f"检查锁 {lock_key} 时出错: {e}")
                    
                    if stuck_tasks > 0:
                        logger.warning(f"当前有 {stuck_tasks} 个疑似卡住的任务")
                    else:
                        logger.debug("所有任务运行正常")

                    if recovered_tasks > 0:
                        logger.warning(f"本轮恢复了 {recovered_tasks} 个异常中断的任务")
                        # 上报恢复任务的指标
                        await self._report_recovery_metrics(recovered_tasks)

                    # 额外检查：查找可能被遗漏的任务
                    await self._check_orphaned_tasks()

                finally:
                    await self.push_conn_actor(conn_actor)

            except Exception as e:
                logger.error(f"任务监控出错: {e}")
                await asyncio.sleep(60)  # 出错时1分钟后重试

    async def _check_orphaned_tasks(self):
        """检查可能被遗漏的孤儿任务"""
        try:
            conn_actor = await self.get_redis_actor()
            try:
                # 获取队列中的任务数量
                queue_size = await conn_actor.redis_llen.remote(self.redis_queue_key)

                # 获取正在处理的任务数量
                processing_count = await conn_actor.redis_scard.remote("video:processing_tasks")

                # 如果队列为空且没有正在处理的任务，检查是否应该有任务
                if queue_size == 0 and processing_count == 0:
                    current_time = datetime.now(self.shanghai_tz)
                    current_hour = current_time.hour

                    # 只在特定时间段进行孤儿任务检查，避免过度检查
                    if current_hour in [1, 7, 13, 19]:  # 每6小时检查一次
                        logger.info("执行孤儿任务检查...")

            finally:
                await self.push_conn_actor(conn_actor)

        except Exception as e:
            logger.error(f"检查孤儿任务时出错: {e}")

    async def _report_recovery_metrics(self, recovered_count):
        """上报任务恢复指标"""
        try:
            metrics_data = {
                "metric_name": "task_recovery",
                "value": recovered_count,
                "timestamp": time.time(),
                "instance_id": self.instance_id,
                "tags": {
                    "service": "concat_video",
                    "type": "recovery"
                }
            }

            logger.warning(f"任务恢复指标: 恢复了 {recovered_count} 个任务")

        except Exception as e:
            logger.error(f"上报恢复指标失败: {e}")

    async def _instance_heartbeat(self):
        """实例心跳机制，定期更新实例状态"""
        logger.info("启动实例心跳机制...")

        while True:
            try:
                await asyncio.sleep(30)  # 每30秒发送一次心跳

                conn_actor = await self.get_redis_actor()
                try:
                    # 更新实例心跳时间戳
                    await conn_actor.redis_set.remote(
                        f"instance_heartbeat:{self.instance_id}",
                        time.time(),
                        300  # 5分钟过期
                    )

                    # 记录实例状态信息
                    instance_status = {
                        "instance_id": self.instance_id,
                        "last_heartbeat": time.time(),
                        "status": "active",
                        "queue_size": await self._get_queue_size(),
                        "processing_tasks": await self._get_processing_tasks_count()
                    }

                    await conn_actor.redis_hset.remote(
                        f"instance_status:{self.instance_id}",
                        "status",
                        json.dumps(instance_status),
                        300  # 5分钟过期
                    )

                finally:
                    await self.push_conn_actor(conn_actor)

            except Exception as e:
                logger.error(f"实例心跳出错: {e}")
                await asyncio.sleep(10)  # 出错时等待较短时间再重试

    async def _get_processing_tasks_count(self):
        """获取当前正在处理的任务数量"""
        try:
            conn_actor = await self.get_redis_actor()
            try:
                count = await conn_actor.redis_scard.remote("video:processing_tasks")
                return count if count else 0
            finally:
                await self.push_conn_actor(conn_actor)
        except Exception as e:
            logger.error(f"获取处理任务数量失败: {e}")
            return 0

    async def _get_queue_size(self):
        """获取当前队列中的任务数量"""
        try:
            conn_actor = await self.get_redis_actor()
            try:
                size = await conn_actor.redis_llen.remote(self.redis_queue_key)
                return size if size else 0
            finally:
                await self.push_conn_actor(conn_actor)
        except Exception as e:
            logger.error(f"获取队列大小失败: {e}")
            return 0

    def _setup_graceful_shutdown(self):
        """设置优雅关闭机制（Ray Serve兼容版本）"""
        # 在Ray Serve中不能使用信号处理器，因为不在主线程
        # 改为依赖Ray Serve的生命周期管理和__del__方法
        logger.info("优雅关闭机制已设置（Ray Serve兼容模式）")

    async def _graceful_shutdown(self):
        """优雅关闭处理（简化版本）"""
        try:
            logger.warning("开始优雅关闭流程...")

            # 设置关闭标志
            self.shutdown_flag = getattr(self, 'shutdown_flag', True)

            # 等待当前任务完成（最多等待5分钟）
            wait_time = 0
            max_wait = 300  # 5分钟

            while wait_time < max_wait:
                try:
                    processing_count = await self._get_processing_tasks_count()
                    if processing_count == 0:
                        break
                    logger.info(f"等待 {processing_count} 个任务完成...")
                    await asyncio.sleep(10)
                    wait_time += 10
                except Exception as e:
                    logger.error(f"检查任务状态时出错: {e}")
                    break

            # 清理实例资源
            try:
                conn_actor = await self.get_redis_actor()
                try:
                    await conn_actor.redis_delete.remote(f"instance_heartbeat:{self.instance_id}")
                    await conn_actor.redis_delete.remote(f"instance_status:{self.instance_id}")
                    logger.info("已清理实例资源")
                finally:
                    await self.push_conn_actor(conn_actor)
            except Exception as e:
                logger.error(f"清理实例资源失败: {e}")

            logger.warning("优雅关闭完成")

        except Exception as e:
            logger.error(f"优雅关闭过程中出错: {e}")

def create_app(config_path: str = None):
    """启动服务
    
    Args:
        config_path: 配置文件路径,如果不指定则根据DEPLOY_MODE加载对应配置
    """
    deployment = ConcatVideoService.bind()
    return deployment

app = create_app()

