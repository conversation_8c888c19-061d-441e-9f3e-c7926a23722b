# Prompts for Image Classification

Below are the class names and templates that are used for collecting the zero-shot classification scores in the paper. Each dataset has two lists `classes` and `templates`, where the string `{}` in the template is to be replaced with the corresponding class names. For the Facial Emotion Recognition 2013 dataset specifically, we used multiple class names for certain classes.

This file contains prompt data for 26 of the 27 datasets shown in Table 9 of the paper; the text prompts for ImageNet (as well as other [ImageNet Testbed](https://modestyachts.github.io/imagenet-testbed/) datasets in Figure 13) can be found in [this notebook](https://github.com/openai/CLIP/blob/main/notebooks/Prompt_Engineering_for_ImageNet.ipynb), as well as how to ensemble predictions from multiple prompts using these templates.

If you are viewing this document on GitHub, use the table of contents icon at the upper left to browse the datasets.

## Birdsnap

```bash
classes = [
'Acadian Flycatcher',
'Acorn Woodpecker',
'Alder Flycatcher',
'Allens Hummingbird',
'Altamira Oriole',
'American Avocet',
'American Bittern',
'American Black Duck',
'American Coot',
'American Crow',
'American Dipper',
'American Golden Plover',
'American Goldfinch',
'American Kestrel',
'American Oystercatcher',
'American Pipit',
'American Redstart',
'American Robin',
'American Three toed Woodpecker',
'American Tree Sparrow',
'American White Pelican',
'American Wigeon',
'American Woodcock',
'Anhinga',
'Annas Hummingbird',
'Arctic Tern',
'Ash throated Flycatcher',
'Audubons Oriole',
'Bairds Sandpiper',
'Bald Eagle',
'Baltimore Oriole',
'Band tailed Pigeon',
'Barn Swallow',
'Barred Owl',
'Barrows Goldeneye',
'Bay breasted Warbler',
'Bells Vireo',
'Belted Kingfisher',
'Bewicks Wren',
'Black Guillemot',
'Black Oystercatcher',
'Black Phoebe',
'Black Rosy Finch',
'Black Scoter',
'Black Skimmer',
'Black Tern',
'Black Turnstone',
'Black Vulture',
'Black and white Warbler',
'Black backed Woodpecker',
'Black bellied Plover',
'Black billed Cuckoo',
'Black billed Magpie',
'Black capped Chickadee',
'Black chinned Hummingbird',
'Black chinned Sparrow',
'Black crested Titmouse',
'Black crowned Night Heron',
'Black headed Grosbeak',
'Black legged Kittiwake',
'Black necked Stilt',
'Black throated Blue Warbler',
'Black throated Gray Warbler',
'Black throated Green Warbler',
'Black throated Sparrow',
'Blackburnian Warbler',
'Blackpoll Warbler',
'Blue Grosbeak',
'Blue Jay',
'Blue gray Gnatcatcher',
'Blue headed Vireo',
'Blue winged Teal',
'Blue winged Warbler',
'Boat tailed Grackle',
'Bobolink',
'Bohemian Waxwing',
'Bonapartes Gull',
'Boreal Chickadee',
'Brandts Cormorant',
'Brant',
'Brewers Blackbird',
'Brewers Sparrow',
'Bridled Titmouse',
'Broad billed Hummingbird',
'Broad tailed Hummingbird',
'Broad winged Hawk',
'Bronzed Cowbird',
'Brown Creeper',
'Brown Pelican',
'Brown Thrasher',
'Brown capped Rosy Finch',
'Brown crested Flycatcher',
'Brown headed Cowbird',
'Brown headed Nuthatch',
'Bufflehead',
'Bullocks Oriole',
'Burrowing Owl',
'Bushtit',
'Cackling Goose',
'Cactus Wren',
'California Gull',
'California Quail',
'California Thrasher',
'California Towhee',
'Calliope Hummingbird',
'Canada Goose',
'Canada Warbler',
'Canvasback',
'Canyon Towhee',
'Canyon Wren',
'Cape May Warbler',
'Carolina Chickadee',
'Carolina Wren',
'Caspian Tern',
'Cassins Finch',
'Cassins Kingbird',
'Cassins Sparrow',
'Cassins Vireo',
'Cattle Egret',
'Cave Swallow',
'Cedar Waxwing',
'Cerulean Warbler',
'Chestnut backed Chickadee',
'Chestnut collared Longspur',
'Chestnut sided Warbler',
'Chihuahuan Raven',
'Chimney Swift',
'Chipping Sparrow',
'Cinnamon Teal',
'Clapper Rail',
'Clarks Grebe',
'Clarks Nutcracker',
'Clay colored Sparrow',
'Cliff Swallow',
'Common Black Hawk',
'Common Eider',
'Common Gallinule',
'Common Goldeneye',
'Common Grackle',
'Common Ground Dove',
'Common Loon',
'Common Merganser',
'Common Murre',
'Common Nighthawk',
'Common Raven',
'Common Redpoll',
'Common Tern',
'Common Yellowthroat',
'Connecticut Warbler',
'Coopers Hawk',
'Cordilleran Flycatcher',
'Costas Hummingbird',
'Couchs Kingbird',
'Crested Caracara',
'Curve billed Thrasher',
'Dark eyed Junco',
'Dickcissel',
'Double crested Cormorant',
'Downy Woodpecker',
'Dunlin',
'Dusky Flycatcher',
'Dusky Grouse',
'Eared Grebe',
'Eastern Bluebird',
'Eastern Kingbird',
'Eastern Meadowlark',
'Eastern Phoebe',
'Eastern Screech Owl',
'Eastern Towhee',
'Eastern Wood Pewee',
'Elegant Trogon',
'Elf Owl',
'Eurasian Collared Dove',
'Eurasian Wigeon',
'European Starling',
'Evening Grosbeak',
'Ferruginous Hawk',
'Ferruginous Pygmy Owl',
'Field Sparrow',
'Fish Crow',
'Florida Scrub Jay',
'Forsters Tern',
'Fox Sparrow',
'Franklins Gull',
'Fulvous Whistling Duck',
'Gadwall',
'Gambels Quail',
'Gila Woodpecker',
'Glaucous Gull',
'Glaucous winged Gull',
'Glossy Ibis',
'Golden Eagle',
'Golden crowned Kinglet',
'Golden crowned Sparrow',
'Golden fronted Woodpecker',
'Golden winged Warbler',
'Grasshopper Sparrow',
'Gray Catbird',
'Gray Flycatcher',
'Gray Jay',
'Gray Kingbird',
'Gray cheeked Thrush',
'Gray crowned Rosy Finch',
'Great Black backed Gull',
'Great Blue Heron',
'Great Cormorant',
'Great Crested Flycatcher',
'Great Egret',
'Great Gray Owl',
'Great Horned Owl',
'Great Kiskadee',
'Great tailed Grackle',
'Greater Prairie Chicken',
'Greater Roadrunner',
'Greater Sage Grouse',
'Greater Scaup',
'Greater White fronted Goose',
'Greater Yellowlegs',
'Green Jay',
'Green tailed Towhee',
'Green winged Teal',
'Groove billed Ani',
'Gull billed Tern',
'Hairy Woodpecker',
'Hammonds Flycatcher',
'Harlequin Duck',
'Harriss Hawk',
'Harriss Sparrow',
'Heermanns Gull',
'Henslows Sparrow',
'Hepatic Tanager',
'Hermit Thrush',
'Herring Gull',
'Hoary Redpoll',
'Hooded Merganser',
'Hooded Oriole',
'Hooded Warbler',
'Horned Grebe',
'Horned Lark',
'House Finch',
'House Sparrow',
'House Wren',
'Huttons Vireo',
'Iceland Gull',
'Inca Dove',
'Indigo Bunting',
'Killdeer',
'King Rail',
'Ladder backed Woodpecker',
'Lapland Longspur',
'Lark Bunting',
'Lark Sparrow',
'Laughing Gull',
'Lazuli Bunting',
'Le Contes Sparrow',
'Least Bittern',
'Least Flycatcher',
'Least Grebe',
'Least Sandpiper',
'Least Tern',
'Lesser Goldfinch',
'Lesser Nighthawk',
'Lesser Scaup',
'Lesser Yellowlegs',
'Lewiss Woodpecker',
'Limpkin',
'Lincolns Sparrow',
'Little Blue Heron',
'Loggerhead Shrike',
'Long billed Curlew',
'Long billed Dowitcher',
'Long billed Thrasher',
'Long eared Owl',
'Long tailed Duck',
'Louisiana Waterthrush',
'Magnificent Frigatebird',
'Magnolia Warbler',
'Mallard',
'Marbled Godwit',
'Marsh Wren',
'Merlin',
'Mew Gull',
'Mexican Jay',
'Mississippi Kite',
'Monk Parakeet',
'Mottled Duck',
'Mountain Bluebird',
'Mountain Chickadee',
'Mountain Plover',
'Mourning Dove',
'Mourning Warbler',
'Muscovy Duck',
'Mute Swan',
'Nashville Warbler',
'Nelsons Sparrow',
'Neotropic Cormorant',
'Northern Bobwhite',
'Northern Cardinal',
'Northern Flicker',
'Northern Gannet',
'Northern Goshawk',
'Northern Harrier',
'Northern Hawk Owl',
'Northern Mockingbird',
'Northern Parula',
'Northern Pintail',
'Northern Rough winged Swallow',
'Northern Saw whet Owl',
'Northern Shrike',
'Northern Waterthrush',
'Nuttalls Woodpecker',
'Oak Titmouse',
'Olive Sparrow',
'Olive sided Flycatcher',
'Orange crowned Warbler',
'Orchard Oriole',
'Osprey',
'Ovenbird',
'Pacific Golden Plover',
'Pacific Loon',
'Pacific Wren',
'Pacific slope Flycatcher',
'Painted Bunting',
'Painted Redstart',
'Palm Warbler',
'Pectoral Sandpiper',
'Peregrine Falcon',
'Phainopepla',
'Philadelphia Vireo',
'Pied billed Grebe',
'Pigeon Guillemot',
'Pileated Woodpecker',
'Pine Grosbeak',
'Pine Siskin',
'Pine Warbler',
'Piping Plover',
'Plumbeous Vireo',
'Prairie Falcon',
'Prairie Warbler',
'Prothonotary Warbler',
'Purple Finch',
'Purple Gallinule',
'Purple Martin',
'Purple Sandpiper',
'Pygmy Nuthatch',
'Pyrrhuloxia',
'Red Crossbill',
'Red Knot',
'Red Phalarope',
'Red bellied Woodpecker',
'Red breasted Merganser',
'Red breasted Nuthatch',
'Red breasted Sapsucker',
'Red cockaded Woodpecker',
'Red eyed Vireo',
'Red headed Woodpecker',
'Red naped Sapsucker',
'Red necked Grebe',
'Red necked Phalarope',
'Red shouldered Hawk',
'Red tailed Hawk',
'Red throated Loon',
'Red winged Blackbird',
'Reddish Egret',
'Redhead',
'Ring billed Gull',
'Ring necked Duck',
'Ring necked Pheasant',
'Rock Pigeon',
'Rock Ptarmigan',
'Rock Sandpiper',
'Rock Wren',
'Rose breasted Grosbeak',
'Roseate Tern',
'Rosss Goose',
'Rough legged Hawk',
'Royal Tern',
'Ruby crowned Kinglet',
'Ruby throated Hummingbird',
'Ruddy Duck',
'Ruddy Turnstone',
'Ruffed Grouse',
'Rufous Hummingbird',
'Rufous crowned Sparrow',
'Rusty Blackbird',
'Sage Thrasher',
'Saltmarsh Sparrow',
'Sanderling',
'Sandhill Crane',
'Sandwich Tern',
'Says Phoebe',
'Scaled Quail',
'Scarlet Tanager',
'Scissor tailed Flycatcher',
'Scotts Oriole',
'Seaside Sparrow',
'Sedge Wren',
'Semipalmated Plover',
'Semipalmated Sandpiper',
'Sharp shinned Hawk',
'Sharp tailed Grouse',
'Short billed Dowitcher',
'Short eared Owl',
'Snail Kite',
'Snow Bunting',
'Snow Goose',
'Snowy Egret',
'Snowy Owl',
'Snowy Plover',
'Solitary Sandpiper',
'Song Sparrow',
'Sooty Grouse',
'Sora',
'Spotted Owl',
'Spotted Sandpiper',
'Spotted Towhee',
'Spruce Grouse',
'Stellers Jay',
'Stilt Sandpiper',
'Summer Tanager',
'Surf Scoter',
'Surfbird',
'Swainsons Hawk',
'Swainsons Thrush',
'Swallow tailed Kite',
'Swamp Sparrow',
'Tennessee Warbler',
'Thayers Gull',
'Townsends Solitaire',
'Townsends Warbler',
'Tree Swallow',
'Tricolored Heron',
'Tropical Kingbird',
'Trumpeter Swan',
'Tufted Titmouse',
'Tundra Swan',
'Turkey Vulture',
'Upland Sandpiper',
'Varied Thrush',
'Veery',
'Verdin',
'Vermilion Flycatcher',
'Vesper Sparrow',
'Violet green Swallow',
'Virginia Rail',
'Wandering Tattler',
'Warbling Vireo',
'Western Bluebird',
'Western Grebe',
'Western Gull',
'Western Kingbird',
'Western Meadowlark',
'Western Sandpiper',
'Western Screech Owl',
'Western Scrub Jay',
'Western Tanager',
'Western Wood Pewee',
'Whimbrel',
'White Ibis',
'White breasted Nuthatch',
'White crowned Sparrow',
'White eyed Vireo',
'White faced Ibis',
'White headed Woodpecker',
'White rumped Sandpiper',
'White tailed Hawk',
'White tailed Kite',
'White tailed Ptarmigan',
'White throated Sparrow',
'White throated Swift',
'White winged Crossbill',
'White winged Dove',
'White winged Scoter',
'Wild Turkey',
'Willet',
'Williamsons Sapsucker',
'Willow Flycatcher',
'Willow Ptarmigan',
'Wilsons Phalarope',
'Wilsons Plover',
'Wilsons Snipe',
'Wilsons Warbler',
'Winter Wren',
'Wood Stork',
'Wood Thrush',
'Worm eating Warbler',
'Wrentit',
'Yellow Warbler',
'Yellow bellied Flycatcher',
'Yellow bellied Sapsucker',
'Yellow billed Cuckoo',
'Yellow billed Magpie',
'Yellow breasted Chat',
'Yellow crowned Night Heron',
'Yellow eyed Junco',
'Yellow headed Blackbird',
'Yellow rumped Warbler',
'Yellow throated Vireo',
'Yellow throated Warbler',
'Zone tailed Hawk',
]

templates = [
'a photo of a {}, a type of bird.',
]
```

## CIFAR10

```bash
classes = [
'airplane',
'automobile',
'bird',
'cat',
'deer',
'dog',
'frog',
'horse',
'ship',
'truck',
]

templates = [
'a photo of a {}.',
'a blurry photo of a {}.',
'a black and white photo of a {}.',
'a low contrast photo of a {}.',
'a high contrast photo of a {}.',
'a bad photo of a {}.',
'a good photo of a {}.',
'a photo of a small {}.',
'a photo of a big {}.',
'a photo of the {}.',
'a blurry photo of the {}.',
'a black and white photo of the {}.',
'a low contrast photo of the {}.',
'a high contrast photo of the {}.',
'a bad photo of the {}.',
'a good photo of the {}.',
'a photo of the small {}.',
'a photo of the big {}.',
]
```

## CIFAR100

```bash
classes = [
'apple',
'aquarium fish',
'baby',
'bear',
'beaver',
'bed',
'bee',
'beetle',
'bicycle',
'bottle',
'bowl',
'boy',
'bridge',
'bus',
'butterfly',
'camel',
'can',
'castle',
'caterpillar',
'cattle',
'chair',
'chimpanzee',
'clock',
'cloud',
'cockroach',
'couch',
'crab',
'crocodile',
'cup',
'dinosaur',
'dolphin',
'elephant',
'flatfish',
'forest',
'fox',
'girl',
'hamster',
'house',
'kangaroo',
'keyboard',
'lamp',
'lawn mower',
'leopard',
'lion',
'lizard',
'lobster',
'man',
'maple tree',
'motorcycle',
'mountain',
'mouse',
'mushroom',
'oak tree',
'orange',
'orchid',
'otter',
'palm tree',
'pear',
'pickup truck',
'pine tree',
'plain',
'plate',
'poppy',
'porcupine',
'possum',
'rabbit',
'raccoon',
'ray',
'road',
'rocket',
'rose',
'sea',
'seal',
'shark',
'shrew',
'skunk',
'skyscraper',
'snail',
'snake',
'spider',
'squirrel',
'streetcar',
'sunflower',
'sweet pepper',
'table',
'tank',
'telephone',
'television',
'tiger',
'tractor',
'train',
'trout',
'tulip',
'turtle',
'wardrobe',
'whale',
'willow tree',
'wolf',
'woman',
'worm',
]

templates = [
'a photo of a {}.',
'a blurry photo of a {}.',
'a black and white photo of a {}.',
'a low contrast photo of a {}.',
'a high contrast photo of a {}.',
'a bad photo of a {}.',
'a good photo of a {}.',
'a photo of a small {}.',
'a photo of a big {}.',
'a photo of the {}.',
'a blurry photo of the {}.',
'a black and white photo of the {}.',
'a low contrast photo of the {}.',
'a high contrast photo of the {}.',
'a bad photo of the {}.',
'a good photo of the {}.',
'a photo of the small {}.',
'a photo of the big {}.',
]
```

## CLEVRCounts

```bash
classes = [
'10',
'3',
'4',
'5',
'6',
'7',
'8',
'9',
]

templates = [
'a photo of {} objects.',
]
```

## Caltech101

```bash
classes = [
'background',
'off-center face',
'centered face',
'leopard',
'motorbike',
'accordion',
'airplane',
'anchor',
'ant',
'barrel',
'bass',
'beaver',
'binocular',
'bonsai',
'brain',
'brontosaurus',
'buddha',
'butterfly',
'camera',
'cannon',
'side of a car',
'ceiling fan',
'cellphone',
'chair',
'chandelier',
'body of a cougar cat',
'face of a cougar cat',
'crab',
'crayfish',
'crocodile',
'head of a  crocodile',
'cup',
'dalmatian',
'dollar bill',
'dolphin',
'dragonfly',
'electric guitar',
'elephant',
'emu',
'euphonium',
'ewer',
'ferry',
'flamingo',
'head of a flamingo',
'garfield',
'gerenuk',
'gramophone',
'grand piano',
'hawksbill',
'headphone',
'hedgehog',
'helicopter',
'ibis',
'inline skate',
'joshua tree',
'kangaroo',
'ketch',
'lamp',
'laptop',
'llama',
'lobster',
'lotus',
'mandolin',
'mayfly',
'menorah',
'metronome',
'minaret',
'nautilus',
'octopus',
'okapi',
'pagoda',
'panda',
'pigeon',
'pizza',
'platypus',
'pyramid',
'revolver',
'rhino',
'rooster',
'saxophone',
'schooner',
'scissors',
'scorpion',
'sea horse',
'snoopy (cartoon beagle)',
'soccer ball',
'stapler',
'starfish',
'stegosaurus',
'stop sign',
'strawberry',
'sunflower',
'tick',
'trilobite',
'umbrella',
'watch',
'water lilly',
'wheelchair',
'wild cat',
'windsor chair',
'wrench',
'yin and yang symbol',
]

templates = [
'a photo of a {}.',
'a painting of a {}.',
'a plastic {}.',
'a sculpture of a {}.',
'a sketch of a {}.',
'a tattoo of a {}.',
'a toy {}.',
'a rendition of a {}.',
'a embroidered {}.',
'a cartoon {}.',
'a {} in a video game.',
'a plushie {}.',
'a origami {}.',
'art of a {}.',
'graffiti of a {}.',
'a drawing of a {}.',
'a doodle of a {}.',
'a photo of the {}.',
'a painting of the {}.',
'the plastic {}.',
'a sculpture of the {}.',
'a sketch of the {}.',
'a tattoo of the {}.',
'the toy {}.',
'a rendition of the {}.',
'the embroidered {}.',
'the cartoon {}.',
'the {} in a video game.',
'the plushie {}.',
'the origami {}.',
'art of the {}.',
'graffiti of the {}.',
'a drawing of the {}.',
'a doodle of the {}.',
]
```

## Country211

```bash
classes = [
'Andorra',
'United Arab Emirates',
'Afghanistan',
'Antigua and Barbuda',
'Anguilla',
'Albania',
'Armenia',
'Angola',
'Antarctica',
'Argentina',
'Austria',
'Australia',
'Aruba',
'Aland Islands',
'Azerbaijan',
'Bosnia and Herzegovina',
'Barbados',
'Bangladesh',
'Belgium',
'Burkina Faso',
'Bulgaria',
'Bahrain',
'Benin',
'Bermuda',
'Brunei Darussalam',
'Bolivia',
'Bonaire, Saint Eustatius and Saba',
'Brazil',
'Bahamas',
'Bhutan',
'Botswana',
'Belarus',
'Belize',
'Canada',
'DR Congo',
'Central African Republic',
'Switzerland',
"Cote d'Ivoire",
'Cook Islands',
'Chile',
'Cameroon',
'China',
'Colombia',
'Costa Rica',
'Cuba',
'Cabo Verde',
'Curacao',
'Cyprus',
'Czech Republic',
'Germany',
'Denmark',
'Dominica',
'Dominican Republic',
'Algeria',
'Ecuador',
'Estonia',
'Egypt',
'Spain',
'Ethiopia',
'Finland',
'Fiji',
'Falkland Islands',
'Faeroe Islands',
'France',
'Gabon',
'United Kingdom',
'Grenada',
'Georgia',
'French Guiana',
'Guernsey',
'Ghana',
'Gibraltar',
'Greenland',
'Gambia',
'Guadeloupe',
'Greece',
'South Georgia and South Sandwich Is.',
'Guatemala',
'Guam',
'Guyana',
'Hong Kong',
'Honduras',
'Croatia',
'Haiti',
'Hungary',
'Indonesia',
'Ireland',
'Israel',
'Isle of Man',
'India',
'Iraq',
'Iran',
'Iceland',
'Italy',
'Jersey',
'Jamaica',
'Jordan',
'Japan',
'Kenya',
'Kyrgyz Republic',
'Cambodia',
'St. Kitts and Nevis',
'North Korea',
'South Korea',
'Kuwait',
'Cayman Islands',
'Kazakhstan',
'Laos',
'Lebanon',
'St. Lucia',
'Liechtenstein',
'Sri Lanka',
'Liberia',
'Lithuania',
'Luxembourg',
'Latvia',
'Libya',
'Morocco',
'Monaco',
'Moldova',
'Montenegro',
'Saint-Martin',
'Madagascar',
'Macedonia',
'Mali',
'Myanmar',
'Mongolia',
'Macau',
'Martinique',
'Mauritania',
'Malta',
'Mauritius',
'Maldives',
'Malawi',
'Mexico',
'Malaysia',
'Mozambique',
'Namibia',
'New Caledonia',
'Nigeria',
'Nicaragua',
'Netherlands',
'Norway',
'Nepal',
'New Zealand',
'Oman',
'Panama',
'Peru',
'French Polynesia',
'Papua New Guinea',
'Philippines',
'Pakistan',
'Poland',
'Puerto Rico',
'Palestine',
'Portugal',
'Palau',
'Paraguay',
'Qatar',
'Reunion',
'Romania',
'Serbia',
'Russia',
'Rwanda',
'Saudi Arabia',
'Solomon Islands',
'Seychelles',
'Sudan',
'Sweden',
'Singapore',
'St. Helena',
'Slovenia',
'Svalbard and Jan Mayen Islands',
'Slovakia',
'Sierra Leone',
'San Marino',
'Senegal',
'Somalia',
'South Sudan',
'El Salvador',
'Sint Maarten',
'Syria',
'Eswatini',
'Togo',
'Thailand',
'Tajikistan',
'Timor-Leste',
'Turkmenistan',
'Tunisia',
'Tonga',
'Turkey',
'Trinidad and Tobago',
'Taiwan',
'Tanzania',
'Ukraine',
'Uganda',
'United States',
'Uruguay',
'Uzbekistan',
'Vatican',
'Venezuela',
'British Virgin Islands',
'United States Virgin Islands',
'Vietnam',
'Vanuatu',
'Samoa',
'Kosovo',
'Yemen',
'South Africa',
'Zambia',
'Zimbabwe',
]

templates = [
'a photo i took in {}.',
'a photo i took while visiting {}.',
'a photo from my home country of {}.',
'a photo from my visit to {}.',
'a photo showing the country of {}.',
]
```

## DescribableTextures

```bash
classes = [
'banded',
'blotchy',
'braided',
'bubbly',
'bumpy',
'chequered',
'cobwebbed',
'cracked',
'crosshatched',
'crystalline',
'dotted',
'fibrous',
'flecked',
'freckled',
'frilly',
'gauzy',
'grid',
'grooved',
'honeycombed',
'interlaced',
'knitted',
'lacelike',
'lined',
'marbled',
'matted',
'meshed',
'paisley',
'perforated',
'pitted',
'pleated',
'polka-dotted',
'porous',
'potholed',
'scaly',
'smeared',
'spiralled',
'sprinkled',
'stained',
'stratified',
'striped',
'studded',
'swirly',
'veined',
'waffled',
'woven',
'wrinkled',
'zigzagged',
]

templates = [
'a photo of a {} texture.',
'a photo of a {} pattern.',
'a photo of a {} thing.',
'a photo of a {} object.',
'a photo of the {} texture.',
'a photo of the {} pattern.',
'a photo of the {} thing.',
'a photo of the {} object.',
]
```

## EuroSAT

```bash
classes = [
'forest',
'permanent crop land',
'residential buildings or homes or apartments',
'river',
'pasture land',
'lake or sea',
'brushland or shrubland',
'annual crop land',
'industrial buildings or commercial buildings',
'highway or road',
]

templates = [
'a centered satellite photo of {}.',
'a centered satellite photo of a {}.',
'a centered satellite photo of the {}.',
]
```

## FGVCAircraft

```bash
classes = [
'707-320',
'727-200',
'737-200',
'737-300',
'737-400',
'737-500',
'737-600',
'737-700',
'737-800',
'737-900',
'747-100',
'747-200',
'747-300',
'747-400',
'757-200',
'757-300',
'767-200',
'767-300',
'767-400',
'777-200',
'777-300',
'A300B4',
'A310',
'A318',
'A319',
'A320',
'A321',
'A330-200',
'A330-300',
'A340-200',
'A340-300',
'A340-500',
'A340-600',
'A380',
'ATR-42',
'ATR-72',
'An-12',
'BAE 146-200',
'BAE 146-300',
'BAE-125',
'Beechcraft 1900',
'Boeing 717',
'C-130',
'C-47',
'CRJ-200',
'CRJ-700',
'CRJ-900',
'Cessna 172',
'Cessna 208',
'Cessna 525',
'Cessna 560',
'Challenger 600',
'DC-10',
'DC-3',
'DC-6',
'DC-8',
'DC-9-30',
'DH-82',
'DHC-1',
'DHC-6',
'DHC-8-100',
'DHC-8-300',
'DR-400',
'Dornier 328',
'E-170',
'E-190',
'E-195',
'EMB-120',
'ERJ 135',
'ERJ 145',
'Embraer Legacy 600',
'Eurofighter Typhoon',
'F-16A/B',
'F/A-18',
'Falcon 2000',
'Falcon 900',
'Fokker 100',
'Fokker 50',
'Fokker 70',
'Global Express',
'Gulfstream IV',
'Gulfstream V',
'Hawk T1',
'Il-76',
'L-1011',
'MD-11',
'MD-80',
'MD-87',
'MD-90',
'Metroliner',
'Model B200',
'PA-28',
'SR-20',
'Saab 2000',
'Saab 340',
'Spitfire',
'Tornado',
'Tu-134',
'Tu-154',
'Yak-42',
]

templates = [
'a photo of a {}, a type of aircraft.',
'a photo of the {}, a type of aircraft.',
]
```

## FacialEmotionRecognition2013

```bash
classes = [
['angry'],
['disgusted'],
['fearful'],
['happy', 'smiling'],
['sad', 'depressed'],
['surprised', 'shocked', 'spooked'],
['neutral', 'bored'],
]

templates = [
'a photo of a {} looking face.',
'a photo of a face showing the emotion: {}.',
'a photo of a face looking {}.',
'a face that looks {}.',
'they look {}.',
'look at how {} they are.',
]
```

## Flowers102

```bash
classes = [
'pink primrose',
'hard-leaved pocket orchid',
'canterbury bells',
'sweet pea',
'english marigold',
'tiger lily',
'moon orchid',
'bird of paradise',
'monkshood',
'globe thistle',
'snapdragon',
"colt's foot",
'king protea',
'spear thistle',
'yellow iris',
'globe flower',
'purple coneflower',
'peruvian lily',
'balloon flower',
'giant white arum lily',
'fire lily',
'pincushion flower',
'fritillary',
'red ginger',
'grape hyacinth',
'corn poppy',
'prince of wales feathers',
'stemless gentian',
'artichoke',
'sweet william',
'carnation',
'garden phlox',
'love in the mist',
'mexican aster',
'alpine sea holly',
'ruby-lipped cattleya',
'cape flower',
'great masterwort',
'siam tulip',
'lenten rose',
'barbeton daisy',
'daffodil',
'sword lily',
'poinsettia',
'bolero deep blue',
'wallflower',
'marigold',
'buttercup',
'oxeye daisy',
'common dandelion',
'petunia',
'wild pansy',
'primula',
'sunflower',
'pelargonium',
'bishop of llandaff',
'gaura',
'geranium',
'orange dahlia',
'pink and yellow dahlia',
'cautleya spicata',
'japanese anemone',
'black-eyed susan',
'silverbush',
'californian poppy',
'osteospermum',
'spring crocus',
'bearded iris',
'windflower',
'tree poppy',
'gazania',
'azalea',
'water lily',
'rose',
'thorn apple',
'morning glory',
'passion flower',
'lotus',
'toad lily',
'anthurium',
'frangipani',
'clematis',
'hibiscus',
'columbine',
'desert-rose',
'tree mallow',
'magnolia',
'cyclamen',
'watercress',
'canna lily',
'hippeastrum',
'bee balm',
'air plant',
'foxglove',
'bougainvillea',
'camellia',
'mallow',
'mexican petunia',
'bromelia',
'blanket flower',
'trumpet creeper',
'blackberry lily',
]

templates = [
'a photo of a {}, a type of flower.',
]
```

## Food101

```bash
classes = [
'apple pie',
'baby back ribs',
'baklava',
'beef carpaccio',
'beef tartare',
'beet salad',
'beignets',
'bibimbap',
'bread pudding',
'breakfast burrito',
'bruschetta',
'caesar salad',
'cannoli',
'caprese salad',
'carrot cake',
'ceviche',
'cheese plate',
'cheesecake',
'chicken curry',
'chicken quesadilla',
'chicken wings',
'chocolate cake',
'chocolate mousse',
'churros',
'clam chowder',
'club sandwich',
'crab cakes',
'creme brulee',
'croque madame',
'cup cakes',
'deviled eggs',
'donuts',
'dumplings',
'edamame',
'eggs benedict',
'escargots',
'falafel',
'filet mignon',
'fish and chips',
'foie gras',
'french fries',
'french onion soup',
'french toast',
'fried calamari',
'fried rice',
'frozen yogurt',
'garlic bread',
'gnocchi',
'greek salad',
'grilled cheese sandwich',
'grilled salmon',
'guacamole',
'gyoza',
'hamburger',
'hot and sour soup',
'hot dog',
'huevos rancheros',
'hummus',
'ice cream',
'lasagna',
'lobster bisque',
'lobster roll sandwich',
'macaroni and cheese',
'macarons',
'miso soup',
'mussels',
'nachos',
'omelette',
'onion rings',
'oysters',
'pad thai',
'paella',
'pancakes',
'panna cotta',
'peking duck',
'pho',
'pizza',
'pork chop',
'poutine',
'prime rib',
'pulled pork sandwich',
'ramen',
'ravioli',
'red velvet cake',
'risotto',
'samosa',
'sashimi',
'scallops',
'seaweed salad',
'shrimp and grits',
'spaghetti bolognese',
'spaghetti carbonara',
'spring rolls',
'steak',
'strawberry shortcake',
'sushi',
'tacos',
'takoyaki',
'tiramisu',
'tuna tartare',
'waffles',
]

templates = [
'a photo of {}, a type of food.',
]
```

## GTSRB

```bash
classes = [
'red and white circle 20 kph speed limit',
'red and white circle 30 kph speed limit',
'red and white circle 50 kph speed limit',
'red and white circle 60 kph speed limit',
'red and white circle 70 kph speed limit',
'red and white circle 80 kph speed limit',
'end / de-restriction of 80 kph speed limit',
'red and white circle 100 kph speed limit',
'red and white circle 120 kph speed limit',
'red and white circle red car and black car no passing',
'red and white circle red truck and black car no passing',
'red and white triangle road intersection warning',
'white and yellow diamond priority road',
'red and white upside down triangle yield right-of-way',
'stop',
'empty red and white circle',
'red and white circle no truck entry',
'red circle with white horizontal stripe no entry',
'red and white triangle with exclamation mark warning',
'red and white triangle with black left curve approaching warning',
'red and white triangle with black right curve approaching warning',
'red and white triangle with black double curve approaching warning',
'red and white triangle rough / bumpy road warning',
'red and white triangle car skidding / slipping warning',
'red and white triangle with merging / narrow lanes warning',
'red and white triangle with person digging / construction / road work warning',
'red and white triangle with traffic light approaching warning',
'red and white triangle with person walking warning',
'red and white triangle with child and person walking warning',
'red and white triangle with bicyle warning',
'red and white triangle with snowflake / ice warning',
'red and white triangle with deer warning',
'white circle with gray strike bar no speed limit',
'blue circle with white right turn arrow mandatory',
'blue circle with white left turn arrow mandatory',
'blue circle with white forward arrow mandatory',
'blue circle with white forward or right turn arrow mandatory',
'blue circle with white forward or left turn arrow mandatory',
'blue circle with white keep right arrow mandatory',
'blue circle with white keep left arrow mandatory',
'blue circle with white arrows indicating a traffic circle',
'white circle with gray strike bar indicating no passing for cars has ended',
'white circle with gray strike bar indicating no passing for trucks has ended',
]

templates = [
'a zoomed in photo of a "{}" traffic sign.',
'a centered photo of a "{}" traffic sign.',
'a close up photo of a "{}" traffic sign.',
]
```

## HatefulMemes

```bash
classes = [
'meme',
'hatespeech meme',
]

templates = [
'a {}.',
]
```

## KITTI

```bash
classes = [
'a photo i took of a car on my left or right side.',
'a photo i took with a car nearby.',
'a photo i took with a car in the distance.',
'a photo i took with no car.',
]

templates = [
'{}',
]
```

## Kinetics700

```bash
classes = [
'abseiling',
'acting in play',
'adjusting glasses',
'air drumming',
'alligator wrestling',
'answering questions',
'applauding',
'applying cream',
'archaeological excavation',
'archery',
'arguing',
'arm wrestling',
'arranging flowers',
'arresting',
'assembling bicycle',
'assembling computer',
'attending conference',
'auctioning',
'baby waking up',
'backflip (human)',
'baking cookies',
'bandaging',
'barbequing',
'bartending',
'base jumping',
'bathing dog',
'battle rope training',
'beatboxing',
'bee keeping',
'being excited',
'being in zero gravity',
'belly dancing',
'bench pressing',
'bending back',
'bending metal',
'biking through snow',
'blasting sand',
'blending fruit',
'blowdrying hair',
'blowing bubble gum',
'blowing glass',
'blowing leaves',
'blowing nose',
'blowing out candles',
'bobsledding',
'bodysurfing',
'bookbinding',
'bottling',
'bouncing ball (not juggling)',
'bouncing on bouncy castle',
'bouncing on trampoline',
'bowling',
'braiding hair',
'breading or breadcrumbing',
'breakdancing',
'breaking boards',
'breaking glass',
'breathing fire',
'brush painting',
'brushing floor',
'brushing hair',
'brushing teeth',
'building cabinet',
'building lego',
'building sandcastle',
'building shed',
'bulldozing',
'bungee jumping',
'burping',
'busking',
'calculating',
'calligraphy',
'canoeing or kayaking',
'capoeira',
'capsizing',
'card stacking',
'card throwing',
'carrying baby',
'carrying weight',
'cartwheeling',
'carving ice',
'carving marble',
'carving pumpkin',
'carving wood with a knife',
'casting fishing line',
'catching fish',
'catching or throwing baseball',
'catching or throwing frisbee',
'catching or throwing softball',
'celebrating',
'changing gear in car',
'changing oil',
'changing wheel (not on bike)',
'chasing',
'checking tires',
'checking watch',
'cheerleading',
'chewing gum',
'chiseling stone',
'chiseling wood',
'chopping meat',
'chopping wood',
'clam digging',
'clapping',
'clay pottery making',
'clean and jerk',
'cleaning gutters',
'cleaning pool',
'cleaning shoes',
'cleaning toilet',
'cleaning windows',
'climbing a rope',
'climbing ladder',
'climbing tree',
'closing door',
'coloring in',
'combing hair',
'contact juggling',
'contorting',
'cooking chicken',
'cooking egg',
'cooking on campfire',
'cooking sausages (not on barbeque)',
'cooking scallops',
'cosplaying',
'coughing',
'counting money',
'country line dancing',
'cracking back',
'cracking knuckles',
'cracking neck',
'crawling baby',
'crocheting',
'crossing eyes',
'crossing river',
'crying',
'cumbia',
'curling (sport)',
'curling eyelashes',
'curling hair',
'cutting apple',
'cutting cake',
'cutting nails',
'cutting orange',
'cutting pineapple',
'cutting watermelon',
'dancing ballet',
'dancing charleston',
'dancing gangnam style',
'dancing macarena',
'deadlifting',
'dealing cards',
'decorating the christmas tree',
'decoupage',
'delivering mail',
'digging',
'dining',
'directing traffic',
'disc golfing',
'diving cliff',
'docking boat',
'dodgeball',
'doing aerobics',
'doing jigsaw puzzle',
'doing laundry',
'doing nails',
'doing sudoku',
'drawing',
'dribbling basketball',
'drinking shots',
'driving car',
'driving tractor',
'drooling',
'drop kicking',
'drumming fingers',
'dumpster diving',
'dunking basketball',
'dyeing eyebrows',
'dyeing hair',
'eating burger',
'eating cake',
'eating carrots',
'eating chips',
'eating doughnuts',
'eating hotdog',
'eating ice cream',
'eating nachos',
'eating spaghetti',
'eating watermelon',
'egg hunting',
'embroidering',
'entering church',
'exercising arm',
'exercising with an exercise ball',
'extinguishing fire',
'faceplanting',
'falling off bike',
'falling off chair',
'feeding birds',
'feeding fish',
'feeding goats',
'fencing (sport)',
'fidgeting',
'filling cake',
'filling eyebrows',
'finger snapping',
'fixing bicycle',
'fixing hair',
'flint knapping',
'flipping bottle',
'flipping pancake',
'fly tying',
'flying kite',
'folding clothes',
'folding napkins',
'folding paper',
'front raises',
'frying vegetables',
'gargling',
'geocaching',
'getting a haircut',
'getting a piercing',
'getting a tattoo',
'giving or receiving award',
'gold panning',
'golf chipping',
'golf driving',
'golf putting',
'gospel singing in church',
'grinding meat',
'grooming cat',
'grooming dog',
'grooming horse',
'gymnastics tumbling',
'hammer throw',
'hand washing clothes',
'head stand',
'headbanging',
'headbutting',
'helmet diving',
'herding cattle',
'high fiving',
'high jump',
'high kick',
'historical reenactment',
'hitting baseball',
'hockey stop',
'holding snake',
'home roasting coffee',
'hopscotch',
'hoverboarding',
'huddling',
'hugging (not baby)',
'hugging baby',
'hula hooping',
'hurdling',
'hurling (sport)',
'ice climbing',
'ice fishing',
'ice skating',
'ice swimming',
'inflating balloons',
'installing carpet',
'ironing',
'ironing hair',
'javelin throw',
'jaywalking',
'jetskiing',
'jogging',
'juggling balls',
'juggling fire',
'juggling soccer ball',
'jumping bicycle',
'jumping into pool',
'jumping jacks',
'jumping sofa',
'jumpstyle dancing',
'karaoke',
'kicking field goal',
'kicking soccer ball',
'kissing',
'kitesurfing',
'knitting',
'krumping',
'land sailing',
'laughing',
'lawn mower racing',
'laying bricks',
'laying concrete',
'laying decking',
'laying stone',
'laying tiles',
'leatherworking',
'letting go of balloon',
'licking',
'lifting hat',
'lighting candle',
'lighting fire',
'listening with headphones',
'lock picking',
'long jump',
'longboarding',
'looking at phone',
'looking in mirror',
'luge',
'lunge',
'making a cake',
'making a sandwich',
'making balloon shapes',
'making bubbles',
'making cheese',
'making horseshoes',
'making jewelry',
'making latte art',
'making paper aeroplanes',
'making pizza',
'making slime',
'making snowman',
'making sushi',
'making tea',
'making the bed',
'marching',
'marriage proposal',
'massaging back',
'massaging feet',
'massaging legs',
'massaging neck',
"massaging person's head",
'metal detecting',
'milking cow',
'milking goat',
'mixing colours',
'moon walking',
'mopping floor',
'mosh pit dancing',
'motorcycling',
'mountain climber (exercise)',
'moving baby',
'moving child',
'moving furniture',
'mowing lawn',
'mushroom foraging',
'needle felting',
'news anchoring',
'opening bottle (not wine)',
'opening coconuts',
'opening door',
'opening present',
'opening refrigerator',
'opening wine bottle',
'packing',
'paragliding',
'parasailing',
'parkour',
'passing American football (in game)',
'passing American football (not in game)',
'passing soccer ball',
'peeling apples',
'peeling banana',
'peeling potatoes',
'person collecting garbage',
'petting animal (not cat)',
'petting cat',
'petting horse',
'photobombing',
'photocopying',
'picking apples',
'picking blueberries',
'pillow fight',
'pinching',
'pirouetting',
'planing wood',
'planting trees',
'plastering',
'playing accordion',
'playing american football',
'playing badminton',
'playing bagpipes',
'playing basketball',
'playing bass guitar',
'playing beer pong',
'playing billiards',
'playing blackjack',
'playing cards',
'playing cello',
'playing checkers',
'playing chess',
'playing clarinet',
'playing controller',
'playing cricket',
'playing cymbals',
'playing darts',
'playing didgeridoo',
'playing dominoes',
'playing drums',
'playing field hockey',
'playing flute',
'playing gong',
'playing guitar',
'playing hand clapping games',
'playing harmonica',
'playing harp',
'playing ice hockey',
'playing keyboard',
'playing kickball',
'playing laser tag',
'playing lute',
'playing mahjong',
'playing maracas',
'playing marbles',
'playing monopoly',
'playing netball',
'playing nose flute',
'playing oboe',
'playing ocarina',
'playing organ',
'playing paintball',
'playing pan pipes',
'playing piano',
'playing piccolo',
'playing pinball',
'playing ping pong',
'playing poker',
'playing polo',
'playing recorder',
'playing road hockey',
'playing rounders',
'playing rubiks cube',
'playing saxophone',
'playing scrabble',
'playing shuffleboard',
'playing slot machine',
'playing squash or racquetball',
'playing tennis',
'playing trombone',
'playing trumpet',
'playing ukulele',
'playing violin',
'playing volleyball',
'playing with trains',
'playing xylophone',
'poaching eggs',
'poking bellybutton',
'pole vault',
'polishing furniture',
'polishing metal',
'popping balloons',
'pouring beer',
'pouring milk',
'pouring wine',
'preparing salad',
'presenting weather forecast',
'pretending to be a statue',
'pull ups',
'pulling espresso shot',
'pulling rope (game)',
'pumping fist',
'pumping gas',
'punching bag',
'punching person (boxing)',
'push up',
'pushing car',
'pushing cart',
'pushing wheelbarrow',
'pushing wheelchair',
'putting in contact lenses',
'putting on eyeliner',
'putting on foundation',
'putting on lipstick',
'putting on mascara',
'putting on sari',
'putting on shoes',
'putting wallpaper on wall',
'raising eyebrows',
'reading book',
'reading newspaper',
'recording music',
'repairing puncture',
'riding a bike',
'riding camel',
'riding elephant',
'riding mechanical bull',
'riding mule',
'riding or walking with horse',
'riding scooter',
'riding snow blower',
'riding unicycle',
'ripping paper',
'roasting marshmallows',
'roasting pig',
'robot dancing',
'rock climbing',
'rock scissors paper',
'roller skating',
'rolling eyes',
'rolling pastry',
'rope pushdown',
'running on treadmill',
'sailing',
'salsa dancing',
'saluting',
'sanding floor',
'sanding wood',
'sausage making',
'sawing wood',
'scrambling eggs',
'scrapbooking',
'scrubbing face',
'scuba diving',
'seasoning food',
'separating eggs',
'setting table',
'sewing',
'shaking hands',
'shaking head',
'shaping bread dough',
'sharpening knives',
'sharpening pencil',
'shaving head',
'shaving legs',
'shearing sheep',
'shining flashlight',
'shining shoes',
'shoot dance',
'shooting basketball',
'shooting goal (soccer)',
'shooting off fireworks',
'shopping',
'shot put',
'shouting',
'shoveling snow',
'shredding paper',
'shucking oysters',
'shuffling cards',
'shuffling feet',
'side kick',
'sieving',
'sign language interpreting',
'silent disco',
'singing',
'sipping cup',
'situp',
'skateboarding',
'ski ballet',
'ski jumping',
'skiing crosscountry',
'skiing mono',
'skiing slalom',
'skipping rope',
'skipping stone',
'skydiving',
'slacklining',
'slapping',
'sled dog racing',
'sleeping',
'slicing onion',
'smashing',
'smelling feet',
'smoking',
'smoking hookah',
'smoking pipe',
'snatch weight lifting',
'sneezing',
'snorkeling',
'snowboarding',
'snowkiting',
'snowmobiling',
'somersaulting',
'spelunking',
'spinning plates',
'spinning poi',
'splashing water',
'spray painting',
'spraying',
'springboard diving',
'square dancing',
'squat',
'squeezing orange',
'stacking cups',
'stacking dice',
'standing on hands',
'staring',
'steer roping',
'steering car',
'sticking tongue out',
'stomping grapes',
'stretching arm',
'stretching leg',
'sucking lolly',
'surfing crowd',
'surfing water',
'surveying',
'sweeping floor',
'swimming backstroke',
'swimming breast stroke',
'swimming butterfly stroke',
'swimming front crawl',
'swimming with dolphins',
'swimming with sharks',
'swing dancing',
'swinging baseball bat',
'swinging on something',
'sword fighting',
'sword swallowing',
'tackling',
'tagging graffiti',
'tai chi',
'taking photo',
'talking on cell phone',
'tango dancing',
'tap dancing',
'tapping guitar',
'tapping pen',
'tasting beer',
'tasting food',
'tasting wine',
'testifying',
'texting',
'threading needle',
'throwing axe',
'throwing ball (not baseball or American football)',
'throwing discus',
'throwing knife',
'throwing snowballs',
'throwing tantrum',
'throwing water balloon',
'tickling',
'tie dying',
'tightrope walking',
'tiptoeing',
'tobogganing',
'tossing coin',
'tossing salad',
'training dog',
'trapezing',
'treating wood',
'trimming or shaving beard',
'trimming shrubs',
'trimming trees',
'triple jump',
'twiddling fingers',
'tying bow tie',
'tying knot (not on a tie)',
'tying necktie',
'tying shoe laces',
'unboxing',
'uncorking champagne',
'unloading truck',
'using a microscope',
'using a paint roller',
'using a power drill',
'using a sledge hammer',
'using a wrench',
'using atm',
'using bagging machine',
'using circular saw',
'using inhaler',
'using megaphone',
'using puppets',
'using remote controller (not gaming)',
'using segway',
'vacuuming car',
'vacuuming floor',
'visiting the zoo',
'wading through mud',
'wading through water',
'waiting in line',
'waking up',
'walking on stilts',
'walking the dog',
'walking through snow',
'walking with crutches',
'washing dishes',
'washing feet',
'washing hair',
'washing hands',
'watching tv',
'water skiing',
'water sliding',
'watering plants',
'waving hand',
'waxing armpits',
'waxing back',
'waxing chest',
'waxing eyebrows',
'waxing legs',
'weaving basket',
'weaving fabric',
'welding',
'whistling',
'windsurfing',
'winking',
'wood burning (art)',
'wrapping present',
'wrestling',
'writing',
'yarn spinning',
'yawning',
'yoga',
'zumba'
]

templates = [
'a photo of {}.',
'a photo of a person {}.',
'a photo of a person using {}.',
'a photo of a person doing {}.',
'a photo of a person during {}.',
'a photo of a person performing {}.',
'a photo of a person practicing {}.',
'a video of {}.',
'a video of a person {}.',
'a video of a person using {}.',
'a video of a person doing {}.',
'a video of a person during {}.',
'a video of a person performing {}.',
'a video of a person practicing {}.',
'a example of {}.',
'a example of a person {}.',
'a example of a person using {}.',
'a example of a person doing {}.',
'a example of a person during {}.',
'a example of a person performing {}.',
'a example of a person practicing {}.',
'a demonstration of {}.',
'a demonstration of a person {}.',
'a demonstration of a person using {}.',
'a demonstration of a person doing {}.',
'a demonstration of a person during {}.',
'a demonstration of a person performing {}.',
'a demonstration of a person practicing {}.',
]
```

## MNIST

```bash
classes = [
'0',
'1',
'2',
'3',
'4',
'5',
'6',
'7',
'8',
'9',
]

templates = [
'a photo of the number: "{}".',
]
```

## OxfordPets

```bash
classes = [
'Abyssinian',
'Bengal',
'Birman',
'Bombay',
'British Shorthair',
'Egyptian Mau',
'Maine Coon',
'Persian',
'Ragdoll',
'Russian Blue',
'Siamese',
'Sphynx',
'american bulldog',
'american pit bull terrier',
'basset hound',
'beagle',
'boxer',
'chihuahua',
'english cocker spaniel',
'english setter',
'german shorthaired',
'great pyrenees',
'havanese',
'japanese chin',
'keeshond',
'leonberger',
'miniature pinscher',
'newfoundland',
'pomeranian',
'pug',
'saint bernard',
'samoyed',
'scottish terrier',
'shiba inu',
'staffordshire bull terrier',
'wheaten terrier',
'yorkshire terrier',
]

templates = [
'a photo of a {}, a type of pet.',
]
```

## PascalVOC2007

```bash
classes = [
'aeroplane',
'bicycle',
'bird',
'boat',
'bottle',
'bus',
'car',
'cat',
'chair',
'cow',
'dog',
'horse',
'motorbike',
'person',
'sheep',
'sofa',
'diningtable',
'pottedplant',
'train',
'tvmonitor',
]

templates = [
'a photo of a {}.',
]
```

## PatchCamelyon

```bash
classes = [
'lymph node',
'lymph node containing metastatic tumor tissue',
]

templates = [
'this is a photo of {}',
]
```

## RESISC45

```bash
classes = [
'airplane',
'airport',
'baseball diamond',
'basketball court',
'beach',
'bridge',
'chaparral',
'church',
'circular farmland',
'cloud',
'commercial area',
'dense residential',
'desert',
'forest',
'freeway',
'golf course',
'ground track field',
'harbor',
'industrial area',
'intersection',
'island',
'lake',
'meadow',
'medium residential',
'mobile home park',
'mountain',
'overpass',
'palace',
'parking lot',
'railway',
'railway station',
'rectangular farmland',
'river',
'roundabout',
'runway',
'sea ice',
'ship',
'snowberg',
'sparse residential',
'stadium',
'storage tank',
'tennis court',
'terrace',
'thermal power station',
'wetland',
]

templates = [
'satellite imagery of {}.',
'aerial imagery of {}.',
'satellite photo of {}.',
'aerial photo of {}.',
'satellite view of {}.',
'aerial view of {}.',
'satellite imagery of a {}.',
'aerial imagery of a {}.',
'satellite photo of a {}.',
'aerial photo of a {}.',
'satellite view of a {}.',
'aerial view of a {}.',
'satellite imagery of the {}.',
'aerial imagery of the {}.',
'satellite photo of the {}.',
'aerial photo of the {}.',
'satellite view of the {}.',
'aerial view of the {}.',
]
```

## SST2

```bash
classes = [
'negative',
'positive',
]

templates = [
'a {} review of a movie.',
]
```

## STL10

```bash
classes = [
'airplane',
'bird',
'car',
'cat',
'deer',
'dog',
'horse',
'monkey',
'ship',
'truck',
]

templates = [
'a photo of a {}.',
'a photo of the {}.',
]
```

## SUN397

```bash
classes = [
'abbey',
'airplane cabin',
'airport terminal',
'alley',
'amphitheater',
'amusement arcade',
'amusement park',
'anechoic chamber',
'apartment building outdoor',
'apse indoor',
'aquarium',
'aqueduct',
'arch',
'archive',
'arrival gate outdoor',
'art gallery',
'art school',
'art studio',
'assembly line',
'athletic field outdoor',
'atrium public',
'attic',
'auditorium',
'auto factory',
'badlands',
'badminton court indoor',
'baggage claim',
'bakery shop',
'balcony exterior',
'balcony interior',
'ball pit',
'ballroom',
'bamboo forest',
'banquet hall',
'bar',
'barn',
'barndoor',
'baseball field',
'basement',
'basilica',
'basketball court outdoor',
'bathroom',
'batters box',
'bayou',
'bazaar indoor',
'bazaar outdoor',
'beach',
'beauty salon',
'bedroom',
'berth',
'biology laboratory',
'bistro indoor',
'boardwalk',
'boat deck',
'boathouse',
'bookstore',
'booth indoor',
'botanical garden',
'bow window indoor',
'bow window outdoor',
'bowling alley',
'boxing ring',
'brewery indoor',
'bridge',
'building facade',
'bullring',
'burial chamber',
'bus interior',
'butchers shop',
'butte',
'cabin outdoor',
'cafeteria',
'campsite',
'campus',
'canal natural',
'canal urban',
'candy store',
'canyon',
'car interior backseat',
'car interior frontseat',
'carrousel',
'casino indoor',
'castle',
'catacomb',
'cathedral indoor',
'cathedral outdoor',
'cavern indoor',
'cemetery',
'chalet',
'cheese factory',
'chemistry lab',
'chicken coop indoor',
'chicken coop outdoor',
'childs room',
'church indoor',
'church outdoor',
'classroom',
'clean room',
'cliff',
'cloister indoor',
'closet',
'clothing store',
'coast',
'cockpit',
'coffee shop',
'computer room',
'conference center',
'conference room',
'construction site',
'control room',
'control tower outdoor',
'corn field',
'corral',
'corridor',
'cottage garden',
'courthouse',
'courtroom',
'courtyard',
'covered bridge exterior',
'creek',
'crevasse',
'crosswalk',
'cubicle office',
'dam',
'delicatessen',
'dentists office',
'desert sand',
'desert vegetation',
'diner indoor',
'diner outdoor',
'dinette home',
'dinette vehicle',
'dining car',
'dining room',
'discotheque',
'dock',
'doorway outdoor',
'dorm room',
'driveway',
'driving range outdoor',
'drugstore',
'electrical substation',
'elevator door',
'elevator interior',
'elevator shaft',
'engine room',
'escalator indoor',
'excavation',
'factory indoor',
'fairway',
'fastfood restaurant',
'field cultivated',
'field wild',
'fire escape',
'fire station',
'firing range indoor',
'fishpond',
'florist shop indoor',
'food court',
'forest broadleaf',
'forest needleleaf',
'forest path',
'forest road',
'formal garden',
'fountain',
'galley',
'game room',
'garage indoor',
'garbage dump',
'gas station',
'gazebo exterior',
'general store indoor',
'general store outdoor',
'gift shop',
'golf course',
'greenhouse indoor',
'greenhouse outdoor',
'gymnasium indoor',
'hangar indoor',
'hangar outdoor',
'harbor',
'hayfield',
'heliport',
'herb garden',
'highway',
'hill',
'home office',
'hospital',
'hospital room',
'hot spring',
'hot tub outdoor',
'hotel outdoor',
'hotel room',
'house',
'hunting lodge outdoor',
'ice cream parlor',
'ice floe',
'ice shelf',
'ice skating rink indoor',
'ice skating rink outdoor',
'iceberg',
'igloo',
'industrial area',
'inn outdoor',
'islet',
'jacuzzi indoor',
'jail cell',
'jail indoor',
'jewelry shop',
'kasbah',
'kennel indoor',
'kennel outdoor',
'kindergarden classroom',
'kitchen',
'kitchenette',
'labyrinth outdoor',
'lake natural',
'landfill',
'landing deck',
'laundromat',
'lecture room',
'library indoor',
'library outdoor',
'lido deck outdoor',
'lift bridge',
'lighthouse',
'limousine interior',
'living room',
'lobby',
'lock chamber',
'locker room',
'mansion',
'manufactured home',
'market indoor',
'market outdoor',
'marsh',
'martial arts gym',
'mausoleum',
'medina',
'moat water',
'monastery outdoor',
'mosque indoor',
'mosque outdoor',
'motel',
'mountain',
'mountain snowy',
'movie theater indoor',
'museum indoor',
'music store',
'music studio',
'nuclear power plant outdoor',
'nursery',
'oast house',
'observatory outdoor',
'ocean',
'office',
'office building',
'oil refinery outdoor',
'oilrig',
'operating room',
'orchard',
'outhouse outdoor',
'pagoda',
'palace',
'pantry',
'park',
'parking garage indoor',
'parking garage outdoor',
'parking lot',
'parlor',
'pasture',
'patio',
'pavilion',
'pharmacy',
'phone booth',
'physics laboratory',
'picnic area',
'pilothouse indoor',
'planetarium outdoor',
'playground',
'playroom',
'plaza',
'podium indoor',
'podium outdoor',
'pond',
'poolroom establishment',
'poolroom home',
'power plant outdoor',
'promenade deck',
'pub indoor',
'pulpit',
'putting green',
'racecourse',
'raceway',
'raft',
'railroad track',
'rainforest',
'reception',
'recreation room',
'residential neighborhood',
'restaurant',
'restaurant kitchen',
'restaurant patio',
'rice paddy',
'riding arena',
'river',
'rock arch',
'rope bridge',
'ruin',
'runway',
'sandbar',
'sandbox',
'sauna',
'schoolhouse',
'sea cliff',
'server room',
'shed',
'shoe shop',
'shopfront',
'shopping mall indoor',
'shower',
'skatepark',
'ski lodge',
'ski resort',
'ski slope',
'sky',
'skyscraper',
'slum',
'snowfield',
'squash court',
'stable',
'stadium baseball',
'stadium football',
'stage indoor',
'staircase',
'street',
'subway interior',
'subway station platform',
'supermarket',
'sushi bar',
'swamp',
'swimming pool indoor',
'swimming pool outdoor',
'synagogue indoor',
'synagogue outdoor',
'television studio',
'temple east asia',
'temple south asia',
'tennis court indoor',
'tennis court outdoor',
'tent outdoor',
'theater indoor procenium',
'theater indoor seats',
'thriftshop',
'throne room',
'ticket booth',
'toll plaza',
'topiary garden',
'tower',
'toyshop',
'track outdoor',
'train railway',
'train station platform',
'tree farm',
'tree house',
'trench',
'underwater coral reef',
'utility room',
'valley',
'van interior',
'vegetable garden',
'veranda',
'veterinarians office',
'viaduct',
'videostore',
'village',
'vineyard',
'volcano',
'volleyball court indoor',
'volleyball court outdoor',
'waiting room',
'warehouse indoor',
'water tower',
'waterfall block',
'waterfall fan',
'waterfall plunge',
'watering hole',
'wave',
'wet bar',
'wheat field',
'wind farm',
'windmill',
'wine cellar barrel storage',
'wine cellar bottle storage',
'wrestling ring indoor',
'yard',
'youth hostel',
]

templates = [
'a photo of a {}.',
'a photo of the {}.',
]
```

## StanfordCars

```bash
classes = [
'AM General Hummer SUV 2000',
'Acura RL Sedan 2012',
'Acura TL Sedan 2012',
'Acura TL Type-S 2008',
'Acura TSX Sedan 2012',
'Acura Integra Type R 2001',
'Acura ZDX Hatchback 2012',
'Aston Martin V8 Vantage Convertible 2012',
'Aston Martin V8 Vantage Coupe 2012',
'Aston Martin Virage Convertible 2012',
'Aston Martin Virage Coupe 2012',
'Audi RS 4 Convertible 2008',
'Audi A5 Coupe 2012',
'Audi TTS Coupe 2012',
'Audi R8 Coupe 2012',
'Audi V8 Sedan 1994',
'Audi 100 Sedan 1994',
'Audi 100 Wagon 1994',
'Audi TT Hatchback 2011',
'Audi S6 Sedan 2011',
'Audi S5 Convertible 2012',
'Audi S5 Coupe 2012',
'Audi S4 Sedan 2012',
'Audi S4 Sedan 2007',
'Audi TT RS Coupe 2012',
'BMW ActiveHybrid 5 Sedan 2012',
'BMW 1 Series Convertible 2012',
'BMW 1 Series Coupe 2012',
'BMW 3 Series Sedan 2012',
'BMW 3 Series Wagon 2012',
'BMW 6 Series Convertible 2007',
'BMW X5 SUV 2007',
'BMW X6 SUV 2012',
'BMW M3 Coupe 2012',
'BMW M5 Sedan 2010',
'BMW M6 Convertible 2010',
'BMW X3 SUV 2012',
'BMW Z4 Convertible 2012',
'Bentley Continental Supersports Conv. Convertible 2012',
'Bentley Arnage Sedan 2009',
'Bentley Mulsanne Sedan 2011',
'Bentley Continental GT Coupe 2012',
'Bentley Continental GT Coupe 2007',
'Bentley Continental Flying Spur Sedan 2007',
'Bugatti Veyron 16.4 Convertible 2009',
'Bugatti Veyron 16.4 Coupe 2009',
'Buick Regal GS 2012',
'Buick Rainier SUV 2007',
'Buick Verano Sedan 2012',
'Buick Enclave SUV 2012',
'Cadillac CTS-V Sedan 2012',
'Cadillac SRX SUV 2012',
'Cadillac Escalade EXT Crew Cab 2007',
'Chevrolet Silverado 1500 Hybrid Crew Cab 2012',
'Chevrolet Corvette Convertible 2012',
'Chevrolet Corvette ZR1 2012',
'Chevrolet Corvette Ron Fellows Edition Z06 2007',
'Chevrolet Traverse SUV 2012',
'Chevrolet Camaro Convertible 2012',
'Chevrolet HHR SS 2010',
'Chevrolet Impala Sedan 2007',
'Chevrolet Tahoe Hybrid SUV 2012',
'Chevrolet Sonic Sedan 2012',
'Chevrolet Express Cargo Van 2007',
'Chevrolet Avalanche Crew Cab 2012',
'Chevrolet Cobalt SS 2010',
'Chevrolet Malibu Hybrid Sedan 2010',
'Chevrolet TrailBlazer SS 2009',
'Chevrolet Silverado 2500HD Regular Cab 2012',
'Chevrolet Silverado 1500 Classic Extended Cab 2007',
'Chevrolet Express Van 2007',
'Chevrolet Monte Carlo Coupe 2007',
'Chevrolet Malibu Sedan 2007',
'Chevrolet Silverado 1500 Extended Cab 2012',
'Chevrolet Silverado 1500 Regular Cab 2012',
'Chrysler Aspen SUV 2009',
'Chrysler Sebring Convertible 2010',
'Chrysler Town and Country Minivan 2012',
'Chrysler 300 SRT-8 2010',
'Chrysler Crossfire Convertible 2008',
'Chrysler PT Cruiser Convertible 2008',
'Daewoo Nubira Wagon 2002',
'Dodge Caliber Wagon 2012',
'Dodge Caliber Wagon 2007',
'Dodge Caravan Minivan 1997',
'Dodge Ram Pickup 3500 Crew Cab 2010',
'Dodge Ram Pickup 3500 Quad Cab 2009',
'Dodge Sprinter Cargo Van 2009',
'Dodge Journey SUV 2012',
'Dodge Dakota Crew Cab 2010',
'Dodge Dakota Club Cab 2007',
'Dodge Magnum Wagon 2008',
'Dodge Challenger SRT8 2011',
'Dodge Durango SUV 2012',
'Dodge Durango SUV 2007',
'Dodge Charger Sedan 2012',
'Dodge Charger SRT-8 2009',
'Eagle Talon Hatchback 1998',
'FIAT 500 Abarth 2012',
'FIAT 500 Convertible 2012',
'Ferrari FF Coupe 2012',
'Ferrari California Convertible 2012',
'Ferrari 458 Italia Convertible 2012',
'Ferrari 458 Italia Coupe 2012',
'Fisker Karma Sedan 2012',
'Ford F-450 Super Duty Crew Cab 2012',
'Ford Mustang Convertible 2007',
'Ford Freestar Minivan 2007',
'Ford Expedition EL SUV 2009',
'Ford Edge SUV 2012',
'Ford Ranger SuperCab 2011',
'Ford GT Coupe 2006',
'Ford F-150 Regular Cab 2012',
'Ford F-150 Regular Cab 2007',
'Ford Focus Sedan 2007',
'Ford E-Series Wagon Van 2012',
'Ford Fiesta Sedan 2012',
'GMC Terrain SUV 2012',
'GMC Savana Van 2012',
'GMC Yukon Hybrid SUV 2012',
'GMC Acadia SUV 2012',
'GMC Canyon Extended Cab 2012',
'Geo Metro Convertible 1993',
'HUMMER H3T Crew Cab 2010',
'HUMMER H2 SUT Crew Cab 2009',
'Honda Odyssey Minivan 2012',
'Honda Odyssey Minivan 2007',
'Honda Accord Coupe 2012',
'Honda Accord Sedan 2012',
'Hyundai Veloster Hatchback 2012',
'Hyundai Santa Fe SUV 2012',
'Hyundai Tucson SUV 2012',
'Hyundai Veracruz SUV 2012',
'Hyundai Sonata Hybrid Sedan 2012',
'Hyundai Elantra Sedan 2007',
'Hyundai Accent Sedan 2012',
'Hyundai Genesis Sedan 2012',
'Hyundai Sonata Sedan 2012',
'Hyundai Elantra Touring Hatchback 2012',
'Hyundai Azera Sedan 2012',
'Infiniti G Coupe IPL 2012',
'Infiniti QX56 SUV 2011',
'Isuzu Ascender SUV 2008',
'Jaguar XK XKR 2012',
'Jeep Patriot SUV 2012',
'Jeep Wrangler SUV 2012',
'Jeep Liberty SUV 2012',
'Jeep Grand Cherokee SUV 2012',
'Jeep Compass SUV 2012',
'Lamborghini Reventon Coupe 2008',
'Lamborghini Aventador Coupe 2012',
'Lamborghini Gallardo LP 570-4 Superleggera 2012',
'Lamborghini Diablo Coupe 2001',
'Land Rover Range Rover SUV 2012',
'Land Rover LR2 SUV 2012',
'Lincoln Town Car Sedan 2011',
'MINI Cooper Roadster Convertible 2012',
'Maybach Landaulet Convertible 2012',
'Mazda Tribute SUV 2011',
'McLaren MP4-12C Coupe 2012',
'Mercedes-Benz 300-Class Convertible 1993',
'Mercedes-Benz C-Class Sedan 2012',
'Mercedes-Benz SL-Class Coupe 2009',
'Mercedes-Benz E-Class Sedan 2012',
'Mercedes-Benz S-Class Sedan 2012',
'Mercedes-Benz Sprinter Van 2012',
'Mitsubishi Lancer Sedan 2012',
'Nissan Leaf Hatchback 2012',
'Nissan NV Passenger Van 2012',
'Nissan Juke Hatchback 2012',
'Nissan 240SX Coupe 1998',
'Plymouth Neon Coupe 1999',
'Porsche Panamera Sedan 2012',
'Ram C/V Cargo Van Minivan 2012',
'Rolls-Royce Phantom Drophead Coupe Convertible 2012',
'Rolls-Royce Ghost Sedan 2012',
'Rolls-Royce Phantom Sedan 2012',
'Scion xD Hatchback 2012',
'Spyker C8 Convertible 2009',
'Spyker C8 Coupe 2009',
'Suzuki Aerio Sedan 2007',
'Suzuki Kizashi Sedan 2012',
'Suzuki SX4 Hatchback 2012',
'Suzuki SX4 Sedan 2012',
'Tesla Model S Sedan 2012',
'Toyota Sequoia SUV 2012',
'Toyota Camry Sedan 2012',
'Toyota Corolla Sedan 2012',
'Toyota 4Runner SUV 2012',
'Volkswagen Golf Hatchback 2012',
'Volkswagen Golf Hatchback 1991',
'Volkswagen Beetle Hatchback 2012',
'Volvo C30 Hatchback 2012',
'Volvo 240 Sedan 1993',
'Volvo XC90 SUV 2007',
'smart fortwo Convertible 2012',
]

templates = [
'a photo of a {}.',
'a photo of the {}.',
'a photo of my {}.',
'i love my {}!',
'a photo of my dirty {}.',
'a photo of my clean {}.',
'a photo of my new {}.',
'a photo of my old {}.',
]
```

## UCF101

```bash
classes = [
'Apply Eye Makeup',
'Apply Lipstick',
'Archery',
'Baby Crawling',
'Balance Beam',
'Band Marching',
'Baseball Pitch',
'Basketball',
'Basketball Dunk',
'Bench Press',
'Biking',
'Billiards',
'Blow Dry Hair',
'Blowing Candles',
'Body Weight Squats',
'Bowling',
'Boxing Punching Bag',
'Boxing Speed Bag',
'Breast Stroke',
'Brushing Teeth',
'Clean And Jerk',
'Cliff Diving',
'Cricket Bowling',
'Cricket Shot',
'Cutting In Kitchen',
'Diving',
'Drumming',
'Fencing',
'Field Hockey Penalty',
'Floor Gymnastics',
'Frisbee Catch',
'Front Crawl',
'Golf Swing',
'Haircut',
'Hammer Throw',
'Hammering',
'Hand Stand Pushups',
'Handstand Walking',
'Head Massage',
'High Jump',
'Horse Race',
'Horse Riding',
'Hula Hoop',
'Ice Dancing',
'Javelin Throw',
'Juggling Balls',
'Jump Rope',
'Jumping Jack',
'Kayaking',
'Knitting',
'Long Jump',
'Lunges',
'Military Parade',
'Mixing',
'Mopping Floor',
'Nunchucks',
'Parallel Bars',
'Pizza Tossing',
'Playing Cello',
'Playing Daf',
'Playing Dhol',
'Playing Flute',
'Playing Guitar',
'Playing Piano',
'Playing Sitar',
'Playing Tabla',
'Playing Violin',
'Pole Vault',
'Pommel Horse',
'Pull Ups',
'Punch',
'Push Ups',
'Rafting',
'Rock Climbing Indoor',
'Rope Climbing',
'Rowing',
'Salsa Spin',
'Shaving Beard',
'Shotput',
'Skate Boarding',
'Skiing',
'Skijet',
'Sky Diving',
'Soccer Juggling',
'Soccer Penalty',
'Still Rings',
'Sumo Wrestling',
'Surfing',
'Swing',
'Table Tennis Shot',
'Tai Chi',
'Tennis Swing',
'Throw Discus',
'Trampoline Jumping',
'Typing',
'Uneven Bars',
'Volleyball Spiking',
'Walking With Dog',
'Wall Pushups',
'Writing On Board',
'Yo Yo',
]

templates = [
'a photo of a person {}.',
'a video of a person {}.',
'a example of a person {}.',
'a demonstration of a person {}.',
'a photo of the person {}.',
'a video of the person {}.',
'a example of the person {}.',
'a demonstration of the person {}.',
'a photo of a person using {}.',
'a video of a person using {}.',
'a example of a person using {}.',
'a demonstration of a person using {}.',
'a photo of the person using {}.',
'a video of the person using {}.',
'a example of the person using {}.',
'a demonstration of the person using {}.',
'a photo of a person doing {}.',
'a video of a person doing {}.',
'a example of a person doing {}.',
'a demonstration of a person doing {}.',
'a photo of the person doing {}.',
'a video of the person doing {}.',
'a example of the person doing {}.',
'a demonstration of the person doing {}.',
'a photo of a person during {}.',
'a video of a person during {}.',
'a example of a person during {}.',
'a demonstration of a person during {}.',
'a photo of the person during {}.',
'a video of the person during {}.',
'a example of the person during {}.',
'a demonstration of the person during {}.',
'a photo of a person performing {}.',
'a video of a person performing {}.',
'a example of a person performing {}.',
'a demonstration of a person performing {}.',
'a photo of the person performing {}.',
'a video of the person performing {}.',
'a example of the person performing {}.',
'a demonstration of the person performing {}.',
'a photo of a person practicing {}.',
'a video of a person practicing {}.',
'a example of a person practicing {}.',
'a demonstration of a person practicing {}.',
'a photo of the person practicing {}.',
'a video of the person practicing {}.',
'a example of the person practicing {}.',
'a demonstration of the person practicing {}.',
]
```
