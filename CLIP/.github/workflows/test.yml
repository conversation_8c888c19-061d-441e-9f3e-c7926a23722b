# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

name: test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  CLIP-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
        pytorch-version: [2.5.0, 2.6.0]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      
      - name: Install dependencies
        run: |
          pip install uv
          uv pip install --system -e . torch==${{ matrix.pytorch-version }} torchvision pytest --extra-index-url https://download.pytorch.org/whl/cpu
      
      - name: Run tests
        run: pytest
