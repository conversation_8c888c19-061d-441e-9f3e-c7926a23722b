#!/usr/bin/env python3
"""
测试以w开头的ESN的视频识别结果summary字段
从指定的OSS桶中获取数据并进行测试
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any
import alibabacloud_oss_v2 as oss
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WESNSummaryTester:
    """以w开头的ESN summary字段测试器"""
    
    def __init__(self):
        """初始化测试器，使用指定的OSS配置"""
        # 使用指定的OSS配置
        self.endpoint = "https://oss-cn-shenzhen.aliyuncs.com"
        self.region = "cn-shenzhen"
        self.bucket_name = "device-cloudstore-prod"
        self.base_prefix = "pro_file/"
        
        # 从测试配置中获取访问密钥
        self.access_key_id = "LTAI5tCd8onaUBhkS1CFc3qx"
        self.access_key_secret = "******************************"
        
        self.init_oss_client()
    
    def init_oss_client(self):
        """初始化OSS客户端"""
        try:
            credentials_provider = oss.credentials.StaticCredentialsProvider(
                access_key_id=self.access_key_id,
                access_key_secret=self.access_key_secret
            )
            
            cfg = oss.config.load_default()
            cfg.credentials_provider = credentials_provider
            cfg.region = self.region
            cfg.endpoint = self.endpoint
            
            self.oss_client = oss.Client(cfg)
            logger.info(f"OSS客户端初始化成功 - Bucket: {self.bucket_name}, Endpoint: {self.endpoint}")
            
        except Exception as e:
            logger.error(f"OSS客户端初始化失败: {str(e)}")
            raise
    
    def get_w_esn_list(self, max_esn_count: int = 50) -> List[str]:
        """获取以w开头的ESN列表"""
        logger.info(f"开始获取以'w'开头的ESN列表，最大数量: {max_esn_count}")
        
        try:
            w_esn_set = set()
            
            # 使用分页器列出所有对象
            paginator = self.oss_client.list_objects_v2_paginator()
            
            for page in paginator.iter_page(oss.ListObjectsV2Request(
                bucket=self.bucket_name,
                prefix=self.base_prefix,
                delimiter='/',
                max_keys=1000
            )):
                # 处理公共前缀（目录）
                if page.common_prefixes:
                    for prefix_obj in page.common_prefixes:
                        # 提取ESN（去掉前缀和后缀斜杠）
                        full_prefix = prefix_obj.prefix
                        if full_prefix.startswith(self.base_prefix):
                            esn_part = full_prefix[len(self.base_prefix):].rstrip('/')
                            
                            # 检查是否以'w'开头（不区分大小写）
                            if esn_part.lower().startswith('w') and esn_part:
                                w_esn_set.add(esn_part)
                                logger.debug(f"找到w开头的ESN: {esn_part}")
                                
                                # 达到最大数量限制时停止
                                if len(w_esn_set) >= max_esn_count:
                                    break
                
                # 如果已经达到最大数量，跳出外层循环
                if len(w_esn_set) >= max_esn_count:
                    break
            
            w_esn_list = sorted(list(w_esn_set))
            logger.info(f"共找到 {len(w_esn_list)} 个以'w'开头的ESN")
            
            return w_esn_list
            
        except Exception as e:
            logger.error(f"获取w开头ESN列表失败: {str(e)}")
            raise
    
    def get_esn_video_files(self, esn: str, days_back: int = 7) -> List[str]:
        """获取指定ESN的视频文件列表"""
        logger.info(f"获取ESN {esn} 最近 {days_back} 天的视频文件")
        
        try:
            video_files = []
            esn_prefix = f"{self.base_prefix}{esn}/"
            
            # 获取最近几天的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            paginator = self.oss_client.list_objects_v2_paginator()
            
            for page in paginator.iter_page(oss.ListObjectsV2Request(
                bucket=self.bucket_name,
                prefix=esn_prefix,
                max_keys=1000
            )):
                if page.contents:
                    for obj in page.contents:
                        # 检查是否是视频文件
                        if self.is_video_file(obj.key):
                            # 检查文件修改时间是否在指定范围内
                            if obj.last_modified and obj.last_modified >= start_date:
                                video_files.append(obj.key)
            
            logger.info(f"ESN {esn} 找到 {len(video_files)} 个视频文件")
            return video_files[:10]  # 限制最多10个文件进行测试
            
        except Exception as e:
            logger.error(f"获取ESN {esn} 视频文件失败: {str(e)}")
            return []
    
    def is_video_file(self, file_key: str) -> bool:
        """判断是否是视频文件"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v']
        return any(file_key.lower().endswith(ext) for ext in video_extensions)
    
    def download_and_analyze_video(self, video_file_key: str) -> Dict[str, Any]:
        """下载并分析视频文件（模拟）"""
        logger.info(f"模拟分析视频文件: {video_file_key}")
        
        try:
            # 这里模拟视频分析过程
            # 在实际应用中，这里会调用真实的视频分析服务
            
            # 从文件路径提取信息
            parts = video_file_key.split('/')
            esn = parts[1] if len(parts) > 1 else "unknown"
            filename = parts[-1] if parts else "unknown"
            
            # 模拟分析结果
            analysis_result = {
                "file_key": video_file_key,
                "esn": esn,
                "filename": filename,
                "title": f"视频分析 - {filename}",
                "summary": f"这是ESN {esn} 的视频文件 {filename} 的分析摘要。检测到的主要内容包括：人员活动、物体识别等。",
                "confidence": 0.85,
                "objects_detected": ["person", "vehicle", "package"],
                "actions": ["walking", "delivery", "interaction"],
                "timestamp": datetime.now().isoformat(),
                "analysis_version": "v2.0.0-w-esn-test"
            }
            
            # 测试summary字段
            summary_test_result = self.test_summary_field(analysis_result["summary"])
            analysis_result["summary_test"] = summary_test_result
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析视频文件失败 {video_file_key}: {str(e)}")
            return {
                "file_key": video_file_key,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def test_summary_field(self, summary: str) -> Dict[str, Any]:
        """测试summary字段的质量和内容"""
        test_result = {
            "length": len(summary),
            "word_count": len(summary.split()),
            "has_chinese": bool(re.search(r'[\u4e00-\u9fff]', summary)),
            "has_keywords": False,
            "keywords_found": [],
            "quality_score": 0.0
        }
        
        # 检查关键词
        keywords = ["检测", "识别", "分析", "人员", "车辆", "包裹", "活动", "行为", "物体"]
        found_keywords = [kw for kw in keywords if kw in summary]
        test_result["keywords_found"] = found_keywords
        test_result["has_keywords"] = len(found_keywords) > 0
        
        # 计算质量分数
        score = 0.0
        if test_result["length"] > 20:  # 长度合适
            score += 0.3
        if test_result["word_count"] > 5:  # 词数合适
            score += 0.2
        if test_result["has_chinese"]:  # 包含中文
            score += 0.2
        if test_result["has_keywords"]:  # 包含关键词
            score += 0.3
        
        test_result["quality_score"] = round(score, 2)
        
        return test_result
    
    async def run_test(self, max_esn_count: int = 10, max_videos_per_esn: int = 3):
        """运行完整的测试流程"""
        logger.info("开始运行w开头ESN的summary字段测试")
        
        try:
            # 1. 获取w开头的ESN列表
            w_esn_list = self.get_w_esn_list(max_esn_count)
            
            if not w_esn_list:
                logger.warning("未找到以'w'开头的ESN")
                return
            
            logger.info(f"将测试以下ESN: {w_esn_list[:5]}{'...' if len(w_esn_list) > 5 else ''}")
            
            # 2. 对每个ESN进行测试
            all_results = []
            
            for i, esn in enumerate(w_esn_list[:max_esn_count], 1):
                logger.info(f"[{i}/{min(len(w_esn_list), max_esn_count)}] 测试ESN: {esn}")
                
                # 获取视频文件
                video_files = self.get_esn_video_files(esn, days_back=7)
                
                if not video_files:
                    logger.warning(f"ESN {esn} 没有找到视频文件")
                    continue
                
                # 分析视频文件
                esn_results = []
                for j, video_file in enumerate(video_files[:max_videos_per_esn], 1):
                    logger.info(f"  [{j}/{min(len(video_files), max_videos_per_esn)}] 分析视频: {video_file}")
                    
                    result = self.download_and_analyze_video(video_file)
                    esn_results.append(result)
                
                all_results.extend(esn_results)
            
            # 3. 生成测试报告
            self.generate_test_report(all_results)
            
            logger.info("测试完成！")
            
        except Exception as e:
            logger.error(f"测试运行失败: {str(e)}")
            raise
    
    def generate_test_report(self, results: List[Dict[str, Any]]):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        # 统计信息
        total_videos = len(results)
        successful_analyses = len([r for r in results if "error" not in r])
        failed_analyses = total_videos - successful_analyses
        
        # Summary质量统计
        summary_scores = [r.get("summary_test", {}).get("quality_score", 0) for r in results if "summary_test" in r]
        avg_quality_score = sum(summary_scores) / len(summary_scores) if summary_scores else 0
        
        # 生成报告
        report = {
            "test_summary": {
                "total_videos_tested": total_videos,
                "successful_analyses": successful_analyses,
                "failed_analyses": failed_analyses,
                "success_rate": round(successful_analyses / total_videos * 100, 2) if total_videos > 0 else 0,
                "average_summary_quality_score": round(avg_quality_score, 2)
            },
            "detailed_results": results,
            "generated_at": datetime.now().isoformat()
        }
        
        # 保存报告到文件
        report_filename = f"w_esn_summary_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印摘要
        print("\n" + "="*60)
        print("📊 测试报告摘要")
        print("="*60)
        print(f"总测试视频数: {total_videos}")
        print(f"成功分析数: {successful_analyses}")
        print(f"失败分析数: {failed_analyses}")
        print(f"成功率: {report['test_summary']['success_rate']}%")
        print(f"平均Summary质量分数: {report['test_summary']['average_summary_quality_score']}")
        print(f"详细报告已保存到: {report_filename}")
        print("="*60)

async def main():
    """主函数"""
    print("🚀 开始w开头ESN的视频识别结果summary字段测试")
    print("="*60)
    
    try:
        tester = WESNSummaryTester()
        await tester.run_test(max_esn_count=5, max_videos_per_esn=2)
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
