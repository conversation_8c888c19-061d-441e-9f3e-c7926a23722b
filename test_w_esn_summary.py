#!/usr/bin/env python3
"""
测试以w开头的ESN的视频识别结果summary字段
从指定的OSS桶中获取数据并进行实际的视频处理测试
"""

import asyncio
import os
import sys
import ray
from datetime import datetime, timedelta
from typing import List, Dict, Any
import alibabacloud_oss_v2 as oss
import logging
import re
from video_processor import VideoProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WESNSummaryTester:
    """以w开头的ESN summary字段测试器"""

    def __init__(self):
        """初始化测试器，使用指定的OSS配置"""
        # 使用指定的OSS配置
        self.endpoint = "https://oss-cn-shenzhen.aliyuncs.com"
        self.region = "cn-shenzhen"
        self.bucket_name = "device-cloudstore-prod"
        self.base_prefix = "pro_file/"

        # 从测试配置中获取访问密钥
        self.access_key_id = "LTAI5tCd8onaUBhkS1CFc3qx"
        self.access_key_secret = "******************************"

        self.init_oss_client()

        # 初始化Ray和视频处理器
        self.video_processor = None
        self.init_video_processor()
    
    def init_oss_client(self):
        """初始化OSS客户端"""
        try:
            credentials_provider = oss.credentials.StaticCredentialsProvider(
                access_key_id=self.access_key_id,
                access_key_secret=self.access_key_secret
            )
            
            cfg = oss.config.load_default()
            cfg.credentials_provider = credentials_provider
            cfg.region = self.region
            cfg.endpoint = self.endpoint
            
            self.oss_client = oss.Client(cfg)
            logger.info(f"OSS客户端初始化成功 - Bucket: {self.bucket_name}, Endpoint: {self.endpoint}")

        except Exception as e:
            logger.error(f"OSS客户端初始化失败: {str(e)}")
            raise

    def init_video_processor(self):
        """初始化视频处理器"""
        try:
            # 设置环境变量
            os.environ['DEPLOY_MODE'] = 'dev'  # 使用开发环境配置

            # 初始化Ray（如果还没有初始化）
            if not ray.is_initialized():
                ray.init(ignore_reinit_error=True)
                logger.info("Ray初始化成功")

            # 创建视频处理器
            self.video_processor = VideoProcessor.remote()
            logger.info("视频处理器初始化成功")

        except Exception as e:
            logger.error(f"视频处理器初始化失败: {str(e)}")
            raise
    
    def get_w_esn_list(self, max_esn_count: int = 50) -> List[str]:
        """获取以w开头的ESN列表"""
        logger.info(f"开始获取以'w'开头的ESN列表，最大数量: {max_esn_count}")
        
        try:
            w_esn_set = set()
            
            # 使用分页器列出所有对象
            paginator = self.oss_client.list_objects_v2_paginator()
            
            for page in paginator.iter_page(oss.ListObjectsV2Request(
                bucket=self.bucket_name,
                prefix=self.base_prefix,
                delimiter='/',
                max_keys=1000
            )):
                # 处理公共前缀（目录）
                if page.common_prefixes:
                    for prefix_obj in page.common_prefixes:
                        # 提取ESN（去掉前缀和后缀斜杠）
                        full_prefix = prefix_obj.prefix
                        if full_prefix.startswith(self.base_prefix):
                            esn_part = full_prefix[len(self.base_prefix):].rstrip('/')
                            
                            # 检查是否以'w'开头（不区分大小写）
                            if esn_part.lower().startswith('w') and esn_part:
                                w_esn_set.add(esn_part)
                                logger.debug(f"找到w开头的ESN: {esn_part}")
                                
                                # 达到最大数量限制时停止
                                if len(w_esn_set) >= max_esn_count:
                                    break
                
                # 如果已经达到最大数量，跳出外层循环
                if len(w_esn_set) >= max_esn_count:
                    break
            
            w_esn_list = sorted(list(w_esn_set))
            logger.info(f"共找到 {len(w_esn_list)} 个以'w'开头的ESN")
            
            return w_esn_list
            
        except Exception as e:
            logger.error(f"获取w开头ESN列表失败: {str(e)}")
            raise
    
    def get_esn_video_files(self, esn: str, days_back: int = 7) -> List[str]:
        """获取指定ESN的视频文件列表"""
        logger.info(f"获取ESN {esn} 最近 {days_back} 天的视频文件")
        
        try:
            video_files = []
            esn_prefix = f"{self.base_prefix}{esn}/"
            
            # 获取最近几天的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            paginator = self.oss_client.list_objects_v2_paginator()
            
            for page in paginator.iter_page(oss.ListObjectsV2Request(
                bucket=self.bucket_name,
                prefix=esn_prefix,
                max_keys=1000
            )):
                if page.contents:
                    for obj in page.contents:
                        # 检查是否是视频文件
                        if self.is_video_file(obj.key):
                            # 检查文件修改时间是否在指定范围内
                            if obj.last_modified and obj.last_modified >= start_date:
                                video_files.append(obj.key)
            
            logger.info(f"ESN {esn} 找到 {len(video_files)} 个视频文件")
            return video_files[:10]  # 限制最多10个文件进行测试
            
        except Exception as e:
            logger.error(f"获取ESN {esn} 视频文件失败: {str(e)}")
            return []
    
    def is_video_file(self, file_key: str) -> bool:
        """判断是否是视频文件"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v']
        return any(file_key.lower().endswith(ext) for ext in video_extensions)
    
    async def download_and_analyze_video(self, video_file_key: str) -> Dict[str, Any]:
        """使用实际的视频处理器分析视频文件"""
        logger.info(f"开始分析视频文件: {video_file_key}")

        try:
            # 从文件路径提取信息
            parts = video_file_key.split('/')
            esn = parts[1] if len(parts) > 1 else "unknown"
            filename = parts[-1] if parts else "unknown"

            # 构建完整的视频URL
            video_url = f"https://{self.bucket_name}.{self.endpoint.replace('https://', '')}/{video_file_key}"

            # 生成事件ID
            event_id = f"w_esn_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{abs(hash(video_file_key)) % 1000:03d}"

            logger.info(f"处理视频: ESN={esn}, Event_ID={event_id}, URL={video_url}")

            # 使用实际的视频处理器进行分析
            result = await self.video_processor.process.remote(
                esn=esn,
                event_id=event_id,
                url=video_url,
                mode="cloud"  # 使用云端模式
            )

            if result and result.get("video_result"):
                video_result = result["video_result"]

                # 测试summary字段
                summary = video_result.get("summary", "")
                summary_test_result = self.test_summary_field(summary)

                # 构建返回结果
                analysis_result = {
                    "file_key": video_file_key,
                    "esn": esn,
                    "filename": filename,
                    "event_id": event_id,
                    "video_url": video_url,
                    "title": video_result.get("title", ""),
                    "summary": summary,
                    "timestamp": video_result.get("timestamp", ""),
                    "num_persons": video_result.get("num_persons", 0),
                    "persons": video_result.get("persons", "[]"),
                    "couriers": video_result.get("couriers", "[]"),
                    "food_deliverers": video_result.get("food_deliverers", "[]"),
                    "packages": video_result.get("packages", "[]"),
                    "pets": video_result.get("pets", "[]"),
                    "security_risk": video_result.get("security_risk", ""),
                    "recommendation": video_result.get("recommendation", ""),
                    "reasoning_content": video_result.get("reasoning_content", ""),
                    "oss_path": video_result.get("oss_path", ""),
                    "summary_test": summary_test_result,
                    "processing_status": result.get("status", "unknown"),
                    "analysis_version": "v2.0.0-w-esn-real-test"
                }

                return analysis_result
            else:
                logger.error(f"视频处理失败，未获得有效结果: {video_file_key}")
                return {
                    "file_key": video_file_key,
                    "esn": esn,
                    "filename": filename,
                    "event_id": event_id,
                    "video_url": video_url,
                    "error": "视频处理失败，未获得有效结果",
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"分析视频文件失败 {video_file_key}: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "file_key": video_file_key,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def test_summary_field(self, summary: str) -> Dict[str, Any]:
        """测试summary字段的基本信息"""
        test_result = {
            "length": len(summary),
            "word_count": len(summary.split()),
            "has_chinese": bool(re.search(r'[\u4e00-\u9fff]', summary)),
            "is_empty": len(summary.strip()) == 0
        }

        return test_result

    def save_result_as_txt(self, result: Dict[str, Any], esn: str, video_index: int):
        """将识别结果保存为简化的txt文件，只包含ESN、视频URL和summary"""
        try:
            # 创建输出目录
            output_dir = f"w_esn_summary_results_{datetime.now().strftime('%Y%m%d')}"
            os.makedirs(output_dir, exist_ok=True)

            # 生成文件名
            filename = f"{esn}_video_{video_index:02d}.txt"
            filepath = os.path.join(output_dir, filename)

            # 准备要写入的内容
            content_lines = []
            content_lines.append(f"ESN: {esn}")
            content_lines.append(f"视频URL: {result.get('video_url', 'N/A')}")
            content_lines.append("")
            content_lines.append("Summary:")
            content_lines.append("-" * 50)

            # 如果有错误，显示错误信息
            if "error" in result:
                content_lines.append(f"处理失败: {result['error']}")
            else:
                summary = result.get('summary', '无摘要内容')
                content_lines.append(summary)

            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content_lines))

            logger.info(f"摘要结果已保存到: {filepath}")

        except Exception as e:
            logger.error(f"保存结果文件失败: {str(e)}")
    
    async def run_test(self, max_esn_count: int = 10, max_videos_per_esn: int = 3):
        """运行完整的测试流程"""
        logger.info("开始运行w开头ESN的summary字段测试")
        
        try:
            # 1. 获取w开头的ESN列表
            w_esn_list = self.get_w_esn_list(max_esn_count)
            
            if not w_esn_list:
                logger.warning("未找到以'w'开头的ESN")
                return
            
            logger.info(f"将测试以下ESN: {w_esn_list[:5]}{'...' if len(w_esn_list) > 5 else ''}")
            
            # 2. 对每个ESN进行测试
            all_results = []
            
            for i, esn in enumerate(w_esn_list[:max_esn_count], 1):
                logger.info(f"[{i}/{min(len(w_esn_list), max_esn_count)}] 测试ESN: {esn}")
                
                # 获取视频文件
                video_files = self.get_esn_video_files(esn, days_back=7)
                
                if not video_files:
                    logger.warning(f"ESN {esn} 没有找到视频文件")
                    continue
                
                # 分析视频文件
                esn_results = []
                for j, video_file in enumerate(video_files[:max_videos_per_esn], 1):
                    logger.info(f"  [{j}/{min(len(video_files), max_videos_per_esn)}] 分析视频: {video_file}")

                    result = await self.download_and_analyze_video(video_file)
                    esn_results.append(result)

                    # 保存单个结果为txt文件
                    self.save_result_as_txt(result, esn, j)

                all_results.extend(esn_results)
            
            # 3. 生成测试报告
            self.generate_test_report(all_results)
            
            logger.info("测试完成！")
            
        except Exception as e:
            logger.error(f"测试运行失败: {str(e)}")
            raise
    
    def generate_test_report(self, results: List[Dict[str, Any]]):
        """生成简化的测试报告"""
        logger.info("测试完成，所有摘要结果已保存为txt文件")

        # 简单统计
        total_videos = len(results)
        successful_analyses = len([r for r in results if "error" not in r])
        failed_analyses = total_videos - successful_analyses

        # 打印简单摘要
        print("\n" + "="*50)
        print("📊 测试完成")
        print("="*50)
        print(f"总测试视频数: {total_videos}")
        print(f"成功处理: {successful_analyses}")
        print(f"处理失败: {failed_analyses}")
        print(f"摘要文件已保存到目录: w_esn_summary_results_{datetime.now().strftime('%Y%m%d')}")
        print("="*50)

async def main():
    """主函数"""
    print("🚀 开始w开头ESN的视频识别结果summary字段测试")
    print("="*60)
    
    try:
        tester = WESNSummaryTester()
        await tester.run_test(max_esn_count=1, max_videos_per_esn=1)  # 减少测试数量

    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 清理Ray资源
        if ray.is_initialized():
            ray.shutdown()
            logger.info("Ray资源已清理")

    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
