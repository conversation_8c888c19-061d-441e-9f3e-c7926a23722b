# OSS V2 迁移文档

## 📋 概述

本次更新将 `oss_manager.py` 从阿里云 OSS SDK v1 (`oss2`) 迁移到 OSS SDK v2 (`alibabacloud_oss_v2`)，以获得更好的性能、稳定性和功能支持。

## 🔄 主要变更

### 1. SDK 依赖更新
- **旧版本**: `import oss2`
- **新版本**: `import alibabacloud_oss_v2 as oss`

### 2. 客户端初始化方式
**旧版本**:
```python
auth = oss2.Auth(access_key_id, access_key_secret)
self.bucket = oss2.Bucket(auth, endpoint, bucket_name)
```

**新版本**:
```python
credentials_provider = oss.credentials.StaticCredentialsProvider(
    access_key_id=access_key_id,
    access_key_secret=access_key_secret
)
cfg = oss.config.load_default()
cfg.credentials_provider = credentials_provider
cfg.region = region
cfg.endpoint = endpoint
self.oss_client = oss.Client(cfg)
```

### 3. API 调用方式更新

#### 上传对象
**旧版本**:
```python
self.bucket.put_object(path, content, headers=headers)
```

**新版本**:
```python
request = oss.PutObjectRequest(
    bucket=bucket_name,
    key=path,
    body=content,
    content_type=content_type
)
self.oss_client.put_object(request)
```

#### 下载对象
**旧版本**:
```python
result = self.bucket.get_object(path)
content = result.read()
```

**新版本**:
```python
request = oss.GetObjectRequest(bucket=bucket_name, key=path)
result = self.oss_client.get_object(request)
content = result.body.read()
```

#### 列出对象
**旧版本**:
```python
for obj in oss2.ObjectIterator(self.bucket, prefix=prefix, max_keys=max_keys):
    files.append(obj.key)
```

**新版本**:
```python
paginator = self.oss_client.list_objects_v2_paginator()
for page in paginator.iter_page(oss.ListObjectsV2Request(
    bucket=bucket_name, 
    prefix=prefix, 
    max_keys=max_keys
)):
    if page.contents:
        for obj in page.contents:
            files.append(obj.key)
```

## ✅ 功能验证

运行以下命令验证迁移是否成功：

```bash
python3 test_oss_v2_migration.py
```

测试包括：
- ✅ OSS配置加载
- ✅ 基本操作（上传JSON、文本、JSONL）
- ✅ 下载操作
- ✅ 文件列表操作
- ✅ 高级操作（视频分析保存、搜索日志保存、存储统计）

## 🎯 优势

### 1. 性能提升
- 更高效的网络连接管理
- 优化的数据传输协议
- 更好的并发处理能力

### 2. 功能增强
- 支持更多OSS功能特性
- 更完善的错误处理
- 更灵活的配置选项

### 3. 稳定性改进
- 更稳定的长连接
- 更好的重试机制
- 更完善的异常处理

### 4. 向后兼容
- 保持所有公共方法接口不变
- 支持两种配置格式
- 无需修改调用代码

## 🚀 部署建议

1. **开发环境**: 已完成迁移，可直接使用
2. **测试环境**: 建议先在测试环境验证功能
3. **生产环境**: 确认测试通过后再部署到生产环境

## 📝 注意事项

1. **依赖更新**: 确保安装了 `alibabacloud_oss_v2` 包
2. **配置检查**: 验证OSS配置文件中的字段名称
3. **网络连接**: 确保服务器能够访问OSS服务
4. **权限验证**: 确认OSS访问密钥具有必要的权限

## 🔍 故障排除

如果遇到问题，请检查：

1. **依赖安装**: `pip install alibabacloud_oss_v2`
2. **配置文件**: 确认配置文件格式正确
3. **网络连接**: 测试到OSS端点的网络连通性
4. **访问权限**: 验证OSS访问密钥和权限设置

## 📞 支持

如有问题，请查看：
- 测试日志: `test_oss_v2_migration.py` 输出
- 应用日志: 检查OSS相关的错误信息
- 阿里云OSS控制台: 验证存储桶和权限设置
