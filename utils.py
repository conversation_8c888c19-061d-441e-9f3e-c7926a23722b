from datetime import datetime
import ray
from typing import Dict, Any
import logging
import os
import time
import asyncio
from ray.util import ActorPool


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_ray(config: Dict[str, Any]):
    """初始化 Ray
    
    Args:
        config: 配置字典,包含 Ray 地址等信息
    """
    if not ray.is_initialized():
        try:
            ray_address = config.get('Ray', {}).get('address')
            if not ray_address:
                raise ValueError("Ray address not specified in config")
                
            ray.init(address=ray_address, ignore_reinit_error=True,namespace="yolo")
            logger.info(f"Ray 初始化成功, 地址: {ray_address}")
        except Exception as e:
            logger.error(f"Ray 初始化失败: {str(e)}")
            raise

class TrackedActorPool:
    """带超时跟踪的Actor池，可以检测长时间未归还的Actor"""
    
    def __init__(self, actors, max_busy_time=300):  # 默认5分钟超时
        """
        初始化带超时跟踪的Actor池
        
        Args:
            actors: Actor列表
            max_busy_time: Actor最大繁忙时间(秒)，超过此时间将被强制归还
        """
        self.actor_pool = ActorPool(actors)
        self.busy_actors = {}  # {actor_id: (actor, checkout_time)}
        self.max_busy_time = max_busy_time
        self.lock = asyncio.Lock()
        self.logger = logging.getLogger('yolo_service')
        self.all_actors = list(actors)  # 保存所有Actor的列表
        
    def pop_idle(self):
        """获取一个空闲的Actor（同步方法）"""
        actor = self.actor_pool.pop_idle()
        if actor:
            try:
                actor_id = id(actor)  # 使用对象ID作为键
                self.busy_actors[actor_id] = (actor, time.time())
                self.logger.debug(f"Actor {actor_id} borrowed, {len(self.busy_actors)} actors now busy")
            except Exception as e:
                self.logger.error(f"Error tracking actor checkout: {e}")
        return actor
    
    def push(self, actor):
        """将Actor推回池中（同步方法）"""
        try:
            actor_id = id(actor)  # 使用对象ID作为键
            if actor_id in self.busy_actors:
                checkout_time = self.busy_actors[actor_id][1]
                busy_time = time.time() - checkout_time
                self.logger.debug(f"Actor {actor_id} returned after {busy_time:.1f} seconds")
                del self.busy_actors[actor_id]
            self.actor_pool.push(actor)
        except Exception as e:
            self.logger.error(f"Error returning actor to pool: {e}")
            # 尝试无论如何都将actor放回池中
            try:
                self.actor_pool.push(actor)
            except:
                pass
    
    async def check_timeouts(self):
        """检查并重置超时的Actor"""
        now = time.time()
        actors_to_reset = []
        
        async with self.lock:
            for actor_id, (actor, checkout_time) in list(self.busy_actors.items()):
                busy_time = now - checkout_time
                if busy_time > self.max_busy_time:
                    # 记录超时情况
                    self.logger.warning(f"Actor {actor_id} has been busy for {busy_time:.1f} seconds, forcing return to pool")
                    # 将超时Actor添加到重置列表
                    actors_to_reset.append((actor_id, actor))
        
        # 将这些Actor推回池中
        for actor_id, actor in actors_to_reset:
            self.push(actor)
            
            # 尝试重启该Actor以恢复到干净状态
            try:
                # 如果Actor有restart方法，调用它
                self.logger.info(f"Attempting to restart actor {actor_id}")
                asyncio.create_task(actor.restart.remote())
            except Exception as e:
                self.logger.error(f"Error scheduling restart for timed out actor {actor_id}: {e}")
    
    def map(self, fn, values):
        """映射函数到Actor池（兼容ActorPool接口）"""
        return self.actor_pool.map(fn, values)
    
    def get_busy_info(self):
        """获取繁忙Actor的详细信息，用于诊断"""
        now = time.time()
        busy_info = []
        
        for actor_id, (actor, checkout_time) in self.busy_actors.items():
            busy_time = now - checkout_time
            busy_info.append(f"Actor {actor_id}: busy for {busy_time:.1f}s")
        
        return busy_info
    
    def get_busy_count(self):
        """获取当前繁忙的Actor数量"""
        return len(self.busy_actors)
    
    def get_total_count(self):
        """获取总Actor数量"""
        return len(self.all_actors)

def convert_timestamp_to_unix(timestamp_str: str) -> int:
        """
        将时间戳字符串转换为Unix时间戳
        
        Args:
            timestamp_str: 时间戳字符串，格式为 "YYYY-MM-DD HH:MM:SS"
            
        Returns:
            Unix时间戳（秒）
        """
        try:
            if not timestamp_str:
                return 0
            
            # 解析时间字符串
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
            
            # 转换为Unix时间戳
            unix_timestamp = int(dt.timestamp())
            
            return unix_timestamp
        except Exception as e:
            logger.warning(f"Failed to convert timestamp '{timestamp_str}' to unix timestamp: {e}")
            return 0
