import json
import gzip
import hashlib
from datetime import datetime
from pathlib import Path
import oss2
from typing import Dict, Any, Optional, List
import logging
import os
import yaml
import pytz

class OSSDataManager:
    """OSS数据管理类，用于存储视频分析结果、搜索日志和训练数据"""
    
    def __init__(self, config_path: str = None):
        self.logger = logging.getLogger(__name__)
        self.load_config(config_path)
        
        # 只有在启用OSS时才初始化客户端
        if self.enabled:
            # 初始化OSS客户端
            auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            self.bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)
            self.logger.info(f"OSS Manager initialized - Bucket: {self.bucket_name}, Endpoint: {self.endpoint}")
        else:
            self.bucket = None
            self.logger.info("OSS Manager initialized - OSS storage disabled")
    
    def load_config(self, path: str = None):
        """加载OSS配置"""
        if not path:
            deploy_mode = os.getenv("DEPLOY_MODE", "dev")
            path = f"config_{deploy_mode}.yaml"
        
        self.logger.info(f"Loading OSS config from: {path}")
        
        with open(path, "r") as f:
            config = yaml.safe_load(f)
        
        oss_config = config.get("OSS", {})
        self.enabled = oss_config.get("enabled", True)  # 默认启用
        self.access_key_id = oss_config.get("access_key_id")
        self.access_key_secret = oss_config.get("access_key_secret")
        self.endpoint = oss_config.get("endpoint")
        self.bucket_name = oss_config.get("bucket_name")
        
        if not self.enabled:
            self.logger.info("OSS storage is disabled in configuration")
            return
        
        if not all([self.access_key_id, self.access_key_secret, self.endpoint, self.bucket_name]):
            raise ValueError("OSS configuration is incomplete. Please check access_key_id, access_key_secret, endpoint, and bucket_name.")
        
        self.logger.info(f"OSS config loaded - Enabled: {self.enabled}, Bucket: {self.bucket_name}")
    
    def save_video_analysis(self, event_id: str, esn: str, video_url: str,
                          analysis_result: Dict[str, Any], reasoning_content: str = "",
                          processing_info: Dict[str, Any] = None,
                          file_info: Dict[str, Any] = None) -> str:
        """
        保存视频分析结果到OSS
        
        Args:
            event_id: 事件ID
            esn: 设备序列号
            video_url: 视频URL
            analysis_result: 分析结果
            reasoning_content: AI推理过程
            processing_info: 处理信息
            file_info: 文件信息
            
        Returns:
            OSS存储路径，如果OSS未启用则返回空字符串
        """
        if not self.enabled:
            self.logger.debug("OSS storage is disabled, skipping video analysis save")
            return ""
            
        try:
            # 使用上海时区确保时间一致性
            shanghai_tz = pytz.timezone('Asia/Shanghai')
            now = datetime.now(shanghai_tz)
            base_path = f"video-analysis/raw/{now.strftime('%Y-%m-%d')}/{esn}/{event_id}"
            
            # 构建完整的分析数据
            analysis_data = {
                "event_id": event_id,
                "esn": esn,
                "video_url": video_url,
                "timestamp": now.isoformat(),
                "analysis_result": analysis_result,
                "processing_info": processing_info or {},
                "file_info": file_info or {}
            }
            
            # 如果分析结果中包含embedding，单独提取
            if "title_embedding" in analysis_result:
                analysis_data["embeddings"] = {
                    "title_embedding": analysis_result.pop("title_embedding", None),
                    "summary_embedding": analysis_result.pop("summary_embedding", None)
                }
            
            # 保存分析结果
            analysis_path = f"{base_path}/analysis.json"
            self._upload_json(analysis_path, analysis_data)
            
            # 保存元数据
            metadata = self._create_metadata(event_id, esn, video_url, analysis_data, base_path)
            metadata_path = f"{base_path}/metadata.json"
            self._upload_json(metadata_path, metadata)
            
            # 保存推理过程
            if reasoning_content:
                reasoning_path = f"{base_path}/reasoning.txt"
                self._upload_text(reasoning_path, reasoning_content)
            
            self.logger.info(f"Video analysis saved to OSS: {base_path}")
            return base_path
            
        except Exception as e:
            self.logger.error(f"Failed to save video analysis to OSS: {str(e)}")
            raise
    
    def save_search_log(self, request_data: Dict[str, Any], response_data: Dict[str, Any],
                       performance_data: Dict[str, Any] = None, session_id: str = None) -> str:
        """
        保存搜索日志到OSS
        
        Args:
            request_data: 搜索请求数据
            response_data: 搜索响应数据
            performance_data: 性能数据
            session_id: 会话ID
            
        Returns:
            OSS存储路径，如果OSS未启用则返回空字符串
        """
        if not self.enabled:
            self.logger.debug("OSS storage is disabled, skipping search log save")
            return ""
            
        try:
            # 使用上海时区确保时间一致性
            shanghai_tz = pytz.timezone('Asia/Shanghai')
            now = datetime.now(shanghai_tz)
            query_id = f"q_{now.strftime('%Y%m%d_%H%M%S')}_{abs(hash(str(request_data))) % 1000:03d}"
            
            query_log = {
                "query_id": query_id,
                "timestamp": now.isoformat(),
                "session_id": session_id or f"sess_{abs(hash(str(request_data))) % 10000:04d}",
                "request": request_data,
                "response": response_data,
                "performance": performance_data or {}
            }
            
            path = f"search-logs/queries/{now.strftime('%Y-%m-%d')}/query_{query_id}.json"
            self._upload_json(path, query_log)
            
            self.logger.info(f"Search log saved to OSS: {path}")
            return path
            
        except Exception as e:
            self.logger.error(f"Failed to save search log to OSS: {str(e)}")
            raise
    
    def create_training_dataset(self, dataset_name: str, version: str,
                              data: List[Dict[str, Any]], metadata: Dict[str, Any]) -> str:
        """
        创建训练数据集
        
        Args:
            dataset_name: 数据集名称
            version: 版本号
            data: 训练数据列表
            metadata: 数据集元信息
            
        Returns:
            OSS存储路径，如果OSS未启用则返回空字符串
        """
        if not self.enabled:
            self.logger.debug("OSS storage is disabled, skipping training dataset creation")
            return ""
            
        try:
            base_path = f"training-data/datasets/{dataset_name}/{version}"
            
            # 保存数据
            data_path = f"{base_path}/data.jsonl"
            self._upload_jsonl(data_path, data)
            
            # 添加创建时间到元数据
            metadata.update({
                "created_at": datetime.now().isoformat(),
                "data_count": len(data),
                "oss_path": base_path
            })
            
            # 保存元数据
            metadata_path = f"{base_path}/metadata.json"
            self._upload_json(metadata_path, metadata)
            
            self.logger.info(f"Training dataset created: {base_path}")
            return base_path
            
        except Exception as e:
            self.logger.error(f"Failed to create training dataset: {str(e)}")
            raise
    
    def backup_daily_data(self, date: datetime = None) -> str:
        """
        每日数据备份
        
        Args:
            date: 备份日期，默认为今天
            
        Returns:
            备份路径，如果OSS未启用则返回空字符串
        """
        if not self.enabled:
            self.logger.debug("OSS storage is disabled, skipping daily backup")
            return ""
            
        if date is None:
            date = datetime.now()
            
        backup_path = f"backups/daily/{date.strftime('%Y-%m-%d')}"
        
        # 这里可以实现具体的备份逻辑
        # 例如：复制指定日期的所有数据到备份目录
        
        self.logger.info(f"Daily backup completed: {backup_path}")
        return backup_path
    
    def _upload_json(self, path: str, data: Dict[str, Any], compress: bool = True):
        """上传JSON数据，可选压缩"""
        if not self.enabled:
            return
            
        content = json.dumps(data, ensure_ascii=False, indent=2)
        if compress:
            content = gzip.compress(content.encode('utf-8'))
            path += '.gz'
            content_type = 'application/gzip'
        else:
            content = content.encode('utf-8')
            content_type = 'application/json'
        
        headers = {'Content-Type': content_type}
        self.bucket.put_object(path, content, headers=headers)
    
    def _upload_jsonl(self, path: str, data: List[Dict[str, Any]], compress: bool = True):
        """上传JSONL数据"""
        if not self.enabled:
            return
            
        lines = [json.dumps(item, ensure_ascii=False) for item in data]
        content = '\n'.join(lines)
        
        if compress:
            content = gzip.compress(content.encode('utf-8'))
            path += '.gz'
            content_type = 'application/gzip'
        else:
            content = content.encode('utf-8')
            content_type = 'text/plain'
        
        headers = {'Content-Type': content_type}
        self.bucket.put_object(path, content, headers=headers)
    
    def _upload_text(self, path: str, content: str, compress: bool = True):
        """上传文本内容"""
        if not self.enabled:
            return
            
        if compress:
            content_bytes = gzip.compress(content.encode('utf-8'))
            path += '.gz'
            content_type = 'application/gzip'
        else:
            content_bytes = content.encode('utf-8')
            content_type = 'text/plain'
        
        headers = {'Content-Type': content_type}
        self.bucket.put_object(path, content_bytes, headers=headers)
    
    def _create_metadata(self, event_id: str, esn: str, video_url: str,
                        analysis_data: Dict[str, Any], oss_path: str) -> Dict[str, Any]:
        """创建元数据"""
        content_hash = hashlib.sha256(json.dumps(analysis_data, sort_keys=True).encode()).hexdigest()
        
        return {
            "event_id": event_id,
            "esn": esn,
            "video_url": video_url,
            "file_info": analysis_data.get("file_info", {}),
            "processing_info": analysis_data.get("processing_info", {}),
            "storage_info": {
                "upload_time": datetime.now().isoformat(),
                "oss_path": oss_path,
                "file_hash": f"sha256:{content_hash}",
                "compression": "gzip",
                "file_size": len(json.dumps(analysis_data).encode()),
                "version": "v1.4.0"
            }
        }
    
    def download_json(self, path: str, decompress: bool = True) -> Dict[str, Any]:
        """从OSS下载JSON数据"""
        if not self.enabled:
            self.logger.warning("OSS storage is disabled, cannot download data")
            return {}
            
        try:
            if decompress and not path.endswith('.gz'):
                path += '.gz'
            
            result = self.bucket.get_object(path)
            content = result.read()
            
            if decompress:
                content = gzip.decompress(content)
            
            return json.loads(content.decode('utf-8'))
            
        except Exception as e:
            self.logger.error(f"Failed to download from OSS: {path}, error: {str(e)}")
            raise
    
    def list_files(self, prefix: str, max_keys: int = 100) -> List[str]:
        """列出指定前缀的文件"""
        if not self.enabled:
            self.logger.warning("OSS storage is disabled, cannot list files")
            return []
            
        try:
            files = []
            for obj in oss2.ObjectIterator(self.bucket, prefix=prefix, max_keys=max_keys):
                files.append(obj.key)
            return files
        except Exception as e:
            self.logger.error(f"Failed to list files with prefix {prefix}: {str(e)}")
            raise
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        if not self.enabled:
            return {
                "oss_enabled": False,
                "message": "OSS storage is disabled",
                "last_updated": datetime.now().isoformat()
            }
            
        try:
            stats = {
                "oss_enabled": True,
                "video_analysis_count": len(self.list_files("video-analysis/", max_keys=1000)),
                "search_logs_count": len(self.list_files("search-logs/", max_keys=1000)),
                "training_datasets_count": len(self.list_files("training-data/", max_keys=1000)),
                "last_updated": datetime.now().isoformat()
            }
            return stats
        except Exception as e:
            self.logger.error(f"Failed to get storage stats: {str(e)}")
            return {"oss_enabled": True, "error": str(e)} 
