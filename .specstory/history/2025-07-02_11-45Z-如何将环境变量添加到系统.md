<!-- Generated by SpecStory -->

# 如何将环境变量添加到系统 (2025-07-02 11:45Z)

_**User**_

怎么把环境加到系统变量，我使用uv找不到命令

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户在使用 uv 时遇到了"找不到命令"的问题。从他们提供的信息中，我可以看到：

1. 他们有一个 Dockerfile-dev 文件，其中已经设置了 `ENV PATH="/root/.local/bin:${PATH}"`
2. 他们手动执行了 uv-installer.sh 脚本，从输出可以看到 uv 被安装到了 `/root/.local/bin` 目录
3. 输出显示安装了 `uv` 和 `uvx` 命令到 `/root/.local/bin`

问题很可能是 `/root/.local/bin` 没有被添加到当前 shell 的 PATH 环境变量中。

我需要帮助用户解决这个问题，主要步骤是：
1. 检查当前的 PATH 环境变量
2. 临时添加到 PATH
3. 永久添加到 PATH（通过修改 .bashrc 或 .profile）
4. 验证 uv 命令是否可用

让我按照用户规则来回答，使用中文，并且分步骤解释。</details></think>

---

我看到你已经成功安装了 uv 到 `/root/.local/bin` 目录，但是系统找不到这个命令。这是因为 `/root/.local/bin` 目录没有被添加到系统的 PATH 环境变量中。

让我帮你解决这个问题：

## 1. 检查当前 PATH 环境变量

首先，让我们查看当前的 PATH 设置：

---

```bash
echo $PATH
```

```
(base) wall@kaadas:/workspace/video_unstanding$ echo $PATH
/home/<USER>/.local/bin:/usr/bin:/usr/local/bin:/usr/bin:&PATH
(base) wall@kaadas:/workspace/video_unstanding$
```

---

<think><details><summary>Thought Process</summary>从输出可以看到，用户当前的用户是 `wall`，而不是 `root`。而且 PATH 中已经包含了 `/home/<USER>/.local/bin`，但是 uv 是安装在 `/root/.local/bin` 的。

这里有几个问题：
1. 用户当前是 `wall` 用户，但是 uv 被安装在了 `/root/.local/bin`
2. PATH 中包含的是 `/home/<USER>/.local/bin`，而不是 `/root/.local/bin`

我需要：
1. 检查 uv 实际安装在哪里
2. 确认当前用户
3. 提供正确的解决方案</details></think>

---

我看到你当前是 `wall` 用户，但是 uv 被安装到了 `/root/.local/bin`。让我们先确认一下具体情况：

## 2. 检查 uv 的安装位置和当前用户

---

```bash
whoami
```

```
(base) wall@kaadas:/workspace/video_unstanding$ whoami
wall
(base) wall@kaadas:/workspace/video_unstanding$
```

---

```bash
ls -la /root/.local/bin/uv
```

---

