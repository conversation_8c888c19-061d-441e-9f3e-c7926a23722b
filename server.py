import asyncio
import base64
import io
import oss2
import uuid
import json
import yaml
import os
import time
import ray
import pytz
import traceback
import logging
import logging.handlers  # 添加这个导入
from datetime import datetime
from typing import List, Union, Optional, Literal
import aiohttp
from fastapi import FastAPI
from ray import serve
from utils import init_ray,TrackedActorPool
from kafka_actor import KafkaActor
from redis_actor import RedisActor
from oss_manager import OSSDataManager  # 添加OSS管理器

from pydantic import BaseModel, Field

# 配置日志
def setup_logging():
    """配置日志处理"""
    # 创建日志目录,使用项目的地址
    log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "logs","video_unstanding")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 生成日志文件名（包含日期）
    current_date = datetime.now().strftime("%Y%m%d")
    log_file = os.path.join(log_dir, f"video_unstanding_{current_date}.log")
    
    # 创建 formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=100*1024*1024,  # 100MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # 配置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    root_logger.handlers.clear()
    
    # 添加处理器
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # 配置特定模块的日志级别
    logging.getLogger('aiokafka').setLevel(logging.INFO)
    logging.getLogger('kafka').setLevel(logging.INFO)
    logging.getLogger('ray').setLevel(logging.INFO)

    
    # 创建应用专用的日志记录器
    logger = logging.getLogger('video_unstanding')
    logger.setLevel(logging.INFO)
    
    return logger


#覆盖ray日志
os.environ['RAY_DEDUP_LOGS'] = '0'


#查询请求的模型
class SearchRequest(BaseModel):
    esn: str = Field(..., description="门锁序列号")
    query_text: str = Field(..., description="查询文本")
    limit: int = Field(default=5, description="返回结果数量")
    # drop_ratio: float = Field(default=0.2, description="drop_ratio")
    # search_types: List[Literal["title", "summary", "frame"]] = Field(
    #     default=["summary"],
    #     description="搜索类型列表, 可选值为 'title', 'summary', 'frame'"
    # )

#查询响应的模型
class SearchResponse(BaseModel):
    results: List[dict]

class ProduceMessageRequest(BaseModel):
    task_id: str = Field(..., description="任务ID")
    file_url: str = Field(..., description="视频文件URL")
    esn: str = Field(..., description="设备序列号")
    start_time: Optional[int] = Field(default=None, description="开始时间戳（微秒），为空则使用当前时间")
    retry_count: int = Field(default=0, description="重试次数")

class ProduceMessageResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    task_id: str = Field(..., description="任务ID")
    topic: str = Field(..., description="发送到的Kafka主题")

class ResetCollectionResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    video_collection: str = Field(..., description="视频集合名称")
    frame_collection: str = Field(..., description="帧集合名称")

class OSSStatsResponse(BaseModel):
    video_analysis_count: int = Field(..., description="视频分析数据数量")
    search_logs_count: int = Field(..., description="搜索日志数量")
    training_datasets_count: int = Field(..., description="训练数据集数量")
    last_updated: str = Field(..., description="最后更新时间")

class CreateTrainingDatasetRequest(BaseModel):
    dataset_name: str = Field(..., description="数据集名称")
    version: str = Field(..., description="版本号")
    description: str = Field(default="", description="数据集描述")
    start_date: Optional[str] = Field(default=None, description="开始日期 (YYYY-MM-DD)")
    end_date: Optional[str] = Field(default=None, description="结束日期 (YYYY-MM-DD)")
    esn_filter: Optional[List[str]] = Field(default=None, description="ESN过滤列表")
    min_score: Optional[float] = Field(default=0.0, description="最小相似度分数")

class CreateTrainingDatasetResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    dataset_path: str = Field(..., description="数据集OSS路径")
    data_count: int = Field(..., description="数据条数")

app = FastAPI()


@serve.deployment()
@serve.ingress(app)
class VideoUnstandingService:
    def __init__(self):
        self.logger = setup_logging()
        self.logger.info("VideoUnstandingService initialized")
        self.config = self.load_config()
        init_ray(self.config)  # 初始化 Ray

        self._setup_actor_pool()
        
        # 初始化连接 - 分离Kafka和Redis连接
        self._setup_redis_pool()  # 设置Redis连接池
        self._setup_kafka_pool()  # 设置Kafka生产者连接池
        asyncio.create_task(self._setup_dedicated_consumer())  # 设置专用Kafka消费者连接
        
        # 使用Redis分布式队列代替本地队列
        self.redis_queue_key = "video_unstanding:message_queue"  # Redis中的队列键名
        self.logger.info(f"使用Redis分布式队列: {self.redis_queue_key}")
        
        # Redis中存储活跃实例的键
        self.redis_instances_key = "video_unstanding:active_instances"
        
        # 生成实例ID - 使用主机名+进程ID确保唯一性
        import socket
        import os
        self.instance_id = f"{socket.gethostname()}:{os.getpid()}"
        self.logger.info(f"实例ID: {self.instance_id}")
        
        # 初始化处理时间跟踪
        self.processing_times: List[float] = []
        self.max_history = 30  # 保留最近30条记录计算平均值
        
        self.logger.info("Starting VideoUnstanding Service...")
        
        # 初始化 FastAPI 生命周期管理
        self._serve_asgi_lifespan = app.router.lifespan_context

        # 启动消费者循环和处理循环
        self.consumer_task = asyncio.create_task(self.consume_from_kafka())
        self.processor_task = asyncio.create_task(self.process_from_queue())
        self.logger.info("Kafka consumer and queue processor loops started")
        
        # 检查队列初始状态
        asyncio.create_task(self._check_queue_status())
        
        # 注册实例并保持心跳
        self.heartbeat_task = asyncio.create_task(self._instance_heartbeat())
        self.logger.info(f"Instance heartbeat task started for instance {self.instance_id}")
        
        # 启动指标上报任务
        self.metrics_task = asyncio.create_task(self.report_metrics_for_scaling())
        self.logger.info("Metrics reporting task started")

        # 启动Actor超时检测
        self.timeout_check_task = asyncio.create_task(self._check_actor_timeouts())
        self.logger.info("Actor timeout monitoring started")

        # 初始化OSS数据管理器
        try:
            self.oss_manager = OSSDataManager()
            self.logger.info("OSS Manager initialized successfully in VideoUnstandingService")
        except Exception as e:
            self.logger.warning(f"OSS Manager initialization failed: {e}. OSS features will be disabled.")
            self.oss_manager = None

    
    def get_config_path(self):
        """获取配置文件路径"""
        deploy_mode = os.getenv("DEPLOY_MODE", "dev")
        config_name = f"config_{deploy_mode}.yaml"
        return os.path.join(os.path.dirname(__file__), config_name)

    
    def load_config(self):
        """加载配置"""
        config_path = self.get_config_path()
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    # 添加Redis队列操作辅助方法
    async def _redis_queue_size(self):
        """获取Redis队列大小"""
        redis_actor = await self.get_redis_actor()
        try:
            size = await redis_actor.redis_llen.remote(self.redis_queue_key)
            return size
        finally:
            await self.push_redis_actor(redis_actor)
    
    async def _redis_queue_empty(self):
        """检查Redis队列是否为空"""
        size = await self._redis_queue_size()
        return size == 0
    
    async def _redis_queue_push(self, message):
        """向Redis队列添加消息"""
        redis_actor = await self.get_redis_actor()
        try:
            # 将消息序列化为JSON字符串
            message_json = json.dumps(message)
            # 使用LPUSH将消息添加到队列左侧(头部)
            await redis_actor.redis_lpush.remote(self.redis_queue_key, message_json)
            # 设置队列过期时间，避免队列无限增长
            await redis_actor.redis_expire.remote(self.redis_queue_key, 86400)  # 24小时过期
        finally:
            await self.push_redis_actor(redis_actor)
    
    async def _redis_queue_pop(self):
        """从Redis队列获取消息，如果队列为空则返回None"""
        redis_actor = await self.get_redis_actor()
        try:
            # 使用RPOP从队列右侧(尾部)取出消息
            message_json = await redis_actor.redis_rpop.remote(self.redis_queue_key)
            if message_json:
                # 将JSON字符串反序列化为字典
                return json.loads(message_json)
            return None
        finally:
            await self.push_redis_actor(redis_actor)
            
    async def _check_queue_status(self):
        """定期检查队列状态"""
        await asyncio.sleep(10)  # 等待系统启动
        self.logger.info("Starting queue status checker")
        
        while True:
            try:
                queue_size = await self._redis_queue_size()
                self.logger.info(f"Queue status - current size: {queue_size}")
                await asyncio.sleep(30)  # 每30秒检查一次
            except Exception as e:
                self.logger.error(f"Error checking queue status: {e}")
                await asyncio.sleep(10)

    async def __del__(self):
        """清理资源"""
        try:
            # 注销实例
            try:
                redis_actor = await self.get_redis_actor()
                await redis_actor.redis_srem.remote(self.redis_instances_key, self.instance_id)
                self.logger.info(f"Instance {self.instance_id} unregistered")
                await self.push_redis_actor(redis_actor)
            except Exception as e:
                self.logger.error(f"Error unregistering instance: {e}")
            
            # 停止心跳任务
            if hasattr(self, 'heartbeat_task'):
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 停止消费者任务
            if hasattr(self, 'consumer_task'):
                self.consumer_task.cancel()
                try:
                    await self.consumer_task
                except asyncio.CancelledError:
                    pass
                    
            # 停止处理任务
            if hasattr(self, 'processor_task'):
                self.processor_task.cancel()
                try:
                    await self.processor_task
                except asyncio.CancelledError:
                    pass
                    
            # 停止指标上报任务
            if hasattr(self, 'metrics_task'):
                self.metrics_task.cancel()
                try:
                    await self.metrics_task
                except asyncio.CancelledError:
                    pass
            
            # 停止Actor超时检查任务
            if hasattr(self, 'timeout_check_task'):
                self.timeout_check_task.cancel()
                try:
                    await self.timeout_check_task
                except asyncio.CancelledError:
                    pass
            
            # 停止常规连接管理器
            if hasattr(self, 'conn_actors_pool'):
                list(self.conn_actors_pool.map(lambda actor,value: actor.stop.remote(),[None]*self.config.get('ray', {}).get('num_conn_actors', 2)))
            
            # 停止消费者专用连接管理器
            if hasattr(self, 'consumer_actors_pool'):
                num_consumer_actors = self.config.get('ray', {}).get('num_consumer_actors', 2)
                list(self.consumer_actors_pool.map(lambda actor,value: actor.stop.remote(),[None]*num_consumer_actors))
                self.logger.info(f"Consumer connection actors stopped")
                
            # 调用父类的 __del__ 方法
            if hasattr(self, '_serve_asgi_lifespan'):
                await self._serve_asgi_lifespan.__aexit__(None, None, None)
                
        except Exception as e:
            self.logger.error(f"Error cleaning up resources: {e}")

    def _setup_actor_pool(self):
        """设置 Actor Pool"""
        try:
            from video_processor import VideoProcessor
            from vector_search_actor import VectorSearchActor
            
            # 创建视频处理Actor Pool
            num_video_actors = self.config.get("VLM").get('num_actors', 3)  # 默认创建3个视频处理actor
            video_actors = [VideoProcessor.remote() for _ in range(num_video_actors)]
            self.video_actor_pool = TrackedActorPool(video_actors)
            self.logger.info(f"Created VideoProcessor ActorPool with {num_video_actors} actors")
            
            # 创建向量检索Actor Pool
            num_search_actors = self.config.get("VectorSearch", {}).get('num_actors', 2)  # 默认创建2个检索actor
            search_actors = [VectorSearchActor.remote() for _ in range(num_search_actors)]
            self.search_actor_pool = TrackedActorPool(search_actors)
            self.logger.info(f"Created VectorSearchActor ActorPool with {num_search_actors} actors")
            
            # 保持向后兼容性
            self.actor_pool = self.video_actor_pool
            
        except Exception as e:
            self.logger.error(f"Error setting up actor pools: {e}")
            raise

    async def get_search_actor(self, max_retries=5, retry_interval=1):
        """获取向量检索Actor"""
        for attempt in range(max_retries):
            search_actor = self.search_actor_pool.pop_idle()
            if search_actor is not None:
                return search_actor
            
            self.logger.warning(f"所有向量检索Actor都繁忙 ({attempt+1}/{max_retries})，等待中...")
            await asyncio.sleep(retry_interval)
        
        error_msg = f"无法获取空闲的向量检索Actor，已重试{max_retries}次"
        self.logger.error(error_msg)
        raise TimeoutError(error_msg)

    def push_search_actor(self, actor):
        """将向量检索Actor推回连接池"""
        try:
            self.search_actor_pool.push(actor)
        except Exception as e:
            self.logger.error(f"Error pushing search actor back to pool: {e}")
            raise

    def _setup_redis_pool(self):
        """设置Redis连接池"""
        try:
            # 创建Redis连接管理器Actor
            num_redis_actors = self.config.get('redis').get('num_redis_actors', 3)  # 默认创建3个Redis连接actor
            redis_actors = [
                RedisActor.options(
                    name=f"RedisActor_{i}",
                    namespace="video_unstanding_service"
                ).remote(self.config) 
                for i in range(num_redis_actors)
            ]
            
            # 使用TrackedActorPool管理Redis连接
            self.redis_actors_pool = TrackedActorPool(redis_actors)
            
            # 启动连接
            list(self.redis_actors_pool.map(
                lambda actor, value: actor.start.remote(),
                [None] * num_redis_actors
            ))
            
            self.logger.info(f"Redis connection pool started with {num_redis_actors} actors")
        except Exception as e:
            self.logger.error(f"Error setting up Redis connections: {e}")
            raise

    def _setup_kafka_pool(self):
        """设置Kafka生产者连接池"""
        try:
            # 创建Kafka连接管理器Actor - 这些用于发送消息
            num_kafka_actors = self.config.get('kafka').get('num_kafka_actors', 3)  # 默认创建3个Kafka生产者连接
            kafka_actors = [
                KafkaActor.options(
                    name=f"KafkaProducerActor_{i}",
                    namespace="video_unstanding_service"
                ).remote(self.config, is_producer_only=True)  # 添加is_producer_only=True参数
                for i in range(num_kafka_actors)
            ]
            
            # 使用TrackedActorPool管理Kafka连接
            self.kafka_actors_pool = TrackedActorPool(kafka_actors)
            
            # 启动连接
            list(self.kafka_actors_pool.map(
                lambda actor, value: actor.start.remote(),
                [None] * num_kafka_actors
            ))
            
            self.logger.info(f"Kafka producer pool started with {num_kafka_actors} actors")
        except Exception as e:
            self.logger.error(f"Error setting up Kafka producer connections: {e}")
            raise

    async def _setup_dedicated_consumer(self):
        """设置专用Kafka消费者连接"""
        try:
            # 创建单个专用的Kafka消费者连接
            self.logger.info("Creating dedicated Kafka consumer connection")
            
            # 使用KafkaActor创建专用消费者 - 显式使用is_producer_only=False
            self.dedicated_consumer = KafkaActor.options(
                name="DedicatedConsumerActor",
                namespace="video_unstanding_service"
            ).remote(self.config, is_producer_only=False)
            
            # 启动连接
            start_result = await self.dedicated_consumer.start.remote()
            
            if start_result:
                self.logger.info("Dedicated Kafka consumer started successfully")
            else:
                self.logger.error("Failed to start dedicated Kafka consumer")
                raise RuntimeError("Failed to start dedicated Kafka consumer")
                
        except Exception as e:
            self.logger.error(f"Error setting up dedicated consumer: {e}")
            raise

    async def get_redis_actor(self, max_retries=10, retry_interval=2, backoff_factor=2):
        """获取Redis连接Actor
        
        Args:
            max_retries: 最大重试次数，默认10次
            retry_interval: 初始重试间隔时间(秒)，默认2秒
            backoff_factor: 退避因子，每次重试后等待时间会乘以这个因子，默认2
            
        Returns:
            Redis连接Actor实例
            
        Raises:
            TimeoutError: 当超过最大重试次数仍无法获取空闲Actor时抛出
        """
        # 尝试获取空闲的Actor
        current_interval = retry_interval  # 初始等待时间
        
        # 获取当前繁忙Actor数量的信息
        busy_count = self.redis_actors_pool.get_busy_count() if hasattr(self.redis_actors_pool, 'get_busy_count') else "unknown"
        total_count = self.redis_actors_pool.get_total_count() if hasattr(self.redis_actors_pool, 'get_total_count') else "unknown"
        self.logger.debug(f"Getting Redis actor. Current busy actors: {busy_count}/{total_count}")
        
        for attempt in range(max_retries):
            redis_actor = self.redis_actors_pool.pop_idle()  # 从连接池中获取空闲Actor
            
            if redis_actor is not None:
                # 找到空闲Actor，直接返回
                return redis_actor
            
            # 如果所有Actor都繁忙，记录详细日志
            if hasattr(self.redis_actors_pool, 'get_busy_info'):
                busy_info = self.redis_actors_pool.get_busy_info()
                if busy_info:
                    self.logger.warning(f"所有Redis Actor都繁忙 ({attempt+1}/{max_retries})，详情: {', '.join(busy_info)}")
                else:
                    self.logger.warning(f"所有Redis Actor都繁忙 ({attempt+1}/{max_retries})，但无法获取详细信息")
            else:
                self.logger.warning(f"所有Redis Actor都繁忙 ({attempt+1}/{max_retries})，等待时间: {current_interval}秒")
            
            # 每隔3次尝试强制检查超时Actor
            if attempt > 0 and attempt % 3 == 0 and hasattr(self.redis_actors_pool, 'check_timeouts'):
                self.logger.warning(f"第{attempt}次尝试失败，强制检查超时Actor")
                await self.redis_actors_pool.check_timeouts()
            
            await asyncio.sleep(current_interval)  # 异步等待一段时间后重试
            
            # 使用指数退避增加等待时间
            current_interval = current_interval * backoff_factor
        
        # 超过最大重试次数仍无法获取空闲Actor
        error_msg = f"无法获取空闲的Redis Actor，已重试{max_retries}次"
        self.logger.error(error_msg)
        
        # 当达到最大重试次数时，强制重置所有Actor
        if hasattr(self.redis_actors_pool, 'check_timeouts'):
            self.logger.warning("强制检查并重置超时Actor")
            await self.redis_actors_pool.check_timeouts()
        
        raise TimeoutError(error_msg)
    
    async def push_redis_actor(self, actor):
        """将Redis Actor推回连接池"""
        try:
            self.redis_actors_pool.push(actor)
        except Exception as e:
            self.logger.error(f"Error pushing Redis actor back to pool: {e}")
            raise

    async def get_kafka_actor(self, max_retries=10, retry_interval=2, backoff_factor=2):
        """获取Kafka生产者连接Actor
        
        Args:
            max_retries: 最大重试次数，默认10次
            retry_interval: 初始重试间隔时间(秒)，默认2秒
            backoff_factor: 退避因子，每次重试后等待时间会乘以这个因子，默认2
            
        Returns:
            Kafka生产者连接Actor实例
            
        Raises:
            TimeoutError: 当超过最大重试次数仍无法获取空闲Actor时抛出
        """
        # 尝试获取空闲的Actor
        current_interval = retry_interval  # 初始等待时间
        
        # 获取当前繁忙Actor数量的信息
        busy_count = self.kafka_actors_pool.get_busy_count() if hasattr(self.kafka_actors_pool, 'get_busy_count') else "unknown"
        total_count = self.kafka_actors_pool.get_total_count() if hasattr(self.kafka_actors_pool, 'get_total_count') else "unknown"
        self.logger.debug(f"Getting Kafka producer actor. Current busy actors: {busy_count}/{total_count}")
        
        for attempt in range(max_retries):
            kafka_actor = self.kafka_actors_pool.pop_idle()  # 从连接池中获取空闲Actor
            
            if kafka_actor is not None:
                # 找到空闲Actor，直接返回
                return kafka_actor
            
            # 如果所有Actor都繁忙，记录详细日志
            if hasattr(self.kafka_actors_pool, 'get_busy_info'):
                busy_info = self.kafka_actors_pool.get_busy_info()
                if busy_info:
                    self.logger.warning(f"所有Kafka生产者Actor都繁忙 ({attempt+1}/{max_retries})，详情: {', '.join(busy_info)}")
                else:
                    self.logger.warning(f"所有Kafka生产者Actor都繁忙 ({attempt+1}/{max_retries})，但无法获取详细信息")
            else:
                self.logger.warning(f"所有Kafka生产者Actor都繁忙 ({attempt+1}/{max_retries})，等待时间: {current_interval}秒")
            
            # 每隔3次尝试强制检查超时Actor
            if attempt > 0 and attempt % 3 == 0 and hasattr(self.kafka_actors_pool, 'check_timeouts'):
                self.logger.warning(f"第{attempt}次尝试失败，强制检查超时Actor")
                await self.kafka_actors_pool.check_timeouts()
            
            await asyncio.sleep(current_interval)  # 异步等待一段时间后重试
            
            # 使用指数退避增加等待时间
            current_interval = current_interval * backoff_factor
        
        # 超过最大重试次数仍无法获取空闲Actor
        error_msg = f"无法获取空闲的Kafka生产者Actor，已重试{max_retries}次"
        self.logger.error(error_msg)
        
        # 当达到最大重试次数时，强制重置所有Actor
        if hasattr(self.kafka_actors_pool, 'check_timeouts'):
            self.logger.warning("强制检查并重置超时Actor")
            await self.kafka_actors_pool.check_timeouts()
        
        raise TimeoutError(error_msg)
    
    async def push_kafka_actor(self, actor):
        """将Kafka生产者Actor推回连接池"""
        try:
            self.kafka_actors_pool.push(actor)
        except Exception as e:
            self.logger.error(f"Error pushing Kafka actor back to pool: {e}")
            raise

    def reconfigure(self, user_config: dict):
        self.logger.info("VideoUnstandingService reconfigured")

    async def check_health(self):
        """健康检查方法"""
        try:
            # 检查连接状态
            conn_actor = await self.get_redis_actor()
            status_ref = conn_actor.check_health.remote()
            status = await status_ref  # ray.get不需要await
            
            # 记录详细的健康状态
            self.logger.info(f"Health check status: {status}")
            
            # 检查每个连接的状态并记录具体问题
            connection_issues = []
            if not status.get('kafka_producer', False):
                msg = "Kafka producer is not healthy"
                self.logger.warning(msg)  # 降低严重性，仅警告而不是错误
            if not status.get('kafka_consumer', False):
                msg = "Kafka consumer is not healthy"
                self.logger.warning(msg)  # 降低严重性，仅警告而不是错误
            if not status.get('redis', False):
                msg = "Redis connection is not healthy"
                self.logger.error(msg)
                connection_issues.append(msg)
            
            # 只有Redis连接问题时才返回失败
            if status.get('redis', False) == False:
                raise RuntimeError(f"Critical connection issues: {'; '.join(connection_issues)}")
                
            self.logger.info("Essential connections are healthy (Redis is working)")
            return True
                
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            raise
        finally:
            await self.push_redis_actor(conn_actor)
    
    async def consume_from_kafka(self):
        """从Kafka消费消息并放入队列"""
        self.logger.info("Starting Kafka consumer loop with dedicated consumer...")
        # 消息记录只记录当天，如果新的一天则重置为0
        
        message_counter = 0  # 记录处理的消息总数
        
        # 添加日期跟踪，用于每天重置计数器
        shanghai_tz = pytz.timezone('Asia/Shanghai')
        current_date = datetime.now(shanghai_tz).strftime('%Y-%m-%d')
        self.logger.info(f"初始化消息计数器，当前日期: {current_date}")
        
        try:
            # 使用专用的Kafka消费者实例
            self.logger.info("Using dedicated Kafka consumer for the entire consumption loop")
            
            while True:
                try:
                    # 检查日期是否发生变化，如果变化则重置计数器
                    shanghai_tz = pytz.timezone('Asia/Shanghai')
                    today = datetime.now(shanghai_tz).strftime('%Y-%m-%d')
                    if today != current_date:
                        self.logger.info(f"日期已变更: {current_date} -> {today}，重置消息计数器")
                        current_date = today
                        message_counter = 0  # 重置消息计数器
                    
                    # 获取消息批次 - 使用专用消费者连接
                    self.logger.debug("Attempting to fetch messages from Kafka...")
                    messages = await self.dedicated_consumer.get_messages.remote(
                        max_records=self.config.get('kafka', {}).get('max_poll_records', 1000),
                        timeout_ms=1000
                    ) 
                    
                    if not messages:
                        self.logger.debug("No messages received from Kafka, waiting...")
                        # 即使没有消息也提交偏移量，防止积压
                        await self.dedicated_consumer.commit.remote()
                        await asyncio.sleep(1)
                        continue
                    
                    # 区分消息类型并记录
                    task_messages = [msg for msg in messages if msg.get('type') != 'health_check']
                    health_check_messages = [msg for msg in messages if msg.get('type') == 'health_check']
                    
                    if task_messages:
                        self.logger.info(f"Received {len(task_messages)} task messages from Kafka")
                    if health_check_messages:
                        self.logger.debug(f"Received {len(health_check_messages)} health check messages")
                    
                    # 将消息放入Redis队列
                    put_count = 0
                    for message in messages:
                        # 跳过健康检查消息
                        if message.get('type') == 'health_check':
                            continue
                        
                        try:
                            task_id = message.get('taskId', 'unknown')
                            message_counter += 1
                            
                            # 使用Redis队列代替本地队列
                            await self._redis_queue_push(message)
                            
                            put_count += 1
                            self.logger.info(f"Message #{message_counter} with taskId {task_id} put into Redis queue")
                            
                            # 每放入5条消息检查一次队列大小
                            if message_counter % 5 == 0:
                                queue_size = await self._redis_queue_size()
                                self.logger.info(f"Queue status after {message_counter} messages: size = {queue_size}")
                                
                        except Exception as e:
                            self.logger.error(f"Error putting message into Redis queue: {e}")
                            self.logger.error(traceback.format_exc())
                    
                    if put_count > 0:
                        self.logger.info(f"Put {put_count} messages into Redis queue")
                    
                    # 提交消费位移
                    await self.dedicated_consumer.commit.remote()
                    self.logger.debug("Offset committed after processing messages")
                    
                    # 每1000批消息检查一次连接健康状态
                    if message_counter % 1000 == 0:
                        health = await self.dedicated_consumer.check_health.remote()
                        if not health.get('kafka_consumer', False):
                            self.logger.warning("Kafka消费者连接异常，尝试重新连接")
                            # 重新启动专用消费者
                            await self.dedicated_consumer.restart.remote()
                            self.logger.info("Kafka消费者连接已重新建立")
                    
                    # 短暂休眠，避免过度轮询
                    await asyncio.sleep(0.2)
                    
                except Exception as e:
                    self.logger.error(f"Error consuming from Kafka: {str(e)}")
                    self.logger.error(traceback.format_exc())
                    
                    # 连接可能有问题，尝试重新连接
                    try:
                        self.logger.warning("尝试重新启动Kafka消费者连接")
                        await self.dedicated_consumer.restart.remote()
                        self.logger.info("Kafka消费者连接已重新启动")
                    except Exception as restart_err:
                        self.logger.error(f"重启Kafka消费者连接失败: {restart_err}")
                    
                    await asyncio.sleep(1)
        finally:
            # Kafka消费循环结束时的清理工作
            self.logger.info("Kafka消费循环结束")
    async def process_from_queue(self):
        """处理队列中的消息"""
        self.logger.info("Starting queue processor loop...")
        # 跟踪正在进行的任务
        active_tasks = set()
        processed_count = 0  # 记录已处理的消息数
        
        # 添加日期跟踪，用于每天重置计数器
        shanghai_tz = pytz.timezone('Asia/Shanghai')
        current_date = datetime.now(shanghai_tz).strftime('%Y-%m-%d')
        self.logger.info(f"初始化处理计数器，当前日期: {current_date}")
        
        while True:
            try:
                # 检查日期是否发生变化，如果变化则重置计数器
                shanghai_tz = pytz.timezone('Asia/Shanghai')
                today = datetime.now(shanghai_tz).strftime('%Y-%m-%d')
                if today != current_date:
                    self.logger.info(f"日期已变更: {current_date} -> {today}，重置处理计数器")
                    current_date = today
                    processed_count = 0
                    # 同时清空处理时间历史记录，确保平均时间也是当天的
                    self.processing_times = []
                
                # 清理已完成的任务
                prev_active = len(active_tasks)
                active_tasks = {task for task in active_tasks if not task.done()}
                completed = prev_active - len(active_tasks)
                
                if completed > 0:
                    self.logger.info(f"{completed} tasks completed, {len(active_tasks)} still active")
                
                # 检查队列状态
                try:
                    # 使用Redis队列方法检查队列大小
                    queue_size = await self._redis_queue_size()
                    if queue_size > 0:
                        self.logger.info(f"Queue has {queue_size} messages waiting to be processed")
                except Exception as e:
                    self.logger.error(f"Error checking queue size: {e}")
                
                # 控制并发数量，避免过多任务同时执行
                max_concurrent = self.config.get("VLM").get('num_actors', 3)
                if len(active_tasks) >= max_concurrent:
                    self.logger.info(f"Reached max concurrent tasks ({max_concurrent}), waiting for some to complete")
                    # 等待任意一个任务完成
                    done, active_tasks = await asyncio.wait(
                        active_tasks, 
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    # 检查已完成任务的异常
                    for task in done:
                        try:
                            await task
                            processed_count += 1
                            if processed_count % 10 == 0:
                                self.logger.info(f"今日已处理 {processed_count} 条消息 ({current_date})")
                        except Exception as e:
                            self.logger.error(f"Task processing error: {e}")
                            self.logger.error(traceback.format_exc())
                
                # 尝试获取消息，如果队列为空则等待一段时间
                try:
                    # 使用Redis队列方法检查队列是否为空
                    if await self._redis_queue_empty():
                        self.logger.debug("Queue is empty, waiting...")
                        await asyncio.sleep(0.5)  # 增加等待时间，减少空轮询
                        continue
                    
                    # 从Redis队列获取消息
                    message = await self._redis_queue_pop()
                    
                    if not message:  # 额外检查，以防万一
                        self.logger.debug("Received empty message from queue, skipping...")
                        await asyncio.sleep(0.5)
                        continue
                    
                    # 创建跟踪数据对象，用于记录处理信息
                    task_metadata = {
                        "start_time": time.time(),
                        "task_id": message.get('taskId', 'unknown'),
                        "skipped": False,  # 默认未跳过
                        "message": message
                    }
                    
                    task_id = task_metadata["task_id"]
                    self.logger.info(f"Got message with taskId {task_id} from Redis queue")
                    
                    # 为每条消息创建一个新的异步任务，避免阻塞主循环
                    self.logger.info(f"Creating task to process message {task_id}")
                    task = asyncio.create_task(self.consume(message, task_metadata))
                    
                    # 添加完成回调来记录处理时间
                    task.add_done_callback(
                        lambda future, metadata=task_metadata: 
                            self._record_processing_time(future, metadata)
                    )
                    
                    active_tasks.add(task)
                    
                except Exception as e:
                    self.logger.error(f"Error getting message from Redis queue: {e}")
                    self.logger.error(traceback.format_exc())
                    await asyncio.sleep(1)
                    continue
                
            except Exception as e:
                self.logger.error(f"Error in queue processor: {str(e)}")
                self.logger.error(traceback.format_exc())
                await asyncio.sleep(1)


    def _record_processing_time(self, future, metadata):
        """记录消息处理时间"""
        try:
            # 检查任务是否成功完成
            if not future.cancelled():
                try:
                    # 尝试获取结果，如果有异常会在这里抛出
                    future.result()
                    
                    # 计算处理时间
                    processing_time = time.time() - metadata["start_time"]
                    task_id = metadata["task_id"]
                    
                    # 只记录未跳过的消息处理时间
                    if not metadata.get("skipped", False):
                        # 保存处理时间到历史记录
                        self.processing_times.append(processing_time)
                        
                        # 保持历史记录大小在限制范围内
                        while len(self.processing_times) > self.max_history:
                            self.processing_times.pop(0)
                        
                        self.logger.info(f"Message {task_id} processed in {processing_time:.2f} seconds (recorded for avg calculation)")
                    else:
                        self.logger.info(f"Message {task_id} skipped processing in {processing_time:.2f} seconds (not counted in avg)")
                    
                except Exception:
                    # 任务执行中出现异常，不记录处理时间
                    pass
        except Exception as e:
            self.logger.error(f"Error recording processing time: {e}")

    async def consume(self, message=None, metadata=None):
        """处理消息"""
        conn_actor = await self.get_redis_actor()
        try:
            if not message:
                self.logger.debug("No message provided to consume, skipping...")
                return
            
            # 获取重试次数，默认为0
            retry_count = message.get('retry_count', 0)
            max_retries = self.config.get('Consume_policy', {}).get('max_retries', 3)
            
            # 记录下当前的时间，为遇到临近午夜12点时，任务还没处理完，最终的合并视频就已经开始生成，
            # 导致最终视频缺失，故需要redis做状态管理
            #上海时区

            tag = str(uuid.uuid4())
            event_id = message.get('taskId', 'unknown') # 任务id就是EventId
            file_url = message.get('fileUrl')
            esn = message.get('esn')
            shanghai_tz = pytz.timezone('Asia/Shanghai')
            
            # 使用上海时区生成当前时间戳
            now = datetime.now(shanghai_tz)
            now_ts = int(now.timestamp() * 1000000)  # 微秒级时间戳
            start_time = message.get('startTime', now_ts)

            # 记录原始时间戳
            self.logger.info(f"原始时间戳: {start_time}")
            
            # 标准化时间戳到微秒级
            if start_time > 10**15:  # 已经是微秒级时间戳
                timestamp_microseconds = start_time
                self.logger.info("检测到微秒级时间戳，保持不变")
            elif start_time > 10**12:  # 毫秒级时间戳
                timestamp_microseconds = start_time * 1000
                self.logger.info("检测到毫秒级时间戳，转换为微秒级")
            else:  # 秒级时间戳
                timestamp_microseconds = start_time * 1000000
                self.logger.info("检测到秒级时间戳，转换为微秒级")

            # 使用微秒级时间戳计算日期（需要先转换为秒）
            current_date = datetime.fromtimestamp(
                timestamp_microseconds / 1000000,  # 转换为秒级用于日期计算
                shanghai_tz
            ).strftime('%Y-%m-%d')
            
            # 记录详细的时间信息
            self.logger.info(f"处理后的时间戳(微秒): {timestamp_microseconds}")
            self.logger.info(f"上海时区日期: {current_date}")
            self.logger.info(f"上海时区完整时间: {datetime.fromtimestamp(timestamp_microseconds/1000000, shanghai_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")

            now_current_date = datetime.now(shanghai_tz).strftime('%Y-%m-%d')
            self.logger.info(f"当前日期: {now_current_date}")

            #　假如当前日期是昨天，则记录当天消息，键值增加1
            if current_date == now_current_date:
                #记录当天消息，键值增加1
                await conn_actor.redis_incr.remote(f"{self.config.get('Consume_policy').get('num_current_messages_count')}:{current_date}")
            else:
                self.logger.info(f"检测到当前链接的日期是{current_date},不是当天，故记录当天还在处理昨天的消息数，用于衡量服务的消费能力")
                self.logger.info(f"当前消息: {message}")
                #记录当天还在处理昨天的消息数，用于衡量视频理解服务的消费能力
                await conn_actor.redis_sadd.remote(
                    f"{self.config.get('Consume_policy').get('num_old_messages_count')}:{current_date}",
                    json.dumps(message),
                    self.config.get('Consume_policy').get('old_messages_expire_time', 172800)
                )
                # 标记为跳过
                if metadata:
                    metadata["skipped"] = True
                return

            # 打印消息
            self.logger.info(f"Processing task message: {message} (重试次数: {retry_count}/{max_retries})")
            
            if not all([event_id, file_url, esn, start_time]):
                self.logger.error(f"Missing required fields in task data: {message}")
                # 标记为跳过
                if metadata:
                    metadata["skipped"] = True
                return
                
            
            # 从URL中提取到.m3u8的部分（保留用于其他用途）
            m3u8_path = file_url.split('?')[0]

            # 使用esn+event_id作为任务唯一标识的查重键
            task_key = f"video_unstanding:task:{esn}:{event_id}"

            # 从redis中获取这个任务的状态
            redis_result = await conn_actor.redis_get.remote(task_key)
            # 添加空值检查
            if redis_result:
                try:
                    redis_result = json.loads(redis_result)
                except json.JSONDecodeError:
                    redis_result = {"status": "error"}  # 如果json解析失败，则认为任务状态为error
            else:
                redis_result = {"status": "error"}  # 如果redis_result为None，设置默认值

            self.logger.info(f"redis_result: {redis_result}")

            # 如果任务状态为completed，则跳过
            if redis_result.get('status') == 'completed':  # 使用get方法安全访问
                self.logger.info(f"Task {task_key} already completed, skipping")
                # 标记为跳过
                if metadata:
                    metadata["skipped"] = True
                return

            tasks = []
            # 如果任务状态为error或者redis_result为空，则继续处理
            if redis_result.get("status") == 'error' or redis_result.get("status") == 'failed':
                # 将任务加入到tasks中，用于处理
                tasks.append((esn, event_id, m3u8_path))  # 修改顺序：esn, event_id, url
                self.logger.info(f"Task {task_key} continue processing")


            # 使用video_actor_pool处理任务
            if tasks:
                try:
                    results = list(self.video_actor_pool.map(
                        lambda actor, task: actor.process.remote(
                            esn=task[0],
                            event_id=task[1],
                            url=task[2],
                            mode=self.config.get('video_processing').get('mode','cloud')
                        ),
                        tasks
                    ))
                    self.logger.debug(f"视频处理结果: {results}")

                    #处理返回结果

                    for result in results:
                        try:
                            # 检查结果格式
                            if not isinstance(result, dict):
                                self.logger.error(f"Invalid result format: {type(result)}, expected dict")
                                continue
                                
                            # 更新任务状态
                            status_data = {
                                "status": result.get("status", "completed"),
                                "event_id": event_id,
                                "esn": esn,
                                "fileUrl": file_url,
                                "m3u8_path": m3u8_path,
                                "unstanding_result": result,
                                "tag": tag,
                                "date": current_date,
                                "startTime": start_time,
                                "result_url": result.get("url", ""),
                                "processed_time": time.time(),
                                "processing_time": result.get("process_time", 0),
                                "file_size": result.get("file_size", 0),
                                "frames_processed": result.get("frames_processed", 0),
                                "total_frames": result.get("total_frames", 0),
                                "video_info": result.get("video_info", {}),
                                "processing_stats": result.get("processing_stats", {})
                            }
                            
                            # 将状态数据存储到Redis
                            await conn_actor.redis_set.remote(
                                task_key,
                                json.dumps(status_data),
                                self.config.get('redis', {}).get('ex', 86400)  # 24小时过期
                            )

                            # 记录处理成功的消息数
                            await conn_actor.redis_incr.remote(f"{self.config.get('Consume_policy', {}).get('num_current_success_messages_count')}:{current_date}")


                        except Exception as e:
                            error_msg = f"Error processing task {task_key}: {str(e)}"
                            self.logger.error(error_msg)
                            
                            # 检查是否还可以重试
                            if retry_count < max_retries:
                                self.logger.info(f"重试任务 {event_id}, 当前重试次数: {retry_count+1}/{max_retries}")
                                
                                # 创建新的任务对象，增加重试计数
                                retry_message = message.copy()
                                retry_message['retry_count'] = retry_count + 1
                                
                                # 增加延迟重试，避免立即重试导致相同错误
                                retry_delay = self.config.get('Consume_policy').get('retry_delay', 5) * (2 ** retry_count)
                                self.logger.info(f"任务 {event_id} 将在 {retry_delay} 秒后重试")
                                await asyncio.sleep(retry_delay)
                                
                                # 重新推回队列
                                await self._redis_queue_push(retry_message)
                                self.logger.info(f"任务 {event_id} 已重新推回队列等待重试")
                            else:
                                self.logger.warning(f"任务 {event_id} 已达到最大重试次数 {max_retries}，不再重试")
                                # 更新任务状态为永久失败
                                await conn_actor.redis_set.remote(
                                    task_key,
                                    json.dumps({
                                        "status": "failed",
                                        "event_id": event_id,
                                        "esn": esn,
                                        "fileUrl": file_url,
                                        "error_message": str(e),
                                        "create_time": time.time(),
                                        "processed_time": time.time(),
                                        "retry_count": retry_count,
                                        "max_retries": max_retries
                                    }),
                                    self.config.get('redis', {}).get('ex', 86400)
                                )
                                
                                # 记录失败任务
                                failed_tasks_key = f"video_unstanding:failed_tasks:{current_date}"
                                await conn_actor.redis_sadd.remote(
                                    failed_tasks_key,
                                    json.dumps({
                                        "event_id": event_id,
                                        "esn": esn,
                                        "fileUrl": file_url,
                                        "error_message": str(e),
                                        "retry_count": retry_count,
                                        "failed_time": time.time()
                                    }),
                                    86400 * 7  # 保存7天
                                )
                            
                except Exception as batch_error:
                    # 处理整个批次出错的情况
                    self.logger.error(f"处理任务批次出错: {str(batch_error)}")
                    self.logger.error(traceback.format_exc())
                    
                    # 检查是否还可以重试
                    if retry_count < max_retries:
                        self.logger.info(f"重试任务 {event_id}, 当前重试次数: {retry_count+1}/{max_retries}")
                        
                        # 创建新的任务对象，增加重试计数
                        retry_message = message.copy()
                        retry_message['retry_count'] = retry_count + 1
                        
                        # 增加延迟重试，避免立即重试导致相同错误
                        retry_delay = self.config.get('Consume_policy').get('retry_delay', 5) * (2 ** retry_count)
                        self.logger.info(f"任务 {event_id} 将在 {retry_delay} 秒后重试")
                        await asyncio.sleep(retry_delay)
                        
                        # 重新推回队列
                        await self._redis_queue_push(retry_message)
                        self.logger.info(f"任务 {event_id} 已重新推回队列等待重试")
                    else:
                        self.logger.warning(f"任务 {event_id} 已达到最大重试次数 {max_retries}，不再重试")
                        # 记录失败任务
                        failed_tasks_key = f"video_unstanding:failed_tasks:{current_date}"
                        await conn_actor.redis_sadd.remote(
                            failed_tasks_key,
                            json.dumps({
                                "event_id": event_id,
                                "esn": esn,
                                "fileUrl": file_url,
                                "error_message": str(batch_error),
                                "retry_count": retry_count,
                                "failed_time": time.time()
                            }),
                            86400 * 7  # 保存7天
                        )

        except Exception as e:
            self.logger.error(f"消费失败: {str(e)}")
            self.logger.error(traceback.format_exc())
            
            # 记录处理失败的消息数和消息
            try:
                if 'current_date' in locals() and 'message' in locals():
                    await conn_actor.redis_incr.remote(f"{self.config.get('Consume_policy').get('num_current_error_messages_count')}:{current_date}")
                    await conn_actor.redis_sadd.remote(
                        f"{self.config.get('Consume_policy').get('num_current_error_messages')}:{current_date}",
                        json.dumps(message),
                        self.config.get('Consume_policy', {}).get('old_messages_expire_time', 172800)
                    )
                    
                    # 检查是否还可以重试
                    retry_count = message.get('retry_count', 0)
                    max_retries = self.config.get('Consume_policy').get('max_retries', 3)
                    
                    if retry_count < max_retries and 'event_id' in locals():
                        self.logger.info(f"重试任务 {event_id}, 当前重试次数: {retry_count+1}/{max_retries}")
                        
                        # 创建新的任务对象，增加重试计数
                        retry_message = message.copy()
                        retry_message['retry_count'] = retry_count + 1
                        
                        # 增加延迟重试，避免立即重试导致相同错误
                        retry_delay = self.config.get('Consume_policy').get('retry_delay', 5) * (2 ** retry_count)
                        self.logger.info(f"任务将在 {retry_delay} 秒后重试")
                        await asyncio.sleep(retry_delay)
                        
                        # 重新推回队列
                        await self._redis_queue_push(retry_message)
                        self.logger.info(f"任务已重新推回队列等待重试")
                    elif 'event_id' in locals():
                        self.logger.warning(f"任务 {event_id} 已达到最大重试次数 {max_retries}，不再重试")
            
            except Exception as err:
                self.logger.error(f"记录处理失败的消息失败: {str(err)}")
        
        finally:
            await self.push_redis_actor(conn_actor)

    async def _instance_heartbeat(self):
        """维护实例心跳，定期更新实例存活状态"""
        await asyncio.sleep(5)  # 等待系统启动稳定
        self.logger.info(f"Starting instance heartbeat for {self.instance_id}")
        
        while True:
            try:
                redis_actor = await self.get_redis_actor()
                
                # 获取当前实例列表
                active_instances = await redis_actor.redis_smembers.remote(self.redis_instances_key)
                
                # 向Redis注册当前实例
                await redis_actor.redis_sadd.remote(
                    self.redis_instances_key,  # 活跃实例集合键
                    self.instance_id,  # 实例ID
                    300  # 设置5分钟过期时间，实例心跳每分钟更新一次
                )
                
                # 清理僵尸实例 - 检查所有实例的最后心跳时间
                await self._cleanup_zombie_instances(redis_actor, active_instances)
                
                # 获取当前活跃实例数和每个实例的Actor数量
                current_count = len(active_instances) + (0 if self.instance_id in active_instances else 1)
                config_actors = self.config.get('VLM').get('num_actors', 3)
                
                # 记录日志 - 明确区分实例数和Actor数
                self.logger.info(f"集群状态: {current_count}个活跃服务实例, 当前实例ID: {self.instance_id}, 每实例{config_actors}个工作者(Actor)")
                
                # 更新实例状态信息
                instance_status_key = f"video_unstanding:instance:{self.instance_id}:status"
                status_data = {
                    "instance_id": self.instance_id,
                    "last_heartbeat": time.time(),
                    "active_tasks": 0,  # 这里可以添加更多有用的实例状态信息
                    "processing_times": self.processing_times[-5:] if self.processing_times else [],
                    "avg_processing_time": sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0,
                    "num_actors": config_actors  # 添加Actor数量到状态信息中
                }
                await redis_actor.redis_set.remote(
                    instance_status_key,
                    json.dumps(status_data),
                    300  # 5分钟过期
                )
                
                await self.push_redis_actor(redis_actor)
                
                # 每分钟心跳一次
                await asyncio.sleep(60)
                
            except Exception as e:
                self.logger.error(f"Error updating instance heartbeat: {e}")
                self.logger.error(traceback.format_exc())
                await asyncio.sleep(10)  # 出错时等待较短时间再重试

    async def _cleanup_zombie_instances(self, redis_actor, active_instances):
        """清理僵尸实例，移除长时间未发送心跳的实例"""
        try:
            # 获取配置参数
            zombie_timeout = self.config.get('PAI_EAS').get('zombie_instance_timeout', 600)  # 默认10分钟
            cleanup_interval = self.config.get('PAI_EAS').get('zombie_cleanup_interval', 300)  # 默认5分钟一次
            
            # 检查是否到了清理时间
            current_time = time.time()
            if not hasattr(self, 'last_zombie_cleanup') or (current_time - self.last_zombie_cleanup) >= cleanup_interval:
                self.logger.info("开始检查僵尸实例...")
                self.last_zombie_cleanup = current_time
                
                zombie_instances = []
                for instance_id in active_instances:
                    # 检查实例状态
                    instance_status_key = f"video_unstanding:instance:{instance_id}:status"
                    status_json = await redis_actor.redis_get.remote(instance_status_key)
                    
                    if not status_json:
                        # 没有状态信息的实例被视为僵尸实例
                        self.logger.warning(f"实例 {instance_id} 没有状态信息，将被标记为僵尸实例")
                        zombie_instances.append(instance_id)
                        continue
                    
                    try:
                        status = json.loads(status_json)
                        last_heartbeat = status.get('last_heartbeat', 0)
                        
                        # 如果最后心跳时间超过超时时间，将实例标记为僵尸实例
                        if (current_time - last_heartbeat) > zombie_timeout:
                            self.logger.warning(f"实例 {instance_id} 的最后心跳时间是 {datetime.fromtimestamp(last_heartbeat).strftime('%Y-%m-%d %H:%M:%S')}，"
                                        f"超过 {zombie_timeout} 秒，将被标记为僵尸实例")
                            zombie_instances.append(instance_id)
                    except Exception as e:
                        self.logger.error(f"解析实例 {instance_id} 的状态信息时出错: {e}")
                        zombie_instances.append(instance_id)
                
                # 移除僵尸实例
                if zombie_instances:
                    self.logger.warning(f"发现 {len(zombie_instances)} 个僵尸实例，准备清理: {zombie_instances}")
                    for instance_id in zombie_instances:
                        # 从活跃实例集合中移除
                        await redis_actor.redis_srem.remote(self.redis_instances_key, instance_id)
                        
                        # 设置实例状态信息过期
                        instance_status_key = f"video_unstanding:instance:{instance_id}:status"
                        await redis_actor.redis_expire.remote(instance_status_key, 1)
                        
                        self.logger.info(f"已清理僵尸实例: {instance_id}")
                    
                    # 记录清理结果
                    self.logger.info(f"僵尸实例清理完成，共清理 {len(zombie_instances)} 个实例")
                else:
                    self.logger.info("没有发现僵尸实例")
        except Exception as e:
            self.logger.error(f"清理僵尸实例时出错: {e}")
            self.logger.error(traceback.format_exc())


    async def _get_active_instance_count(self):
        """获取当前活跃的实例数量"""
        try:
            redis_actor = await self.get_redis_actor()
            active_instances = await redis_actor.redis_smembers.remote(self.redis_instances_key)
            
            instance_count = len(active_instances)
            # 确保至少返回1，避免除以零错误
            await self.push_redis_actor(redis_actor)
            return max(instance_count, 1)
        except Exception as e:
            self.logger.error(f"Error getting active instance count: {e}")
            # 出错时返回保守的配置值
            return self.config.get('VLM').get('num_actors', 3)

    async def report_metrics_for_scaling(self):
        """上报用于自动伸缩的自定义指标"""
        await asyncio.sleep(30)  # 等待系统启动稳定
        self.logger.info("Starting metrics reporting for auto-scaling")
        
        while True:
            try:
                # 1. 获取队列大小
                queue_size = await self._redis_queue_size()
                
                # 2. 计算平均处理时间 (秒)
                if not self.processing_times:
                    # 没有处理记录时使用默认值
                    avg_time = 60.0  # 使用较保守的默认值，假设视频处理平均需要60秒
                    self.logger.info(f"没有处理时间记录，使用默认值 {avg_time} 秒")
                else:
                    avg_time = sum(self.processing_times) / len(self.processing_times)
                    self.logger.info(f"基于 {len(self.processing_times)} 条记录计算的平均处理时间: {avg_time:.2f} 秒")
                
                # 3. 获取当前上海时间
                shanghai_tz = pytz.timezone('Asia/Shanghai')
                now = datetime.now(shanghai_tz)
                
                # 4. 计算距离今天午夜的秒数
                midnight = now.replace(hour=23, minute=0, second=0) # 23:00:00　隔一个小时，让系统有足够时间处理并且能让弹性更好的发挥
                time_until_midnight = (midnight - now).total_seconds()
                
                # 5. 获取活跃实例数量
                instance_count = await self._get_active_instance_count()
                self.logger.info(f"当前活跃实例数: {instance_count}")
                
                # 6. 获取每个实例的处理器数量
                num_processors_per_instance = self.config.get('VLM').get('num_actors', 3)
                
                # 7. 计算总处理器数量 (实例数 × 每个实例的处理器数)
                total_processors = instance_count * num_processors_per_instance
                self.logger.info(f"总处理器数量: {total_processors} (每实例 {num_processors_per_instance} × {instance_count} 实例)")
                
                # 8. 预估处理完队列中所有任务所需的时间，考虑所有实例的并行处理能力
                estimated_completion = (queue_size * avg_time) / total_processors if total_processors > 0 else 0
                
                # 9. 计算容量比率 (比值小于1表示处理能力不足)
                capacity_ratio = time_until_midnight / max(estimated_completion, 1) if estimated_completion > 0 else 10.0
                
                # 10. 确定扩缩容决策值: 容量比率小于0.9时为3(扩容)，大于1.1时为1(缩容)，介于两者之间为2(保持不变)
                if capacity_ratio < 0.9:
                    scaling_decision = 3  # 扩容
                elif capacity_ratio > 1.1:
                    scaling_decision = 1  # 缩容
                else:
                    scaling_decision = 2  # 保持不变
                
                # 准备上报的指标
                metrics_data = [
                    {"name": "queue_size", "value": queue_size},
                    {"name": "avg_processing_time", "value": avg_time},
                    {"name": "time_until_midnight", "value": time_until_midnight},
                    {"name": "estimated_completion", "value": estimated_completion},
                    {"name": "capacity_ratio", "value": capacity_ratio},
                    {"name": "active_instances", "value": instance_count},
                    {"name": "total_processors", "value": total_processors},
                    {"name": "scaling_decision", "value": scaling_decision}  # 新增扩缩容决策指标
                ]
                
                # 记录日志
                scaling_text = "1=缩容" if scaling_decision < 2 else ("2=保持不变" if scaling_decision == 2 else "3=扩容")
                self.logger.info(f"扩缩容指标 - 队列大小: {queue_size}, 平均处理时间: {avg_time:.2f}秒, "
                           f"预估完成时间: {estimated_completion:.2f}秒, 距离午夜: {time_until_midnight:.2f}秒, "
                           f"容量比率: {capacity_ratio:.2f}, 活跃实例数: {instance_count}, "
                           f"扩缩容决策: {scaling_decision} ({scaling_text})")
                
                # 上报指标
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            self.config.get('PAI_EAS', {}).get('report_url'),
                            json=metrics_data,
                            timeout=5
                        ) as response:
                            if response.status != 200:
                                self.logger.error(f"上报指标失败: {await response.text()}")
                except Exception as e:
                    self.logger.error(f"调用指标API出错: {str(e)}")
                
                await asyncio.sleep(self.config.get('PAI_EAS', {}).get('report_interval', 30))  # 每30秒上报一次
            
            except Exception as e:
                self.logger.error(f"上报伸缩指标出错: {str(e)}")
                self.logger.error(traceback.format_exc())
                await asyncio.sleep(10)


    async def _check_actor_timeouts(self):
        """检查Actor超时"""
        await asyncio.sleep(30)  # 等待系统启动稳定
        self.logger.info("Starting actor timeout monitoring")
        
        while True:
            try:
                conn_actor = await self.get_redis_actor()
                
                # 获取当前实例列表
                active_instances = await conn_actor.redis_smembers.remote(self.redis_instances_key)
                
                # 检查每个实例的最后心跳时间
                for instance_id in active_instances:
                    # 检查实例状态
                    instance_status_key = f"video_unstanding:instance:{instance_id}:status"
                    status_json = await conn_actor.redis_get.remote(instance_status_key)
                    
                    if not status_json:
                        # 没有状态信息的实例被视为僵尸实例
                        self.logger.warning(f"实例 {instance_id} 没有状态信息，将被标记为僵尸实例")
                        continue
                    
                    try:
                        status = json.loads(status_json)
                        last_heartbeat = status.get('last_heartbeat', 0)
                        
                        # 如果最后心跳时间超过超时时间，将实例标记为僵尸实例
                        if (time.time() - last_heartbeat) > self.config.get('PAI_EAS', {}).get('zombie_instance_timeout', 600):
                            self.logger.warning(f"实例 {instance_id} 的最后心跳时间是 {datetime.fromtimestamp(last_heartbeat).strftime('%Y-%m-%d %H:%M:%S')}，"
                                        f"超过 {self.config.get('PAI_EAS', {}).get('zombie_instance_timeout', 600)} 秒，将被标记为僵尸实例")
                            # 从活跃实例集合中移除
                            await conn_actor.redis_srem.remote(self.redis_instances_key, instance_id)
                            
                            # 删除实例状态信息 - 修正：使用redis_expire设置1秒过期时间，而不是使用不存在的redis_del方法
                            instance_status_key = f"video_unstanding:instance:{instance_id}:status"
                            await conn_actor.redis_expire.remote(instance_status_key, 1)
                            
                            self.logger.info(f"已清理僵尸实例: {instance_id}")
                    except Exception as e:
                        self.logger.error(f"解析实例 {instance_id} 的状态信息时出错: {e}")
                
                await self.push_redis_actor(conn_actor)
                
                # 使用配置中的检查间隔，默认60秒
                await asyncio.sleep(self.config.get('PAI_EAS').get('check_interval', 60))
            
            except Exception as e:
                self.logger.error(f"检查Actor超时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
                await asyncio.sleep(10)

    @app.post("/search", response_model=SearchResponse)
    async def search_videos(self, request: SearchRequest):
        """向量检索API端点"""
        try:
            self.logger.info(f"Received search request: ESN={request.esn}, query={request.query_text}, types=summary（其他功能暂未开放）")
            
            # 获取向量检索Actor
            search_actor = await self.get_search_actor()
            
            try:
                # 执行组合搜索
                search_results = await search_actor.combined_search.remote(
                    esn=request.esn,
                    query_text=request.query_text,
                    limit=request.limit,
                    drop_ratio=0.2,
                    search_types=["summary"]#默认只使用summary进行搜索，先不对后端暴露其他能力
                )
                
                self.logger.info(f"Search completed for ESN {request.esn}, total matches: {search_results.get('total_matches', 0)}")
                
                # 格式化响应
                response = SearchResponse(results=[search_results])
                return response
                
            finally:
                # 归还Actor到池中
                self.push_search_actor(search_actor)
                
        except Exception as e:
            self.logger.error(f"Search request failed: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            # 返回空结果而不是抛出异常
            return SearchResponse(results=[])

    @app.get("/search/stats")
    async def get_search_stats(self):
        """获取搜索服务统计信息"""
        try:
            # 获取向量检索Actor
            search_actor = await self.get_search_actor()
            
            try:
                # 获取集合统计信息
                stats = await search_actor.get_collection_stats.remote()
                
                # 添加Actor池状态信息
                pool_stats = {
                    "video_actor_pool_size": self.video_actor_pool.get_total_count() if hasattr(self.video_actor_pool, 'get_total_count') else "unknown",
                    "video_actor_pool_busy": self.video_actor_pool.get_busy_count() if hasattr(self.video_actor_pool, 'get_busy_count') else "unknown",
                    "search_actor_pool_size": self.search_actor_pool.get_total_count() if hasattr(self.search_actor_pool, 'get_total_count') else "unknown",
                    "search_actor_pool_busy": self.search_actor_pool.get_busy_count() if hasattr(self.search_actor_pool, 'get_busy_count') else "unknown"
                }
                
                stats.update(pool_stats)
                return stats
                
            finally:
                # 归还Actor到池中
                self.push_search_actor(search_actor)
                
        except Exception as e:
            self.logger.error(f"Get search stats failed: {str(e)}")
            return {"error": str(e)}

    @app.post("/produce", response_model=ProduceMessageResponse)
    async def produce_kafka_message(self, request: ProduceMessageRequest):
        """生产Kafka消息用于测试"""
        try:
            self.logger.info(f"Received produce message request: task_id={request.task_id}, esn={request.esn}, file_url={request.file_url}")
            
            # 获取Kafka Actor
            kafka_actor = await self.get_kafka_actor()
            
            try:
                # 构造消息
                import time
                start_time = request.start_time if request.start_time else int(time.time() * 1000000)
                
                message = {
                    "taskId": request.task_id,
                    "fileUrl": request.file_url,
                    "esn": request.esn,
                    "startTime": start_time,
                    "retry_count": request.retry_count
                }
                
                # 获取Kafka主题配置
                kafka_config = self.config.get("Kafka", {})
                topic = kafka_config.get("topic", "video_unstanding_topic")
                
                # 发送消息到Kafka
                success = await kafka_actor.send_message.remote(topic, message)
                
                if success:
                    self.logger.info(f"Successfully produced message to topic {topic}: task_id={request.task_id}")
                    return ProduceMessageResponse(
                        success=True,
                        message=f"消息成功发送到Kafka主题: {topic}",
                        task_id=request.task_id,
                        topic=topic
                    )
                else:
                    self.logger.error(f"Failed to produce message to topic {topic}: task_id={request.task_id}")
                    return ProduceMessageResponse(
                        success=False,
                        message="发送消息到Kafka失败",
                        task_id=request.task_id,
                        topic=topic
                    )
                
            finally:
                # 归还Kafka Actor到池中
                await self.push_kafka_actor(kafka_actor)
                
        except Exception as e:
            self.logger.error(f"Produce kafka message failed: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return ProduceMessageResponse(
                success=False,
                message=f"生产消息出错: {str(e)}",
                task_id=request.task_id if hasattr(request, 'task_id') else 0,
                topic="unknown"
            )

    @app.post("/reset-collections", response_model=ResetCollectionResponse)
    async def reset_collections(self):
        """重置Milvus集合（删除并重新创建）"""
        try:
            self.logger.info("Received request to reset collections")
            
            # 获取视频处理Actor
            # 从video_actor_pool中获取一个Actor来执行重置操作
            video_actor = None
            for _ in range(5):  # 最多尝试5次获取Actor
                try:
                    video_actor = self.video_actor_pool.get_actor()
                    if video_actor:
                        break
                    await asyncio.sleep(1)
                except Exception as e:
                    self.logger.warning(f"尝试获取video actor失败: {e}")
                    await asyncio.sleep(1)
            
            if not video_actor:
                return ResetCollectionResponse(
                    success=False,
                    message="无法获取视频处理Actor",
                    video_collection="unknown",
                    frame_collection="unknown"
                )
            
            try:
                # 获取集合名称
                video_collection_name = await video_actor.video_collection_name.remote()
                frame_collection_name = await video_actor.frame_collection_name.remote()
                
                self.logger.info(f"开始重置集合: video={video_collection_name}, frame={frame_collection_name}")
                
                # 重置视频集合
                await video_actor.reset_collection.remote(video_collection_name, True)
                self.logger.info(f"视频集合 {video_collection_name} 重置完成")
                
                # 重置帧集合  
                await video_actor.reset_collection.remote(frame_collection_name, False)
                self.logger.info(f"帧集合 {frame_collection_name} 重置完成")
                
                return ResetCollectionResponse(
                    success=True,
                    message="集合重置成功，现在包含event_id字段",
                    video_collection=video_collection_name,
                    frame_collection=frame_collection_name
                )
                
            finally:
                # 归还Actor到池中
                if video_actor:
                    self.video_actor_pool.return_actor(video_actor)
                
        except Exception as e:
            self.logger.error(f"Reset collections failed: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return ResetCollectionResponse(
                success=False,
                message=f"重置集合出错: {str(e)}",
                video_collection="unknown",
                frame_collection="unknown"
            )

    @app.get("/oss/stats", response_model=OSSStatsResponse)
    async def get_oss_stats(self):
        """获取OSS存储统计信息"""
        try:
            if not self.oss_manager:
                return OSSStatsResponse(
                    video_analysis_count=0,
                    search_logs_count=0,
                    training_datasets_count=0,
                    last_updated="OSS not configured"
                )
            
            stats = self.oss_manager.get_storage_stats()
            return OSSStatsResponse(**stats)
            
        except Exception as e:
            self.logger.error(f"Get OSS stats failed: {str(e)}")
            return OSSStatsResponse(
                video_analysis_count=0,
                search_logs_count=0,
                training_datasets_count=0,
                last_updated=f"Error: {str(e)}"
            )

    @app.post("/oss/create-training-dataset", response_model=CreateTrainingDatasetResponse)
    async def create_training_dataset(self, request: CreateTrainingDatasetRequest):
        """创建训练数据集"""
        try:
            if not self.oss_manager:
                return CreateTrainingDatasetResponse(
                    success=False,
                    message="OSS未配置，无法创建训练数据集",
                    dataset_path="",
                    data_count=0
                )
            
            self.logger.info(f"Creating training dataset: {request.dataset_name} v{request.version}")
            
            # 这里可以实现从Milvus或OSS中收集数据的逻辑
            # 目前先创建一个示例数据集
            training_data = []
            
            # 示例：从搜索日志中创建训练数据
            # 实际实现时可以根据日期范围、ESN过滤等条件收集数据
            sample_data = {
                "query": "快递员送包裹",
                "esn": "ESN001",
                "response": {
                    "title_matches": [{"video_id": 123, "score": 0.95}],
                    "summary_matches": [{"video_id": 124, "score": 0.88}]
                },
                "feedback": "positive",  # 可以后续添加用户反馈
                "timestamp": datetime.now().isoformat()
            }
            training_data.append(sample_data)
            
            # 准备元数据
            metadata = {
                "dataset_name": request.dataset_name,
                "version": request.version,
                "description": request.description,
                "creation_params": {
                    "start_date": request.start_date,
                    "end_date": request.end_date,
                    "esn_filter": request.esn_filter,
                    "min_score": request.min_score
                },
                "created_by": "video_understanding_service",
                "format": "jsonl"
            }
            
            # 创建训练数据集
            dataset_path = self.oss_manager.create_training_dataset(
                dataset_name=request.dataset_name,
                version=request.version,
                data=training_data,
                metadata=metadata
            )
            
            return CreateTrainingDatasetResponse(
                success=True,
                message=f"训练数据集创建成功: {request.dataset_name} v{request.version}",
                dataset_path=dataset_path,
                data_count=len(training_data)
            )
            
        except Exception as e:
            self.logger.error(f"Create training dataset failed: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return CreateTrainingDatasetResponse(
                success=False,
                message=f"创建训练数据集失败: {str(e)}",
                dataset_path="",
                data_count=0
            )

    @app.post("/oss/backup-daily")
    async def backup_daily_data(self, date: Optional[str] = None):
        """每日数据备份"""
        try:
            if not self.oss_manager:
                return {"success": False, "message": "OSS未配置"}
            
            # 解析日期参数
            backup_date = None
            if date:
                from datetime import datetime
                backup_date = datetime.strptime(date, "%Y-%m-%d")
            
            backup_path = self.oss_manager.backup_daily_data(backup_date)
            
            return {
                "success": True,
                "message": f"每日数据备份完成",
                "backup_path": backup_path,
                "backup_date": date or datetime.now().strftime("%Y-%m-%d")
            }
            
        except Exception as e:
            self.logger.error(f"Daily backup failed: {str(e)}")
            return {"success": False, "message": f"备份失败: {str(e)}"}
    
    
def create_app(config_path: str = None):
    """启动服务
    
    Args:
        config_path: 配置文件路径,如果不指定则根据DEPLOY_MODE加载对应配置
    """
    deployment = VideoUnstandingService.bind()
    return deployment

app = create_app()
    
    
