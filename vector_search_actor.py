import os
import ray
import asyncio
import aiohttp
from openai import AsyncOpenAI
from pymilvus import DataType, MilvusClient
from typing import Optional, List
import yaml
import json
import time
import logging

from datetime import datetime
from oss_manager import OSSDataManager  # 添加OSS管理器
from llm_summary_service import LLMSummaryService  # 添加LLM摘要服务
from utils import convert_timestamp_to_unix

@ray.remote(max_task_retries=2)  # 向量检索通常不需要太多CPU资源
class VectorSearchActor:
    """专门用于向量检索的Actor类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.load_config()
        self.milvus_client = MilvusClient(uri=self.uri, token=self.token)
        self.text_embedding_client = AsyncOpenAI(api_key=self.text_embedding_api_key, base_url=self.text_embedding_base_url)
        
        # 连接到数据库
        self.milvus_client.use_database(db_name=self.db_name)
        self.logger.info(f"VectorSearchActor connected to database: {self.db_name}")
        
        # 设置当前时区为上海
        os.environ['TZ'] = 'Asia/Shanghai'
        time.tzset()
        
        # 初始化OSS数据管理器
        try:
            self.oss_manager = OSSDataManager()
            self.logger.info("OSS Manager initialized successfully in VectorSearchActor")
        except Exception as e:
            self.logger.warning(f"OSS Manager initialization failed in VectorSearchActor: {e}. Search logging will be disabled.")
            self.oss_manager = None
        
        # 初始化LLM摘要服务
        try:
            self.llm_summary_service = LLMSummaryService()
            self.logger.info("LLM Summary Service initialized successfully in VectorSearchActor")
        except Exception as e:
            self.logger.warning(f"LLM Summary Service initialization failed in VectorSearchActor: {e}. Summary generation will be disabled.")
            self.llm_summary_service = None

    def load_config(self, path=None):
        """加载配置"""
        if not path:
            # 根据部署模式加载不同的配置文件
            deploy_mode = os.getenv("DEPLOY_MODE", "dev")
            path = f"config_{deploy_mode}.yaml"
        self.logger.info(f"VectorSearchActor Loading config from: {path}")
        
        with open(path, "r") as f:
            self.config = yaml.safe_load(f)
        
        milvus_config = self.config.get("Milvus", {})
        self.db_name = milvus_config.get("db_name")
        self.video_collection_name = milvus_config.get("video_collection_name")
        self.frame_collection_name = milvus_config.get("frame_collection_name")
        self.vector_dim = milvus_config.get("vector_dim")
        self.uri = milvus_config.get("uri")
        self.token = milvus_config.get("token")
        
        # 加载CLIP服务配置
        clip_service_config = self.config.get("chinese_clip_service", {})
        self.clip_service_url = clip_service_config.get("url")
        self.clip_service_max_retries = clip_service_config.get("max_retries", 3)
        self.clip_service_retry_delay = clip_service_config.get("retry_delay", 1.0)
        self.clip_service_timeout = clip_service_config.get("timeout", 30)

        # 加载文本嵌入服务配置
        text_embedding_config = self.config.get("text_embedding_service", {})
        self.text_embedding_api_key = text_embedding_config.get("api_key")
        self.text_embedding_base_url = text_embedding_config.get("base_url")
        self.text_embedding_model = text_embedding_config.get("embedding_model")
        # 使用文本嵌入服务的向量维度，而不是Milvus配置中的向量维度
        self.text_embedding_dim = text_embedding_config.get("embedding_dim", 1536)

        self.logger.info(f"VectorSearchActor config loaded - DB: {self.db_name}, "
                        f"Video collection: {self.video_collection_name}, "
                        f"Frame collection: {self.frame_collection_name}, "
                        f"Text embedding dim: {self.text_embedding_dim}, "
                        f"Frame embedding dim: {self.vector_dim}")


    async def _get_embeddings_from_api(self, text: str) -> Optional[list[float]]:
        """通过API获取文本的嵌入向量"""
        if not self.clip_service_url:
            self.logger.error("Chinese CLIP service URL is not configured.")
            return None

        for attempt in range(self.clip_service_max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.clip_service_timeout)) as session:
                    payload = {"texts": [text]}  # API需要一个文本列表
                    async with session.post(self.clip_service_url, json=payload) as response:
                        response.raise_for_status()
                        result = await response.json()
                        # API在text_embeddings字段中返回嵌入列表
                        if "text_embeddings" in result and result["text_embeddings"]:
                            # 我们只发送了一个文本，所以我们从列表中获取第一个嵌入
                            return result["text_embeddings"][0]
                        else:
                            self.logger.error(f"Invalid response from embedding service: {result}")
                            return None
            except aiohttp.ClientError as e:
                self.logger.error(f"Error calling embedding service (attempt {attempt + 1}/{self.clip_service_max_retries}): {e}")
                if attempt < self.clip_service_max_retries - 1:
                    await asyncio.sleep(self.clip_service_retry_delay)
                else:
                    self.logger.error("Max retries reached for embedding service.")
                    return None
            except Exception as e:
                self.logger.error(f"An unexpected error occurred while getting embeddings: {e}")
                return None
        return None

    async def _get_text_embeddings_async(self, text: str) -> Optional[list[float]]:
        """通过文本嵌入服务获取文本的嵌入向量"""
        try:
            response = await self.text_embedding_client.embeddings.create(
                input=[text],
                model=self.text_embedding_model,
                dimensions=self.text_embedding_dim  # 使用文本嵌入服务的维度
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while getting text embeddings: {e}")
            return None

    async def _ensure_collection_loaded(self, collection_name: str):
        """确保集合已加载到内存中"""
        try:
            # 检查集合是否存在
            collections = self.milvus_client.list_collections()
            if collection_name not in collections:
                self.logger.error(f"Collection {collection_name} does not exist")
                return
                
            # 检查集合加载状态
            load_state = self.milvus_client.get_load_state(collection_name)
            self.logger.debug(f"Collection {collection_name} load state: {load_state}")
            
            # 根据文档，load_state 返回的格式是 {'state': '<LoadState: Loaded>'}
            # 检查状态是否为已加载
            state_str = str(load_state.get('state', ''))
            if 'Loaded' not in state_str:
                self.logger.info(f"Loading collection {collection_name}...")
                self.milvus_client.load_collection(collection_name=collection_name)
                
                # 等待加载完成并验证
                import asyncio
                max_wait = 30  # 最多等待30秒
                wait_time = 0
                while wait_time < max_wait:
                    await asyncio.sleep(1)
                    wait_time += 1
                    new_state = self.milvus_client.get_load_state(collection_name)
                    new_state_str = str(new_state.get('state', ''))
                    if 'Loaded' in new_state_str:
                        self.logger.info(f"Collection {collection_name} loaded successfully")
                        return
                        
                self.logger.error(f"Collection {collection_name} failed to load within {max_wait} seconds")
            else:
                self.logger.debug(f"Collection {collection_name} is already loaded")
                
        except Exception as e:
            self.logger.error(f"Failed to ensure collection {collection_name} is loaded: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise

    async def search_video_title_collection(
        self,
        esn: str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """在视频标题集合中搜索"""
        try:
            self.logger.info(f"Searching video titles for ESN: {esn}, query: {query_text}")
            
            # 确保集合已加载
            await self._ensure_collection_loaded(self.video_collection_name)
            
            # 使用文本嵌入服务生成查询向量
            query_vector = await self._get_text_embeddings_async(query_text)
            if not query_vector:
                self.logger.error("Failed to get query vector for video title search.")
                return []

            self.logger.debug(f"Generated query vector with dimension: {len(query_vector)}")
            
            # 构建过滤条件
            filter_expr = f'esn == "{esn}"'
            
            # 执行向量搜索
            search_results = self.milvus_client.search(
                collection_name=self.video_collection_name,
                data=[query_vector],
                anns_field="title_embedding",
                search_params={"metric_type": "COSINE", "params": {"nprobe": 10}},
                limit=limit,
                filter=filter_expr,
                output_fields=["video_id", "event_id", "video_url", "esn", "title", "timestamp", 
                             "num_persons", "persons", "couriers", "food_deliverers", "packages", "pets",
                             "summary", "security_risk", "recommendation", "reasoning_content"]
            )
            
            # 处理搜索结果
            results = []
            if search_results and len(search_results) > 0:
                output_fields_list = ["video_id", "event_id", "video_url", "esn", "title", "timestamp", 
                                    "num_persons", "persons", "couriers", "food_deliverers", "packages", "pets",
                                    "summary", "security_risk", "recommendation", "reasoning_content"]
                for hit in search_results[0]:
                    if hit.distance >= drop_ratio:  # 只保留相似度高于阈值的结果
                        entity = {field: hit.entity.get(field) for field in output_fields_list}
                        # 解析JSON字符串字段
                        try:
                            persons = json.loads(entity["persons"]) if entity["persons"] else []
                        except:
                            persons = []
                        
                        result = {
                            "score": hit.distance,
                            "video_id": str(hit.id),
                            "video_url": entity["video_url"],
                            "esn": entity["esn"],
                            "event_id": entity["event_id"],
                            "title": entity["title"],
                            "summary": entity["summary"],
                            "timestamp": convert_timestamp_to_unix(entity["timestamp"]),
                            "num_persons": entity["num_persons"],
                            "persons": persons,
                            "security_risk": entity["security_risk"],
                            "recommendation": entity["recommendation"]
                        }
                        results.append(result)
            
            self.logger.info(f"Found {len(results)} video title matches for ESN {esn}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching video title collection: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []

    async def search_video_summary_collection(
        self,
        esn: str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """在视频摘要集合中搜索"""
        try:
            self.logger.info(f"Searching video summaries for ESN: {esn}, query: {query_text}")
            
            # 确保集合已加载
            await self._ensure_collection_loaded(self.video_collection_name)
            
            # 使用文本嵌入服务生成查询向量
            query_vector = await self._get_text_embeddings_async(query_text)
            if not query_vector:
                self.logger.error("Failed to get query vector for video summary search.")
                return []

            self.logger.debug(f"Generated query vector with dimension: {len(query_vector)}")
            
            # 构建过滤条件
            filter_expr = f'esn == "{esn}"'
            
            # 执行向量搜索
            search_results = self.milvus_client.search(
                collection_name=self.video_collection_name,
                data=[query_vector],
                anns_field="summary_embedding",
                search_params={"metric_type": "COSINE", "params": {"nprobe": 10}},
                limit=limit,
                filter=filter_expr,
                output_fields=["video_id", "event_id", "video_url", "esn", "title", "timestamp", 
                             "num_persons", "persons", "couriers", "food_deliverers", "packages", "pets",
                             "summary", "security_risk", "recommendation", "reasoning_content"]
            )
            
            # 处理搜索结果
            results = []
            if search_results and len(search_results) > 0:
                output_fields_list = ["video_id", "event_id", "video_url", "esn", "title", "timestamp", 
                                    "num_persons", "persons", "couriers", "food_deliverers", "packages", "pets",
                                    "summary", "security_risk", "recommendation", "reasoning_content"]
                for hit in search_results[0]:
                    if hit.distance >= drop_ratio:  # 只保留相似度高于阈值的结果
                        entity = {field: hit.entity.get(field) for field in output_fields_list}
                        # 解析JSON字符串字段
                        try:
                            persons = json.loads(entity["persons"]) if entity["persons"] else []
                        except:
                            persons = []
                        
                        result = {
                            "score": hit.distance,
                            "video_id": str(hit.id),
                            "video_url": entity["video_url"],
                            "esn": entity["esn"],
                            "event_id": entity["event_id"],
                            "title": entity["title"],
                            "summary": entity["summary"],
                            "timestamp": convert_timestamp_to_unix(entity["timestamp"]),
                            "num_persons": entity["num_persons"],
                            "persons": persons,
                            "security_risk": entity["security_risk"],
                            "recommendation": entity["recommendation"]
                        }
                        results.append(result)
            
            self.logger.info(f"Found {len(results)} video summary matches for ESN {esn}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching video summary collection: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []

    async def search_frame_collection(
        self,
        esn: str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
    ) -> list[dict]:
        """在帧集合中搜索（目前该功能产品不使用，暂时禁用）"""
        try:
            self.logger.info(f"Searching frames for ESN: {esn}, query: {query_text}")
            
            #确保集合已加载
            await self._ensure_collection_loaded(self.frame_collection_name)
            
            # 使用Chinese-CLIP API生成查询向量（用于帧搜索）
            query_vector = await self._get_embeddings_from_api(query_text)
            if not query_vector:
                self.logger.error("Failed to get Chinese-CLIP query vector for frame search.")
                return []

            self.logger.debug(f"Generated Chinese-CLIP query vector with dimension: {len(query_vector)}")
            
            # 构建过滤条件
            filter_expr = f'esn == "{esn}"'
            
            # 执行向量搜索
            search_results = self.milvus_client.search(
                collection_name=self.frame_collection_name,
                data=[query_vector],
                anns_field="embedding",
                search_params={"metric_type": "COSINE", "params": {"nprobe": 10}},
                limit=limit,
                filter=filter_expr,
                output_fields=["frame_id", "event_id", "esn", "source_url", "time", "timestamp"]
            )
            
            # 处理搜索结果
            results = []
            if search_results and len(search_results) > 0:
                output_fields_list = ["frame_id", "event_id", "esn", "source_url", "time", "timestamp"]
                for hit in search_results[0]:
                    if hit.distance >= drop_ratio:  # 只保留相似度高于阈值的结果
                        entity = {field: hit.entity.get(field) for field in output_fields_list}
                        result = {
                            "frame_id": hit.id,
                            "score": hit.distance,
                            "esn": entity["esn"],
                            "event_id": entity["event_id"],
                            "url": entity["source_url"],
                            "timestamp": entity["timestamp"],  # 帧的timestamp是视频内时间点(FLOAT)，不需要转换
                            "summary": ""  # 帧搜索结果没有summary字段
                        }
                        results.append(result)
            
            self.logger.info(f"Found {len(results)} frame matches for ESN {esn}")
            return results
            
        except Exception as e:
            self.logger.error(f"Error searching frame collection: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return []

    async def combined_search(
        self,
        esn: str,
        query_text: str,
        limit: int = 5,
        drop_ratio: float = 0.2,
        search_types: Optional[List[str]] = None,
        session_id: str = None
    ) -> dict:
        """
        组合搜索：根据指定的类型(search_types)搜索视频标题、摘要和帧。
        默认搜索所有类型。
        """
        if search_types is None:
            search_types = ["title", "summary", "frame"]

        # 记录搜索开始时间
        search_start_time = time.time()
        
        try:
            self.logger.info(f"Performing combined search for ESN: {esn}, query: {query_text}, types: {search_types}")
            
            tasks = []
            task_names = []

            if "title" in search_types:
                tasks.append(self.search_video_title_collection(esn, query_text, limit, drop_ratio))
                task_names.append("title")
            
            if "summary" in search_types:
                tasks.append(self.search_video_summary_collection(esn, query_text, limit, drop_ratio))
                task_names.append("summary")

            if "frame" in search_types:
                tasks.append(self.search_frame_collection(esn, query_text, limit, drop_ratio))
                task_names.append("frame")

            if not tasks:
                self.logger.warning("No valid search types provided. Returning empty results.")
                return {"title_matches": [], "summary_matches": [], "frame_matches": [], "total_matches": 0}

            # 并行执行选定的搜索任务
            results = await asyncio.gather(*tasks)
            
            # 初始化结果字典
            combined_results = {
                "title_matches": {
                    "total": 0,
                    "total_summary": "",
                    "data": []
                },
                "summary_matches": {
                    "total": 0,
                    "total_summary": "",
                    "data": []
                },
                "frame_matches": {
                    "total": 0,
                    "total_summary": "",
                    "data": []
                },
            }
            
            total_matches = 0
            # 根据任务名称将结果填充到字典中
            for i, name in enumerate(task_names):
                result_list = results[i]
                combined_results[f"{name}_matches"] = {
                    "total": len(result_list),
                    "total_summary": "",  # 稍后生成
                    "data": result_list
                }
                total_matches += len(result_list)
            
            combined_results["total_matches"] = total_matches
            
            # 计算搜索耗时
            search_duration = time.time() - search_start_time
            
            # 为每个搜索类型生成独立的智能总结
            if self.llm_summary_service:
                # 并行生成各类型的总结
                summary_tasks = []
                summary_task_names = []
                
                for search_type in search_types:
                    match_key = f"{search_type}_matches"
                    if match_key in combined_results:
                        matches_data = combined_results[match_key]["data"]
                        if matches_data:  # 只为有数据的类型生成总结
                            summary_tasks.append(
                                self.llm_summary_service.summarize_by_type(
                                    matches=matches_data,
                                    match_type=search_type,
                                    query_text=query_text,
                                    esn=esn
                                )
                            )
                            summary_task_names.append(search_type)
                
                # 并行执行总结任务
                if summary_tasks:
                    try:
                        summary_results = await asyncio.gather(*summary_tasks, return_exceptions=True)
                        
                        # 将总结结果分配到对应的matches中
                        for i, task_name in enumerate(summary_task_names):
                            match_key = f"{task_name}_matches"
                            if i < len(summary_results) and not isinstance(summary_results[i], Exception):
                                combined_results[match_key]["total_summary"] = summary_results[i]
                                self.logger.info(f"Generated {task_name} summary successfully")
                            else:
                                # 生成降级总结
                                matches_count = combined_results[match_key]["total"]
                                combined_results[match_key]["total_summary"] = f"发现{matches_count}个相关事件。"
                                self.logger.error(f"Failed to generate {task_name} summary, using fallback")
                                
                    except Exception as e:
                        self.logger.error(f"Failed to generate type-specific summaries: {str(e)}")
                        # 为所有类型生成降级总结
                        for search_type in search_types:
                            match_key = f"{search_type}_matches"
                            if match_key in combined_results:
                                matches_count = combined_results[match_key]["total"]
                                if matches_count > 0:
                                    combined_results[match_key]["total_summary"] = f"发现{matches_count}个相关事件。"
                                else:
                                    combined_results[match_key]["total_summary"] = f"未发现相关事件。"
            
            # 为没有搜索的类型也设置空的总结
            for search_type in ["title", "summary", "frame"]:
                match_key = f"{search_type}_matches"
                if match_key not in combined_results or search_type not in search_types:
                    if match_key not in combined_results:
                        combined_results[match_key] = {
                            "total": 0,
                            "total_summary": f"未搜索{self._get_type_display_name(search_type)}。",
                            "data": []
                        }
                    elif not combined_results[match_key]["total_summary"]:
                        combined_results[match_key]["total_summary"] = f"未发现{self._get_type_display_name(search_type)}相关事件。"
            
            self.logger.info(f"Combined search completed. Total matches: {combined_results['total_matches']}, Duration: {search_duration:.3f}s")
            
            # 记录搜索日志到OSS（如果OSS管理器可用）
            if self.oss_manager:
                try:
                    # 准备请求数据
                    request_data = {
                        "esn": esn,
                        "query_text": query_text,
                        "limit": limit,
                        "drop_ratio": drop_ratio,
                        "search_types": search_types
                    }
                    
                    # 准备响应数据（包含完整的搜索结果和各类型的智能总结）
                    response_data = {
                        "total_matches": combined_results["total_matches"],
                        "title_matches_count": combined_results["title_matches"]["total"],
                        "title_matches_summary": combined_results["title_matches"]["total_summary"],
                        "summary_matches_count": combined_results["summary_matches"]["total"],
                        "summary_matches_summary": combined_results["summary_matches"]["total_summary"],
                        "frame_matches_count": combined_results["frame_matches"]["total"],
                        "frame_matches_summary": combined_results["frame_matches"]["total_summary"],
                        # 保存完整的搜索结果（前5个）用于分析
                        "title_matches": [
                            {
                                "video_id": r["video_id"], 
                                "score": r["score"], 
                                "event_id": r["event_id"],
                                "video_url": r["video_url"],
                                "summary": r.get("summary", "")[:200]  # 保存summary的前200字符
                            } for r in combined_results["title_matches"]["data"][:5]
                        ],
                        "summary_matches": [
                            {
                                "video_id": r["video_id"], 
                                "score": r["score"], 
                                "event_id": r["event_id"],
                                "video_url": r["video_url"],
                                "summary": r.get("summary", "")[:200]
                            } for r in combined_results["summary_matches"]["data"][:5]
                        ],
                        "frame_matches": [
                            {
                                "frame_id": r["frame_id"], 
                                "score": r["score"], 
                                "event_id": r["event_id"],
                                "source_url": r.get("url", ""),
                                "timestamp": r.get("timestamp", 0)
                            } for r in combined_results["frame_matches"]["data"][:5]
                        ]
                    }
                    
                    # 准备性能数据
                    performance_data = {
                        "search_duration_seconds": search_duration,
                        "search_types_count": len(search_types),
                        "parallel_tasks": len(tasks),
                        "timestamp": time.time()
                    }
                    
                    # 异步保存搜索日志（不阻塞主流程）
                    asyncio.create_task(self._save_search_log_async(
                        request_data, response_data, performance_data, session_id
                    ))
                    
                except Exception as oss_e:
                    self.logger.error(f"保存搜索日志失败: {str(oss_e)}")
                    # 搜索日志保存失败不影响搜索结果返回
            
            return combined_results
            
        except Exception as e:
            self.logger.error(f"Error in combined search: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {"title_matches": [], "summary_matches": [], "frame_matches": [], "total_matches": 0}
    
    async def _save_search_log_async(self, request_data: dict, response_data: dict, 
                                   performance_data: dict, session_id: str = None):
        """异步保存搜索日志"""
        try:
            self.oss_manager.save_search_log(
                request_data=request_data,
                response_data=response_data,
                performance_data=performance_data,
                session_id=session_id
            )
        except Exception as e:
            self.logger.error(f"异步保存搜索日志失败: {str(e)}")

    def _get_type_display_name(self, search_type: str) -> str:
        """获取搜索类型的显示名称"""
        type_names = {
            "title": "标题匹配",
            "summary": "内容匹配",
            "frame": "场景匹配"
        }
        return type_names.get(search_type, "未知类型")

    async def get_collection_stats(self) -> dict:
        """获取集合统计信息"""
        try:
            stats = {}
            
            # 获取视频集合统计 - 使用 get_collection_stats 方法
            try:
                video_stats = self.milvus_client.get_collection_stats(
                    collection_name=self.video_collection_name
                )
                # 从统计信息中提取行数
                stats["video_count"] = video_stats.get("row_count", 0)
            except Exception as e:
                self.logger.warning(f"无法获取视频集合统计: {e}")
                stats["video_count"] = 0
            
            # 获取帧集合统计
            try:
                frame_stats = self.milvus_client.get_collection_stats(
                    collection_name=self.frame_collection_name
                )
                stats["frame_count"] = frame_stats.get("row_count", 0)
            except Exception as e:
                self.logger.warning(f"无法获取帧集合统计: {e}")
                stats["frame_count"] = 0
            
            self.logger.info(f"Collection stats: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting collection stats: {str(e)}")
            return {"video_count": 0, "frame_count": 0} 
