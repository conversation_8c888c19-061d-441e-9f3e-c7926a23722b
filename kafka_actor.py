import ray
import json
import logging
import asyncio
from typing import Optional, Dict, Any, List
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
import time
import uuid
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO)

@ray.remote#(num_cpus=1, max_task_retries=3)
class KafkaActor:
    """Kafka连接管理Actor，专门处理Kafka连接"""
    
    def __init__(self, config: Dict[str, Any], is_producer_only=False):
        """初始化Kafka Actor
        
        Args:
            config: 包含Kafka配置的字典
            is_producer_only: 是否仅作为生产者使用，默认为False
        """
        self.config = config
        self.kafka_config = config.get('kafka', {})
        self.logger = logging.getLogger("KafkaActor")
        self.is_producer_only = is_producer_only
        
        # Kafka连接实例
        self.producer = None
        self.consumer = None
        
        # 连接状态
        self.producer_connected = False
        self.consumer_connected = False
        
        # 心跳任务
        self.group_id_heartbeat_task = None
        
        # 是否正在运行
        self.running = False
        
        # 生成实例ID
        import socket
        import os
        self.instance_id = f"{socket.gethostname()}:{os.getpid()}"
        self.logger.info(f"KafkaActor 实例ID: {self.instance_id}")
        
    async def start(self):
        """启动Kafka连接"""
        try:
            self.running = True
            # 启动Kafka生产者
            producer_ok = await self._start_kafka_producer()
            
            # 如果是生产者模式，则不启动消费者
            if not self.is_producer_only:
                # 启动Kafka消费者
                consumer_ok = await self._start_kafka_consumer()
                
                # 启动消费者组ID心跳
                if consumer_ok:
                    self.group_id_heartbeat_task = asyncio.create_task(self._maintain_consumer_group_id_heartbeat())
                    
                # 返回连接状态
                return producer_ok and consumer_ok
            else:
                self.logger.info("生产者模式，不启动消费者")
                return producer_ok
        except Exception as e:
            self.logger.error(f"启动Kafka连接失败: {e}")
            return False
    
    async def stop(self):
        """停止Kafka连接"""
        self.running = False
        
        try:
            # 取消心跳任务
            if self.group_id_heartbeat_task and not self.group_id_heartbeat_task.done():
                self.group_id_heartbeat_task.cancel()
                try:
                    await self.group_id_heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # 释放消费者组ID - 手动确保释放
            if not self.is_producer_only and self.consumer_connected:
                try:
                    await self._release_consumer_group_id()
                except Exception as release_err:
                    self.logger.error(f"释放消费者组ID失败: {release_err}")
            
            # 关闭Kafka消费者
            if self.consumer:
                try:
                    await self.consumer.stop()
                    self.logger.info("Kafka consumer stopped")
                except Exception as e:
                    self.logger.error(f"Error stopping Kafka consumer: {e}")
                finally:
                    self.consumer = None
                    self.consumer_connected = False
            
            # 关闭Kafka生产者
            if self.producer:
                try:
                    await self.producer.stop()
                    self.logger.info("Kafka producer stopped")
                except Exception as e:
                    self.logger.error(f"Error stopping Kafka producer: {e}")
                finally:
                    self.producer = None
                    self.producer_connected = False
                    
            return True
        except Exception as e:
            self.logger.error(f"停止Kafka连接时发生错误: {e}")
            return False
    
    async def restart(self):
        """重启连接"""
        await self.stop()
        return await self.start()
    
    async def check_health(self) -> Dict[str, bool]:
        """检查当前链接的健康状态
        
        Returns:
            包含各种连接状态的字典
        """
        health_status = {
            'kafka_producer': False,
            'kafka_consumer': False
        }
        
        try:
            # 检查生产者状态
            if self.producer is not None and self.producer_connected:
                try:
                    # 简单的元数据请求来检查连接
                    info = await self.producer.client._coordinator_lookup(group_id="health_check")
                    health_status['kafka_producer'] = True
                except Exception as e:
                    self.logger.error(f"Kafka生产者健康检查失败: {e}")
                    # 尝试重新连接
                    if await self._start_kafka_producer():
                        health_status['kafka_producer'] = True
            
            # 如果是生产者模式，则跳过消费者检查
            if self.is_producer_only:
                health_status['kafka_consumer'] = True
                return health_status
                
            # 检查消费者状态
            if self.consumer is not None and self.consumer_connected:
                try:
                    # 检查分区分配
                    partitions = self.consumer.assignment()
                    if partitions:
                        health_status['kafka_consumer'] = True
                    else:
                        self.logger.warning("Kafka消费者没有分配分区")
                except Exception as e:
                    self.logger.error(f"Kafka消费者健康检查失败: {e}")
                    # 尝试重新连接
                    if await self._start_kafka_consumer():
                        health_status['kafka_consumer'] = True
                    else:
                        self.logger.error("无法重新连接Kafka消费者")
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"健康检查过程中出错: {e}")
            return health_status
    
    async def send_message(self, topic: str, message: Dict[str, Any]) -> bool:
        """发送消息到Kafka
        
        Args:
            topic: 目标主题
            message: 消息内容
            
        Returns:
            发送是否成功
        """
        if not self.producer_connected or not self.producer:
            self.logger.warning("Kafka生产者未连接，尝试重新连接")
            producer_success = await self._start_kafka_producer()
            if not producer_success:
                self.logger.error("无法重新连接Kafka生产者")
                return False
        
        try:
            # 将消息序列化为JSON
            value = json.dumps(message).encode('utf-8')
            
            # 如果有taskId，使用它作为键以确保相同任务的消息进入同一分区
            key = None
            if 'taskId' in message:
                key = str(message['taskId']).encode('utf-8')
            
            # 发送消息
            await self.producer.send_and_wait(topic, value=value, key=key)
            self.logger.debug(f"Message sent to {topic}: {message.get('taskId', 'unknown')}")
            return True
        except Exception as e:
            self.logger.error(f"发送消息到Kafka失败: {e}")
            # 尝试重新建立连接
            await self._start_kafka_producer()
            return False
    
    async def get_messages(self, max_records: int = 500, timeout_ms: int = 1000) -> List[Dict[str, Any]]:
        """从Kafka获取消息
        
        Args:
            max_records: 最大获取记录数
            timeout_ms: 轮询超时毫秒数
            
        Returns:
            消息列表
        """
        if not self.consumer_connected or not self.consumer:
            self.logger.warning("Kafka消费者未连接，尝试重新连接")
            consumer_success = await self._start_kafka_consumer()
            if not consumer_success:
                self.logger.error("无法重新连接Kafka消费者")
                return []
        
        try:
            messages = []
            # 获取消息批次
            batch = await self.consumer.getmany(timeout_ms=timeout_ms, max_records=max_records)
            
            for tp, records in batch.items():
                self.logger.debug(f"Got {len(records)} messages from partition {tp.partition}")
                
                for record in records:
                    try:
                        # 反序列化消息
                        message = json.loads(record.value.decode('utf-8'))
                        messages.append(message)
                    except json.JSONDecodeError:
                        self.logger.error(f"无法解析消息: {record.value}")
                    except Exception as e:
                        self.logger.error(f"处理消息时出错: {e}")
            
            return messages
        except Exception as e:
            self.logger.error(f"从Kafka获取消息失败: {e}")
            # 尝试重新建立连接
            await self._start_kafka_consumer()
            return []
    
    async def commit(self):
        """提交消费者偏移量"""
        if not self.consumer_connected or not self.consumer:
            self.logger.warning("无法提交偏移量，Kafka消费者未连接")
            return False
        
        try:
            await self.consumer.commit()
            return True
        except Exception as e:
            self.logger.error(f"提交偏移量失败: {e}")
            return False
    
    async def _start_kafka_producer(self):
        """启动Kafka生产者"""
        try:
            # 关闭现有生产者
            if self.producer:
                self.logger.info("Stopping existing Kafka producer")
                await self.producer.stop()
                self.producer = None
                self.producer_connected = False
            
            # 从配置中获取参数
            bootstrap_servers = self.kafka_config.get('bootstrap_servers', 'localhost:9092')
            
            # 处理安全协议配置，确保为None时使用默认值'PLAINTEXT'
            security_protocol = self.kafka_config.get('security_protocol')
            if security_protocol is None or security_protocol == "null":
                security_protocol = 'PLAINTEXT'
                self.logger.info("Security protocol is null, using default 'PLAINTEXT'")
            
            # 创建生产者
            self.logger.info(f"Creating Kafka producer with bootstrap servers: {bootstrap_servers}")
            
            # 创建生产者包含重试与压缩配置
            producer = AIOKafkaProducer(
                bootstrap_servers=bootstrap_servers,
                security_protocol=security_protocol,
                retry_backoff_ms=self.kafka_config.get('retry_backoff_ms', 500),
                request_timeout_ms=self.kafka_config.get('request_timeout_ms', 30000),
                max_request_size=self.kafka_config.get('max_request_size', 1048576),
                compression_type=self.kafka_config.get('compression_type', 'gzip'),
                max_batch_size=self.kafka_config.get('max_batch_size', 16384),
                linger_ms=self.kafka_config.get('linger_ms', 100),
            )
            
            # 启动生产者
            await producer.start()
            self.producer = producer
            self.producer_connected = True
            self.logger.info("Kafka producer started successfully")
            
            return True
        except Exception as e:
            self.logger.error(f"Error starting Kafka producer: {e}")
            self.producer_connected = False
            return False
    
    async def _get_consumer_group_id_from_pool(self):
        """从ID池中获取一个可用的消费者组ID"""
        try:
            # 导入redis_actor
            from redis_actor import RedisActor
            
            # 创建一个临时RedisActor来获取Redis连接
            redis_actor = RedisActor.remote(self.config)
            await redis_actor.start.remote()
            
            # Redis中存储ID池的键
            id_pool_key = "yolo:kafka:consumer_group_id_pool"
            # Redis中存储已使用ID的键
            used_ids_key = "yolo:kafka:used_consumer_group_ids"
            
            # 获取实例ID作为标记拥有者
            instance_id = self.instance_id
            
            # 首先检查Redis中记录的实例是否仍然活跃
            # 这一步对修复线上环境中ID池耗尽的问题很重要
            if not hasattr(self.__class__, "_cleaned_id_cache"):
                try:
                    self.logger.info("检查并清理僵尸实例占用的消费者组ID")
                    # 获取所有实例ID到消费者组ID的映射
                    mapping_pattern = "yolo:kafka:instance_group_id_mapping:*"
                    mapping_keys = await redis_actor.redis_keys.remote(mapping_pattern)
                    
                    # 获取所有已使用的消费者组ID
                    used_ids = await redis_actor.redis_smembers.remote(used_ids_key)
                    self.logger.info(f"当前已使用的消费者组ID: {used_ids}")
                    
                    # 获取Ray集群中活跃的节点ID
                    ray_nodes = ray.nodes()
                    active_node_ids = [node["NodeID"] for node in ray_nodes]
                    
                    # 清理不在活跃节点列表中的实例占用的ID
                    cleaned_count = 0
                    for key in mapping_keys:
                        # 从key中提取实例ID
                        key_parts = key.split(":")
                        if len(key_parts) > 3:
                            instance_id_from_key = key_parts[3]
                            
                            # 检查实例ID是否仍然活跃
                            is_active = False
                            for node_id in active_node_ids:
                                if node_id in instance_id_from_key:
                                    is_active = True
                                    break
                            
                            if not is_active:
                                # 获取此实例使用的消费者组ID
                                group_id = await redis_actor.redis_get.remote(key)
                                if group_id:
                                    # 从使用中ID集合移除
                                    await redis_actor.redis_srem.remote(used_ids_key, group_id)
                                    # 删除实例到ID的映射
                                    await redis_actor.redis_delete.remote(key)
                                    cleaned_count += 1
                                    self.logger.info(f"已清理僵尸实例 {instance_id_from_key} 占用的ID: {group_id}")
                    
                    if cleaned_count > 0:
                        self.logger.info(f"成功清理 {cleaned_count} 个僵尸实例占用的消费者组ID")
                    else:
                        self.logger.info("没有发现僵尸实例占用的消费者组ID")
                    
                    # 标记已清理
                    self.__class__._cleaned_id_cache = True
                except Exception as e:
                    self.logger.error(f"清理僵尸实例占用的ID时出错: {e}")
            
            # 检查是否已经有ID分配给该实例
            instance_id_mapping_key = f"yolo:kafka:instance_group_id_mapping:{instance_id}"
            existing_id = await redis_actor.redis_get.remote(instance_id_mapping_key)
            
            if existing_id:
                self.logger.info(f"实例 {instance_id} 已分配消费者组ID: {existing_id}")
                return existing_id
            
            # 尝试从ID池中获取一个未使用的ID
            base_group_id = self.kafka_config.get('group_id', 'yolo_service_group')
            
            # 从配置获取ID池大小，默认为10
            num_ids = self.kafka_config.get('kafka_id_pool_size', 10)
            self.logger.info(f"ID池大小: {num_ids}")
            
            # 生产者模式不需要使用消费者组ID
            if self.is_producer_only:
                producer_id = f"{base_group_id}_producer_{str(uuid.uuid4())[:8]}"
                self.logger.info(f"生产者模式，使用特殊ID: {producer_id}")
                
                # 记录此特殊ID
                await redis_actor.redis_set.remote(
                    instance_id_mapping_key,
                    producer_id,
                    ex=3600  # 1小时过期，会被心跳更新
                )
                
                return producer_id
            
            # 消费者模式，从ID池中获取一个可用ID
            for i in range(num_ids):
                candidate_id = f"{base_group_id}_{i}"
                
                # 检查此ID是否已被使用
                is_used = False
                try:
                    members = await redis_actor.redis_smembers.remote(used_ids_key)
                    is_used = candidate_id in members
                except Exception as e:
                    self.logger.error(f"检查ID是否已使用出错: {e}")
                
                if not is_used:
                    # 标记此ID为已使用
                    await redis_actor.redis_sadd.remote(used_ids_key, candidate_id)
                    # 设置实例到ID的映射，带过期时间
                    await redis_actor.redis_set.remote(
                        instance_id_mapping_key,
                        candidate_id,
                        ex=3600  # 1小时过期，会被心跳更新
                    )
                    
                    self.logger.info(f"实例 {instance_id} 分配到消费者组ID: {candidate_id}")
                    return candidate_id
            
            # 如果常规ID池中所有ID都已被使用，则使用备用ID空间
            # 使用实例ID的哈希值对池大小取模，避免随机ID导致的授权问题
            import hashlib
            hash_val = int(hashlib.md5(instance_id.encode()).hexdigest(), 16)
            backup_index = hash_val % num_ids
            backup_id = f"{base_group_id}_{backup_index}_backup"
            
            self.logger.warning(f"所有常规ID已被使用，实例 {instance_id} 使用备用ID: {backup_id}")
            
            # 标记此备用ID为已使用
            await redis_actor.redis_sadd.remote(used_ids_key, backup_id)
            # 设置实例到ID的映射
            await redis_actor.redis_set.remote(
                instance_id_mapping_key,
                backup_id,
                ex=3600  # 1小时过期
            )
            
            return backup_id
            
        except Exception as e:
            self.logger.error(f"从ID池获取消费者组ID失败: {e}")
            # 出错时使用实例ID的哈希作为组ID的一部分，保证在同一集群中唯一性
            import hashlib
            hash_val = int(hashlib.md5(self.instance_id.encode()).hexdigest(), 16) % 10000
            base_group_id = self.kafka_config.get('group_id', 'yolo_service_group')
            return f"{base_group_id}_error_{hash_val}"
        finally:
            # 停止临时RedisActor
            if 'redis_actor' in locals():
                await redis_actor.stop.remote()
    
    async def _release_consumer_group_id(self):
        """释放当前实例使用的消费者组ID"""
        try:
            # 生产者模式无需释放消费者组ID
            if self.is_producer_only:
                return
                
            # 导入redis_actor
            from redis_actor import RedisActor
            
            # 创建一个临时RedisActor来获取Redis连接
            redis_actor = RedisActor.remote(self.config)
            await redis_actor.start.remote()
            
            instance_id = self.instance_id
            instance_id_mapping_key = f"yolo:kafka:instance_group_id_mapping:{instance_id}"
            
            # 获取当前实例使用的消费者组ID
            group_id = await redis_actor.redis_get.remote(instance_id_mapping_key)
            
            if group_id:
                # 检查是不是producer或error类型的ID
                if "_producer_" in group_id or "_error_" in group_id:
                    # 这些ID不需要从used_ids中移除
                    self.logger.info(f"实例 {instance_id} 的ID {group_id} 是特殊类型，直接删除映射")
                    await redis_actor.redis_delete.remote(instance_id_mapping_key)
                else:
                    used_ids_key = "yolo:kafka:used_consumer_group_ids"
                    # 从已使用集合中移除
                    await redis_actor.redis_srem.remote(used_ids_key, group_id)
                    # 删除实例到ID的映射
                    await redis_actor.redis_delete.remote(instance_id_mapping_key)
                    
                    self.logger.info(f"实例 {instance_id} 释放了消费者组ID: {group_id}")
            else:
                self.logger.info(f"实例 {instance_id} 没有分配的消费者组ID需要释放")
        except Exception as e:
            self.logger.error(f"释放消费者组ID失败: {e}")
        finally:
            # 停止临时RedisActor
            if 'redis_actor' in locals():
                await redis_actor.stop.remote()
    
    async def _maintain_consumer_group_id_heartbeat(self):
        """维护消费者组ID的心跳"""
        self.logger.info("开始维护消费者组ID心跳")
        
        # 最后一次清理时间
        last_cleanup_time = time.time()
        
        while self.running:
            try:
                # 导入redis_actor
                from redis_actor import RedisActor
                
                # 创建一个临时RedisActor来获取Redis连接
                redis_actor = RedisActor.remote(self.config)
                await redis_actor.start.remote()
                
                instance_id = self.instance_id
                instance_id_mapping_key = f"yolo:kafka:instance_group_id_mapping:{instance_id}"
                
                # 获取当前实例使用的消费者组ID
                group_id = await redis_actor.redis_get.remote(instance_id_mapping_key)
                
                if group_id:
                    # 更新过期时间
                    await redis_actor.redis_expire.remote(instance_id_mapping_key, 3600)  # 1小时
                    self.logger.debug(f"更新消费者组ID {group_id} 的心跳")
                    
                    # 每10分钟清理一次孤立的ID
                    current_time = time.time()
                    if current_time - last_cleanup_time > 600:  # 10分钟
                        self.logger.info("定期清理孤立的消费者组ID")
                        await self._cleanup_orphaned_consumer_group_ids(redis_actor)
                        last_cleanup_time = current_time
                
                # 停止临时RedisActor
                await redis_actor.stop.remote()
                
                # 每分钟更新一次
                await asyncio.sleep(60)
            except Exception as e:
                self.logger.error(f"消费者组ID心跳维护出错: {e}")
                await asyncio.sleep(10)  # 出错时短暂等待后重试
    
    async def _cleanup_orphaned_consumer_group_ids(self, redis_actor):
        """清理没有活跃实例的消费者组ID"""
        try:
            used_ids_key = "yolo:kafka:used_consumer_group_ids"
            # 获取所有已使用的ID
            used_ids = await redis_actor.redis_smembers.remote(used_ids_key)
            
            # 如果没有使用的ID，提前返回
            if not used_ids:
                self.logger.debug("没有使用中的消费者组ID")
                return
                
            # 获取所有实例到ID的映射键
            mapping_pattern = "yolo:kafka:instance_group_id_mapping:*"
            instance_mapping_keys = await redis_actor.redis_keys.remote(mapping_pattern)
            
            # 获取所有映射的ID值
            active_group_ids = []
            for mapping_key in instance_mapping_keys:
                mapped_id = await redis_actor.redis_get.remote(mapping_key)
                if mapped_id:
                    active_group_ids.append(mapped_id)
            
            # 获取Ray集群中活跃的节点
            ray_nodes = ray.nodes()
            active_node_ids = [node["NodeID"] for node in ray_nodes]
            
            # 找出孤立的ID (在used_ids中但不在active_group_ids中)
            orphaned_ids = [group_id for group_id in used_ids if group_id not in active_group_ids]
            
            # 清理无效的实例到ID映射，这些实例可能已经不存在
            orphaned_mappings = []
            for key in instance_mapping_keys:
                # 从key中提取实例ID
                key_parts = key.split(":")
                if len(key_parts) > 3:
                    instance_id_from_key = key_parts[3]
                    
                    # 检查实例ID是否仍然活跃
                    is_active = False
                    for node_id in active_node_ids:
                        if node_id in instance_id_from_key:
                            is_active = True
                            break
                    
                    if not is_active:
                        # 获取此实例使用的消费者组ID
                        group_id = await redis_actor.redis_get.remote(key)
                        if group_id:
                            # 从使用中ID集合移除
                            await redis_actor.redis_srem.remote(used_ids_key, group_id)
                            # 删除实例到ID的映射
                            await redis_actor.redis_delete.remote(key)
                            orphaned_mappings.append((instance_id_from_key, group_id))
            
            # 清理孤立的ID
            for orphaned_id in orphaned_ids:
                await redis_actor.redis_srem.remote(used_ids_key, orphaned_id)
                self.logger.info(f"已清理孤立的消费者组ID: {orphaned_id}")
            
            # 输出清理结果
            if orphaned_ids or orphaned_mappings:
                self.logger.info(f"共清理 {len(orphaned_ids)} 个孤立消费者组ID")
                if orphaned_mappings:
                    self.logger.info(f"共清理 {len(orphaned_mappings)} 个僵尸实例ID映射")
            else:
                self.logger.debug("没有发现需要清理的孤立ID或僵尸实例")
        except Exception as e:
            self.logger.error(f"清理孤立消费者组ID出错: {e}")
    
    async def _start_kafka_consumer(self):
        """启动Kafka消费者"""
        try:
            # 关闭现有消费者
            if self.consumer:
                self.logger.info("Stopping existing Kafka consumer")
                await self.consumer.stop()
                self.consumer = None
                self.consumer_connected = False
            
            # 从配置中获取参数
            bootstrap_servers = self.kafka_config.get('bootstrap_servers', 'localhost:9092')
            topics = [self.kafka_config.get('input_topic', 'video_process_topic')]
            
            # 从ID池获取消费者组ID
            unique_group_id = await self._get_consumer_group_id_from_pool()
            
            # 添加唯一标识以避免消费者ID冲突，但保留主ID一致
            consumer_id = f"{unique_group_id}_{ray.get_runtime_context().get_node_id()}"
            
            self.logger.info(f"Using consumer group ID from pool: {unique_group_id}")
            
            # 处理安全协议配置，确保为None时使用默认值'PLAINTEXT'
            security_protocol = self.kafka_config.get('security_protocol')
            if security_protocol is None or security_protocol == "null":
                security_protocol = 'PLAINTEXT'
                self.logger.info("Security protocol is null, using default 'PLAINTEXT'")
            
            # 创建消费者-增加配置来改善稳定性
            self.logger.info(f"Creating Kafka consumer with bootstrap servers: {bootstrap_servers}, group: {unique_group_id}")
            consumer = AIOKafkaConsumer(
                *topics,
                bootstrap_servers=bootstrap_servers,
                group_id=unique_group_id,
                client_id=consumer_id,
                auto_offset_reset=self.kafka_config.get('auto_offset_reset', 'earliest'),
                enable_auto_commit=self.kafka_config.get('enable_auto_commit', False),
                max_poll_records=self.kafka_config.get('max_poll_records', 500),
                max_poll_interval_ms=self.kafka_config.get('max_poll_interval_ms', 300000),  # 5分钟
                session_timeout_ms=self.kafka_config.get('session_timeout_ms', 60000),  # 60秒
                heartbeat_interval_ms=self.kafka_config.get('heartbeat_interval_ms', 10000),  # 10秒
                security_protocol=security_protocol,
            )
            
            # 启动消费者
            await consumer.start()
            self.consumer = consumer
            self.consumer_connected = True
            self.logger.info("Kafka consumer started successfully")
            
            # 检查分区分配
            partitions = consumer.assignment()
            if not partitions:
                self.logger.warning("Kafka consumer has no partitions assigned")
            else:
                self.logger.info(f"Kafka consumer assigned partitions: {partitions}")
            
            return True
        except Exception as e:
            self.logger.error(f"Error starting Kafka consumer: {e}")
            self.consumer_connected = False
            return False
