#!/usr/bin/env python3
"""
测试OSS V2版本迁移后的功能
验证从oss2迁移到alibabacloud_oss_v2后的兼容性
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from oss_manager import OSSDataManager

def test_oss_config_loading():
    """测试OSS配置加载功能"""
    print("🔧 测试OSS配置加载...")
    
    try:
        # 测试默认配置加载
        oss_manager = OSSDataManager()
        print(f"✅ OSS启用状态: {oss_manager.enabled}")
        print(f"✅ 存储桶名称: {oss_manager.bucket_name}")
        print(f"✅ 端点: {oss_manager.endpoint}")
        print(f"✅ 区域: {oss_manager.region}")
        
        if oss_manager.enabled:
            print("✅ OSS客户端初始化成功")
        else:
            print("ℹ️ OSS存储已禁用")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {str(e)}")
        return False

def test_oss_basic_operations():
    """测试OSS基本操作"""
    print("\n📤 测试OSS基本操作...")
    
    try:
        oss_manager = OSSDataManager()
        
        if not oss_manager.enabled:
            print("ℹ️ OSS已禁用，跳过基本操作测试")
            return True
        
        # 测试数据
        test_data = {
            "test_id": "v2_migration_test",
            "timestamp": datetime.now().isoformat(),
            "message": "这是V2迁移测试数据",
            "version": "v2.0.0"
        }
        
        # 测试JSON上传
        print("  📤 测试JSON数据上传...")
        oss_manager._upload_json("test/v2_migration_test.json", test_data)
        print("  ✅ JSON数据上传成功")
        
        # 测试文本上传
        print("  📤 测试文本数据上传...")
        test_text = "这是V2迁移测试的文本内容\n包含多行文本\n用于验证上传功能"
        oss_manager._upload_text("test/v2_migration_test.txt", test_text)
        print("  ✅ 文本数据上传成功")
        
        # 测试JSONL上传
        print("  📤 测试JSONL数据上传...")
        test_jsonl_data = [
            {"id": 1, "content": "第一条测试数据"},
            {"id": 2, "content": "第二条测试数据"},
            {"id": 3, "content": "第三条测试数据"}
        ]
        oss_manager._upload_jsonl("test/v2_migration_test.jsonl", test_jsonl_data)
        print("  ✅ JSONL数据上传成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本操作测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_oss_download_operations():
    """测试OSS下载操作"""
    print("\n⬇️ 测试OSS下载操作...")
    
    try:
        oss_manager = OSSDataManager()
        
        if not oss_manager.enabled:
            print("ℹ️ OSS已禁用，跳过下载操作测试")
            return True
        
        # 尝试下载刚才上传的JSON数据
        print("  ⬇️ 测试JSON数据下载...")
        try:
            downloaded_data = oss_manager.download_json("test/v2_migration_test.json")
            print(f"  ✅ JSON数据下载成功: {downloaded_data.get('test_id')}")
            
            # 验证数据完整性
            if downloaded_data.get('test_id') == 'v2_migration_test':
                print("  ✅ 数据完整性验证通过")
            else:
                print("  ⚠️ 数据完整性验证失败")
                
        except Exception as e:
            print(f"  ⚠️ JSON下载测试失败（可能是正常的）: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载操作测试失败: {str(e)}")
        return False

def test_oss_list_operations():
    """测试OSS列表操作"""
    print("\n📋 测试OSS列表操作...")
    
    try:
        oss_manager = OSSDataManager()
        
        if not oss_manager.enabled:
            print("ℹ️ OSS已禁用，跳过列表操作测试")
            return True
        
        # 测试列出文件
        print("  📋 测试文件列表功能...")
        files = oss_manager.list_files("test/", max_keys=10)
        print(f"  ✅ 找到 {len(files)} 个测试文件")
        
        if files:
            print("  📄 测试文件列表:")
            for file in files[:5]:  # 只显示前5个
                print(f"    - {file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 列表操作测试失败: {str(e)}")
        return False

def test_oss_high_level_operations():
    """测试OSS高级操作"""
    print("\n🚀 测试OSS高级操作...")
    
    try:
        oss_manager = OSSDataManager()
        
        if not oss_manager.enabled:
            print("ℹ️ OSS已禁用，跳过高级操作测试")
            return True
        
        # 测试视频分析保存
        print("  📹 测试视频分析结果保存...")
        test_analysis_result = {
            "title": "V2迁移测试视频",
            "summary": "这是一个用于测试V2迁移的视频分析结果",
            "confidence": 0.95,
            "objects": ["person", "package"],
            "actions": ["delivery", "doorbell"]
        }
        
        oss_path = oss_manager.save_video_analysis(
            event_id="v2_test_12345",
            esn="V2_TEST_ESN",
            video_url="https://example.com/v2_test_video.m3u8",
            analysis_result=test_analysis_result,
            reasoning_content="V2迁移测试的推理内容",
            processing_info={"version": "v2.0.0", "migration_test": True}
        )
        print(f"  ✅ 视频分析结果保存成功: {oss_path}")
        
        # 测试搜索日志保存
        print("  🔍 测试搜索日志保存...")
        test_request = {"query": "V2迁移测试", "esn": "V2_TEST_ESN"}
        test_response = {"results": [], "total": 0, "version": "v2.0.0"}
        
        log_path = oss_manager.save_search_log(
            request_data=test_request,
            response_data=test_response,
            performance_data={"response_time": 0.1, "migration_test": True}
        )
        print(f"  ✅ 搜索日志保存成功: {log_path}")
        
        # 测试存储统计
        print("  📊 测试存储统计信息...")
        stats = oss_manager.get_storage_stats()
        print("  ✅ 存储统计信息:")
        for key, value in stats.items():
            print(f"    {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级操作测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始OSS V2迁移测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_oss_config_loading())
    test_results.append(test_oss_basic_operations())
    test_results.append(test_oss_download_operations())
    test_results.append(test_oss_list_operations())
    test_results.append(test_oss_high_level_operations())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OSS V2迁移成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置和网络连接")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
