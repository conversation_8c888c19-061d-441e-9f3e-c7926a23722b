#!/usr/bin/env python3
"""
测试ModelProcessor的异步HTTP请求功能
"""

import asyncio
import logging
from PIL import Image
import numpy as np
from model_process import ModelProcessor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_text_embedding():
    """测试文本嵌入功能"""
    print("\n" + "="*50)
    print("测试文本嵌入功能")
    print("="*50)
    
    try:
        processor = ModelProcessor("config_dev.yaml")
        
        test_text = "一只可爱的猫咪在阳光下睡觉"
        print(f"测试文本: {test_text}")
        
        embedding = await processor.compute_chinese_clip_embeddings_with_text(test_text)
        
        print(f"嵌入向量维度: {len(embedding)}")
        print(f"向量值范围: [{min(embedding):.4f}, {max(embedding):.4f}]")
        print("✅ 文本嵌入测试成功")
        
    except Exception as e:
        print(f"❌ 文本嵌入测试失败: {e}")

async def test_image_embedding():
    """测试图像嵌入功能"""
    print("\n" + "="*50)
    print("测试图像嵌入功能")
    print("="*50)
    
    try:
        processor = ModelProcessor("config_dev.yaml")
        
        # 创建测试图像
        test_images = []
        for i in range(3):
            # 创建随机颜色的测试图像
            color = (np.random.randint(50, 200), np.random.randint(50, 200), np.random.randint(50, 200))
            image = Image.new('RGB', (224, 224), color)
            test_images.append(image)
        
        print(f"测试图像数量: {len(test_images)}")
        
        embeddings = await processor.compute_chinese_clip_embeddings_with_frames(test_images)
        
        print(f"嵌入向量数量: {len(embeddings)}")
        if embeddings:
            print(f"嵌入向量维度: {len(embeddings[0])}")
            print(f"第一个向量值范围: [{min(embeddings[0]):.4f}, {max(embeddings[0]):.4f}]")
        print("✅ 图像嵌入测试成功")
        
    except Exception as e:
        print(f"❌ 图像嵌入测试失败: {e}")

async def test_similarity_computation():
    """测试相似度计算功能"""
    print("\n" + "="*50)
    print("测试相似度计算功能")
    print("="*50)
    
    try:
        processor = ModelProcessor("config_dev.yaml")
        
        # 创建测试图像
        test_images = []
        for i in range(2):
            color = (np.random.randint(50, 200), np.random.randint(50, 200), np.random.randint(50, 200))
            image = Image.new('RGB', (224, 224), color)
            test_images.append(image)
        
        test_text = "一只猫咪在休息"
        
        print(f"测试文本: {test_text}")
        print(f"测试图像数量: {len(test_images)}")
        
        similarities = await processor.compute_similarity_between_text_and_frames(test_text, test_images)
        
        print(f"相似度分数: {similarities}")
        print(f"平均相似度: {np.mean(similarities):.4f}")
        print("✅ 相似度计算测试成功")
        
    except Exception as e:
        print(f"❌ 相似度计算测试失败: {e}")

async def test_retry_mechanism():
    """测试重试机制"""
    print("\n" + "="*50)
    print("测试重试机制（使用错误的URL）")
    print("="*50)
    
    try:
        # 创建一个使用错误URL的处理器
        processor = ModelProcessor("config_dev.yaml")
        processor.chinese_clip_url = "http://localhost:9999"  # 错误的端口
        processor.max_retries = 2  # 减少重试次数以加快测试
        processor.retry_delay = 0.5  # 减少重试延迟
        
        test_text = "测试重试机制"
        print(f"使用错误的URL: {processor.chinese_clip_url}")
        print(f"最大重试次数: {processor.max_retries}")
        
        embedding = await processor.compute_chinese_clip_embeddings_with_text(test_text)
        
        # 应该返回零向量
        if embedding == [0.0] * 512:
            print("✅ 重试机制测试成功：正确返回零向量")
        else:
            print("❌ 重试机制测试失败：未返回预期的零向量")
        
    except Exception as e:
        print(f"❌ 重试机制测试异常: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始测试ModelProcessor异步HTTP功能")
    
    # 测试各个功能
    await test_text_embedding()
    await test_image_embedding()
    await test_similarity_computation()
    await test_retry_mechanism()
    
    print("\n" + "="*50)
    print("🎉 所有测试完成")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main()) 
