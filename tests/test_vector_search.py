#!/usr/bin/env python3
"""
向量检索服务测试示例

这个测试文件展示了如何使用新的资源隔离架构：
1. 视频处理Actor Pool - 专门处理视频分析任务
2. 向量检索Actor Pool - 专门处理向量检索任务

两个Actor Pool相互独立，不会产生资源竞争。
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List


class VectorSearchClient:
    """向量检索客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8009"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def search_videos(self, esn: str, query_text: str, limit: int = 5, drop_ratio: float = 0.2) -> Dict:
        """搜索视频"""
        url = f"{self.base_url}/kaadas_ai/video_unstanding/search"
        data = {
            "esn": esn,
            "query_text": query_text,
            "limit": limit,
            "drop_ratio": drop_ratio
        }
        print(f"使用的服务url: {url}")
        
        start_time = time.time()
        async with self.session.post(url, json=data) as response:
            elapsed = time.time() - start_time
            
            # 检查HTTP状态码
            if response.status != 200:
                error_text = await response.text()
                print(f"❌ 搜索失败 - HTTP {response.status}")
                print(f"   错误信息: {error_text}")
                return {"error": f"HTTP {response.status}: {error_text}"}
            
            result = await response.json()
            print(f"✅ 搜索完成 - 耗时: {elapsed:.2f}秒")
            print(f"   查询: '{query_text}'")
            print(f"   ESN: {esn}")
            
            if result.get("results"):
                search_data = result["results"][0]
                total_matches = search_data.get("total_matches", 0)
                print(f"   总匹配数: {total_matches}")
                
                # 显示各类型的匹配结果
                title_matches = len(search_data.get("title_matches", []))
                summary_matches = len(search_data.get("summary_matches", []))
                frame_matches = len(search_data.get("frame_matches", []))
                
                print(f"   - 标题匹配: {title_matches} 条")
                print(f"   - 摘要匹配: {summary_matches} 条")
                print(f"   - 帧匹配: {frame_matches} 条")
            
            return result
    
    async def get_search_stats(self) -> Dict:
        """获取搜索服务统计信息"""
        url = f"{self.base_url}/kaadas_ai/video_unstanding/search/stats"
        
        async with self.session.get(url) as response:
            # 检查HTTP状态码
            if response.status != 200:
                error_text = await response.text()
                print(f"❌ 获取统计信息失败 - HTTP {response.status}")
                print(f"   错误信息: {error_text}")
                print(f"   请求URL: {url}")
                return {"error": f"HTTP {response.status}: {error_text}"}
            
            stats = await response.json()
            
            print("📊 搜索服务统计信息:")
            print(f"   视频记录数: {stats.get('video_count', 0)}")
            print(f"   帧记录数: {stats.get('frame_count', 0)}")
            print(f"   视频处理Actor池: {stats.get('video_actor_pool_busy', 0)}/{stats.get('video_actor_pool_size', 0)} (繁忙/总数)")
            print(f"   向量检索Actor池: {stats.get('search_actor_pool_busy', 0)}/{stats.get('search_actor_pool_size', 0)} (繁忙/总数)")
            
            return stats


async def test_concurrent_searches():
    """测试并发搜索，展示独立Actor Pool的优势"""
    
    # 测试查询列表
    test_queries = [
        ("ESN001", "快递员送包裹"),
        ("ESN001", "外卖员送餐"),
        ("ESN002", "可疑人员"),
        ("ESN002", "宠物"),
        ("ESN003", "夜间活动"),
    ]
    
    print("🚀 开始并发向量检索测试...")
    print(f"   将同时执行 {len(test_queries)} 个搜索任务")
    print("   这些任务将使用独立的向量检索Actor Pool，不会影响视频处理任务\n")
    
    async with VectorSearchClient() as client:
        # 先获取服务统计信息
        await client.get_search_stats()
        print()
        
        # 并发执行所有搜索任务
        start_time = time.time()
        
        tasks = [
            client.search_videos(esn, query, limit=3, drop_ratio=0.1)
            for esn, query in test_queries
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        print(f"\n🎯 并发测试完成!")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   平均每个查询: {total_time/len(test_queries):.2f}秒")
        print(f"   并发效率: {'高' if total_time < len(test_queries) * 2 else '中等'}")
        
        # 统计成功和失败的任务
        successes = sum(1 for r in results if not isinstance(r, Exception))
        failures = len(results) - successes
        
        print(f"   成功: {successes}/{len(test_queries)}")
        if failures > 0:
            print(f"   失败: {failures}/{len(test_queries)}")
        
        # 再次获取服务统计信息，查看Actor池状态
        print()
        await client.get_search_stats()


async def test_mixed_workload():
    """模拟混合工作负载测试"""
    print("\n" + "="*50)
    print("🔄 混合工作负载测试")
    print("   模拟同时有视频处理和向量检索任务的场景")
    print("   验证两个独立Actor Pool不会互相影响")
    print("="*50)
    
    async with VectorSearchClient() as client:
        # 模拟高频搜索请求
        search_tasks = []
        for i in range(10):
            esn = f"ESN_{i%3:03d}"
            query = f"测试查询_{i}"
            task = client.search_videos(esn, query, limit=2)
            search_tasks.append(task)
            
            # 每个任务间隔一点时间，模拟真实场景
            await asyncio.sleep(0.1)
        
        print("📡 启动了10个搜索任务...")
        
        # 等待所有搜索任务完成
        results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        successes = sum(1 for r in results if not isinstance(r, Exception))
        print(f"✅ 搜索任务完成: {successes}/10 成功")


async def demonstrate_resource_isolation():
    """演示资源隔离的效果"""
    print("\n" + "="*50)
    print("🛡️  资源隔离演示")
    print("="*50)
    
    print("📋 架构说明:")
    print("   1. 视频处理Actor Pool (video_actor_pool)")
    print("      - 专门处理视频分析任务 (process方法)")
    print("      - 使用较多CPU资源")
    print("      - 处理时间较长 (通常几十秒)")
    print()
    print("   2. 向量检索Actor Pool (search_actor_pool)")
    print("      - 专门处理向量检索任务 (search方法)")
    print("      - 使用较少CPU资源")
    print("      - 处理时间较短 (通常几秒)")
    print()
    print("   3. 优势:")
    print("      ✅ 资源隔离：两个Pool互不影响")
    print("      ✅ 性能稳定：检索服务不会被视频处理阻塞")
    print("      ✅ 扩展性好：可以独立调整每个Pool的大小")
    print("      ✅ 故障隔离：一个Pool的问题不会影响另一个")
    
    async with VectorSearchClient() as client:
        await client.get_search_stats()


if __name__ == "__main__":
    print("🎬 视频理解服务 - 向量检索测试")
    print("="*50)
    
    async def main():
        # 演示资源隔离
        await demonstrate_resource_isolation()
        
        # 测试并发搜索
        await test_concurrent_searches()
        
        # 测试混合工作负载
        await test_mixed_workload()
        
        print("\n🎉 所有测试完成!")
        print("   独立Actor Pool架构成功解决了资源竞争问题")
    
    # 运行测试
    asyncio.run(main()) 
