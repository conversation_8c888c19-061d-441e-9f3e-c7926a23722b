#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Callback服务测试脚本

测试callback服务的各种功能：
1. 配置加载测试
2. 数据准备测试
3. 回调发送测试
4. 连接测试
5. 重试机制测试
"""

import asyncio
import json
import logging
import os
from aiohttp import web, ClientSession
from callback_service import CallbackService


# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class MockCallbackServer:
    """模拟业务方回调服务器"""
    
    def __init__(self, port=8888):
        self.port = port
        self.received_callbacks = []
        self.response_status = 200
        self.response_delay = 0
        
    async def callback_handler(self, request):
        """处理回调请求"""
        try:
            # 模拟处理延迟
            if self.response_delay > 0:
                await asyncio.sleep(self.response_delay)
            
            # 获取请求数据
            data = await request.json()
            self.received_callbacks.append(data)
            
            logger.info(f"Received callback: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            # 验证必要字段
            required_fields = ["accessToken", "esn", "url", "EventId"]
            for field in required_fields:
                if field not in data:
                    return web.json_response(
                        {"error": f"Missing required field: {field}"}, 
                        status=400
                    )
            
            # 返回响应
            if self.response_status == 200:
                return web.json_response({
                    "success": True,
                    "message": "Callback received successfully",
                    "event_id": data.get("EventId")
                })
            else:
                return web.json_response(
                    {"error": "Mock error response"}, 
                    status=self.response_status
                )
                
        except Exception as e:
            logger.error(f"Error handling callback: {str(e)}")
            return web.json_response(
                {"error": str(e)}, 
                status=500
            )
    
    async def start_server(self):
        """启动模拟服务器"""
        app = web.Application()
        app.router.add_post('/ai/video/understanding/callback', self.callback_handler)
        
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', self.port)
        await site.start()
        
        logger.info(f"Mock callback server started on http://localhost:{self.port}")
        return runner


async def test_callback_service():
    """测试callback服务功能"""
    
    # 启动模拟服务器
    mock_server = MockCallbackServer()
    runner = await mock_server.start_server()
    
    try:
        # 设置测试环境
        os.environ['DEPLOY_MODE'] = 'dev'
        
        # 修改配置文件中的URL为测试服务器
        callback_service = CallbackService()
        callback_service.url = "http://localhost:8888/ai/video/understanding/callback"
        callback_service.access_token = "test_token_123"
        callback_service.enabled = True
        callback_service.async_mode = False  # 同步模式便于测试
        
        logger.info("=" * 60)
        logger.info("开始测试Callback服务")
        logger.info("=" * 60)
        
        # 测试1: 连接测试
        logger.info("\n1. 测试连接...")
        connection_ok = await callback_service.test_connection()
        logger.info(f"连接测试结果: {'成功' if connection_ok else '失败'}")
        
        # 测试2: 正常回调测试
        logger.info("\n2. 测试正常回调...")
        test_video_result = {
            "esn": "TEST_ESN_001",
            "event_id": 12345,
            "video_url": "https://example.com/test_video.m3u8",
            "title": "快递员送包裹",
            "timestamp": "2024-12-27 14:30:00",
            "num_persons": 1,
            "persons": json.dumps([{
                "gender": "男性",
                "age": 30,
                "description": "快递员，穿蓝色制服",
                "suspicious": False,
                "reason": "",
                "time_appeared": "14:30:00"
            }], ensure_ascii=False),
            "couriers": json.dumps([{
                "company": "顺丰速运",
                "uniform": "蓝色制服",
                "has_package": True,
                "description": "正常送件",
                "suspicious": False,
                "reason": "",
                "time_appeared": "14:30:00"
            }], ensure_ascii=False),
            "food_deliverers": "[]",
            "packages": json.dumps([{
                "type": "快递",
                "size": "medium",
                "description": "纸箱包裹",
                "time_appeared": "14:30:00"
            }], ensure_ascii=False),
            "pets": "[]",
            "summary": "快递员正常送包裹到门口",
            "security_risk": "无风险",
            "recommendation": "注意及时取走包裹"
        }
        
        callback_success = await callback_service.send_callback(test_video_result)
        logger.info(f"正常回调测试结果: {'成功' if callback_success else '失败'}")
        
        # 测试3: 异步模式测试
        logger.info("\n3. 测试异步模式...")
        callback_service.async_mode = True
        async_success = await callback_service.send_callback(test_video_result)
        logger.info(f"异步模式测试结果: {'成功' if async_success else '失败'}")
        
        # 等待异步任务完成
        await asyncio.sleep(2)
        
        # 测试4: 重试机制测试
        logger.info("\n4. 测试重试机制...")
        mock_server.response_status = 500  # 模拟服务器错误
        callback_service.async_mode = False
        callback_service.max_retries = 2
        callback_service.retry_delay = 1
        
        retry_success = await callback_service.send_callback(test_video_result)
        logger.info(f"重试机制测试结果: {'失败(预期)' if not retry_success else '意外成功'}")
        
        # 恢复正常状态
        mock_server.response_status = 200
        
        # 测试5: 超时测试
        logger.info("\n5. 测试超时机制...")
        mock_server.response_delay = 5  # 5秒延迟
        callback_service.timeout = 2    # 2秒超时
        callback_service.max_retries = 1
        
        timeout_success = await callback_service.send_callback(test_video_result)
        logger.info(f"超时测试结果: {'失败(预期)' if not timeout_success else '意外成功'}")
        
        # 测试6: 禁用状态测试
        logger.info("\n6. 测试禁用状态...")
        callback_service.enabled = False
        disabled_success = await callback_service.send_callback(test_video_result)
        logger.info(f"禁用状态测试结果: {'成功(跳过)' if disabled_success else '失败'}")
        
        # 显示接收到的回调
        logger.info(f"\n模拟服务器接收到的回调数量: {len(mock_server.received_callbacks)}")
        for i, callback in enumerate(mock_server.received_callbacks):
            logger.info(f"回调 {i+1}: EventId={callback.get('EventId')}, esn={callback.get('esn')}")
        
        logger.info("\n" + "=" * 60)
        logger.info("所有测试完成!")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止模拟服务器
        await runner.cleanup()
        logger.info("模拟服务器已停止")


async def test_production_config():
    """测试生产配置"""
    logger.info("\n" + "=" * 60)
    logger.info("测试生产配置加载")
    logger.info("=" * 60)
    
    try:
        os.environ['DEPLOY_MODE'] = 'dev'
        callback_service = CallbackService()
        
        logger.info(f"配置状态:")
        logger.info(f"  - 启用状态: {callback_service.enabled}")
        logger.info(f"  - 回调URL: {callback_service.url}")
        logger.info(f"  - 访问令牌: {callback_service.access_token[:10]}..." if callback_service.access_token else "  - 访问令牌: 未配置")
        logger.info(f"  - 超时时间: {callback_service.timeout}秒")
        logger.info(f"  - 最大重试: {callback_service.max_retries}次")
        logger.info(f"  - 异步模式: {callback_service.async_mode}")
        
    except Exception as e:
        logger.error(f"生产配置测试失败: {str(e)}")


async def main():
    """主测试函数"""
    await test_production_config()
    await test_callback_service()


if __name__ == "__main__":
    asyncio.run(main()) 
