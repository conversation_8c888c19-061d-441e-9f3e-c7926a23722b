#!/usr/bin/env python3
"""
智能筛选系统完整端到端测试套件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import logging
import time
import json
from pathlib import Path
from PIL import Image
import numpy as np
import tempfile
from typing import List, Dict, Any

from model_process import ModelProcessor
from video_processor import VideoProcessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SmartFilterTestSuite:
    """智能筛选完整测试套件"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始智能筛选系统完整测试...")
        
        tests = [
            ("基础功能测试", self.test_basic_functionality),
            ("性能基准测试", self.test_performance_benchmark),
            ("错误处理测试", self.test_error_handling),
            ("配置验证测试", self.test_configuration_validation),
            ("内存和资源测试", self.test_memory_and_resources),
            ("端到端集成测试", self.test_end_to_end_integration),
            ("压力测试", self.test_stress_scenarios),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 执行: {test_name}")
            try:
                start_time = time.time()
                result = await test_func()
                end_time = time.time()
                
                self.test_results[test_name] = {
                    "status": "PASS" if result else "FAIL",
                    "duration": end_time - start_time,
                    "details": result if isinstance(result, dict) else {"success": result}
                }
                
                status_emoji = "✅" if result else "❌"
                logger.info(f"{status_emoji} {test_name}: {'通过' if result else '失败'} ({end_time - start_time:.2f}s)")
                
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "ERROR",
                    "duration": 0,
                    "error": str(e)
                }
                logger.error(f"❌ {test_name}: 异常 - {e}")
        
        # 生成测试报告
        await self.generate_test_report()
        return self.test_results

    async def test_basic_functionality(self) -> Dict[str, Any]:
        """基础功能测试"""
        try:
            processor = ModelProcessor()
            
            # 检查智能筛选可用性
            is_available = processor.is_smart_filter_available()
            
            # 获取配置信息
            detection_info = processor.get_detection_info()
            stats = processor.get_smart_filter_stats()
            
            # 创建测试帧
            test_frames = self._create_test_frames(15)
            test_timestamps = [i * 2.0 for i in range(15)]
            
            # 测试智能筛选
            if is_available:
                selected_indices, scores = await processor._smart_filter_frames(test_frames, test_timestamps)
                filter_success = len(selected_indices) > 0
            else:
                # 测试回退机制
                selected_indices, scores = processor._fallback_uniform_sampling(test_frames, test_timestamps)
                filter_success = len(selected_indices) > 0
            
            return {
                "smart_filter_available": is_available,
                "detection_info_valid": bool(detection_info.get("available")),
                "stats_valid": bool(stats.get("available")),
                "filtering_works": filter_success,
                "selected_frames": len(selected_indices),
                "score_range": [min(scores), max(scores)] if scores else [0, 0]
            }
            
        except Exception as e:
            logger.error(f"基础功能测试失败: {e}")
            return False

    async def test_performance_benchmark(self) -> Dict[str, Any]:
        """性能基准测试"""
        try:
            processor = ModelProcessor()
            
            # 不同规模的测试
            test_cases = [
                (10, "小规模"),
                (25, "中规模"), 
                (50, "大规模"),
                (100, "超大规模")
            ]
            
            benchmark_results = {}
            
            for frame_count, scale_name in test_cases:
                logger.info(f"  测试 {scale_name}: {frame_count} 帧")
                
                test_frames = self._create_test_frames(frame_count)
                test_timestamps = [i * 1.0 for i in range(frame_count)]
                
                if processor.is_smart_filter_available():
                    benchmark = await processor.benchmark_smart_filter(test_frames, test_timestamps)
                    benchmark_results[scale_name] = benchmark
                else:
                    # 测试回退性能
                    start_time = time.time()
                    selected_indices, scores = processor._fallback_uniform_sampling(test_frames, test_timestamps)
                    end_time = time.time()
                    
                    benchmark_results[scale_name] = {
                        "success": True,
                        "processing_time": end_time - start_time,
                        "fps": frame_count / (end_time - start_time) if end_time > start_time else 0,
                        "frames": {
                            "input": frame_count,
                            "output": len(selected_indices),
                            "reduction_ratio": (frame_count - len(selected_indices)) / frame_count
                        }
                    }
            
            # 计算性能指标
            avg_fps = np.mean([r.get("fps", 0) for r in benchmark_results.values()])
            avg_reduction = np.mean([r.get("frames", {}).get("reduction_ratio", 0) for r in benchmark_results.values()])
            
            self.performance_metrics = {
                "average_fps": avg_fps,
                "average_reduction_ratio": avg_reduction,
                "benchmark_details": benchmark_results
            }
            
            return self.performance_metrics
            
        except Exception as e:
            logger.error(f"性能基准测试失败: {e}")
            return False

    async def test_error_handling(self) -> Dict[str, Any]:
        """错误处理测试"""
        try:
            processor = ModelProcessor()
            error_tests = {}
            
            # 测试空输入
            try:
                result = await processor._smart_filter_frames([], [])
                error_tests["empty_input"] = len(result[0]) == 0  # 应该返回空结果
            except Exception:
                error_tests["empty_input"] = False
            
            # 测试不匹配的输入
            try:
                frames = self._create_test_frames(5)
                timestamps = [1.0, 2.0]  # 数量不匹配
                result = processor._fallback_uniform_sampling(frames, timestamps)
                error_tests["mismatched_input"] = True  # 应该能处理
            except Exception:
                error_tests["mismatched_input"] = False
            
            # 测试极端配置
            try:
                # 临时修改配置测试边界情况
                original_config = processor.smart_config
                if original_config:
                    test_config = original_config.copy()
                    test_config["filtering"]["min_frames"] = 0
                    test_config["filtering"]["max_frames"] = 1000
                    processor.smart_config = test_config
                
                frames = self._create_test_frames(10)
                timestamps = [i * 1.0 for i in range(10)]
                result = await processor._smart_filter_frames(frames, timestamps)
                error_tests["extreme_config"] = len(result[0]) > 0
                
                # 恢复配置
                processor.smart_config = original_config
                
            except Exception:
                error_tests["extreme_config"] = False
            
            # 测试模型不可用情况
            try:
                original_model = processor.yolo_model
                processor.yolo_model = None
                
                frames = self._create_test_frames(10)
                timestamps = [i * 1.0 for i in range(10)]
                result = await processor._smart_filter_frames(frames, timestamps)
                error_tests["model_unavailable"] = len(result[0]) > 0  # 应该回退成功
                
                processor.yolo_model = original_model
                
            except Exception:
                error_tests["model_unavailable"] = False
            
            return {
                "error_handling_tests": error_tests,
                "all_passed": all(error_tests.values())
            }
            
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            return False

    async def test_configuration_validation(self) -> Dict[str, Any]:
        """配置验证测试"""
        try:
            processor = ModelProcessor()
            
            config_tests = {
                "has_smart_config": processor.smart_config is not None,
                "has_detection_targets": bool(getattr(processor, 'primary_targets', None)),
                "has_priority_weights": bool(processor.smart_config and processor.smart_config.get("priority_weights")),
                "valid_thresholds": True,
                "valid_frame_limits": True
            }
            
            if processor.smart_config:
                filtering = processor.smart_config.get("filtering", {})
                
                # 验证阈值合理性
                conf_threshold = filtering.get("confidence_threshold", 0)
                sim_threshold = filtering.get("similarity_threshold", 0)
                config_tests["valid_thresholds"] = 0 < conf_threshold < 1 and 0 < sim_threshold < 1
                
                # 验证帧数限制
                min_frames = filtering.get("min_frames", 0)
                max_frames = filtering.get("max_frames", 0)
                config_tests["valid_frame_limits"] = 0 < min_frames <= max_frames
            
            return {
                "configuration_tests": config_tests,
                "all_valid": all(config_tests.values())
            }
            
        except Exception as e:
            logger.error(f"配置验证测试失败: {e}")
            return False

    async def test_memory_and_resources(self) -> Dict[str, Any]:
        """内存和资源测试"""
        try:
            import psutil
            import gc
            
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            processor = ModelProcessor()
            
            # 处理大量帧测试内存使用
            large_frame_count = 200
            test_frames = self._create_test_frames(large_frame_count)
            test_timestamps = [i * 0.5 for i in range(large_frame_count)]
            
            # 执行处理
            if processor.is_smart_filter_available():
                result = await processor._smart_filter_frames(test_frames, test_timestamps)
            else:
                result = processor._fallback_uniform_sampling(test_frames, test_timestamps)
            
            # 清理
            del test_frames
            gc.collect()
            
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            return {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase,
                "memory_efficient": memory_increase < 500,  # 增长小于500MB认为合理
                "frames_processed": large_frame_count,
                "frames_selected": len(result[0]) if result else 0
            }
            
        except Exception as e:
            logger.error(f"内存和资源测试失败: {e}")
            return False

    async def test_end_to_end_integration(self) -> Dict[str, Any]:
        """端到端集成测试"""
        try:
            # 创建临时测试视频文件
            test_video_path = await self._create_test_video()
            
            if not test_video_path:
                return {"error": "无法创建测试视频"}
            
            try:
                processor = ModelProcessor()
                
                # 测试完整的视频处理流程
                if processor.is_smart_filter_available():
                    # 测试智能筛选模式
                    stream = await processor.analyze_video_with_smart_filter(test_video_path)
                    integration_success = stream is not None
                else:
                    # 测试回退到网格模式
                    frames, timestamps, frame_paths = await processor.extract_key_frames(test_video_path)
                    integration_success = len(frames) > 0
                
                return {
                    "end_to_end_success": integration_success,
                    "smart_filter_available": processor.is_smart_filter_available(),
                    "test_video_created": True
                }
                
            finally:
                # 清理测试文件
                if os.path.exists(test_video_path):
                    os.remove(test_video_path)
            
        except Exception as e:
            logger.error(f"端到端集成测试失败: {e}")
            return False

    async def test_stress_scenarios(self) -> Dict[str, Any]:
        """压力测试场景"""
        try:
            processor = ModelProcessor()
            stress_results = {}
            
            # 场景1: 快速连续处理
            logger.info("  压力测试: 快速连续处理")
            start_time = time.time()
            for i in range(10):
                frames = self._create_test_frames(20)
                timestamps = [j * 1.0 for j in range(20)]
                
                if processor.is_smart_filter_available():
                    await processor._smart_filter_frames(frames, timestamps)
                else:
                    processor._fallback_uniform_sampling(frames, timestamps)
            
            stress_results["rapid_processing"] = {
                "success": True,
                "total_time": time.time() - start_time,
                "iterations": 10
            }
            
            # 场景2: 极端帧数
            logger.info("  压力测试: 极端帧数处理")
            extreme_frames = self._create_test_frames(500)  # 500帧
            extreme_timestamps = [i * 0.1 for i in range(500)]
            
            start_time = time.time()
            if processor.is_smart_filter_available():
                result = await processor._smart_filter_frames(extreme_frames, extreme_timestamps)
            else:
                result = processor._fallback_uniform_sampling(extreme_frames, extreme_timestamps)
            
            stress_results["extreme_frame_count"] = {
                "success": len(result[0]) > 0,
                "processing_time": time.time() - start_time,
                "input_frames": 500,
                "output_frames": len(result[0])
            }
            
            return {
                "stress_test_results": stress_results,
                "all_stress_tests_passed": all(r.get("success", False) for r in stress_results.values())
            }
            
        except Exception as e:
            logger.error(f"压力测试失败: {e}")
            return False

    def _create_test_frames(self, count: int) -> List[Image.Image]:
        """创建测试帧"""
        frames = []
        for i in range(count):
            # 创建更真实的测试图像
            img_array = np.random.randint(30, 220, (480, 640, 3), dtype=np.uint8)
            
            # 添加一些模拟的"目标"
            if i % 3 == 0:  # 每3帧添加一个"人形"区域
                img_array[150:350, 250:450] = [200, 180, 160]  # 肤色区域
            if i % 5 == 0:  # 每5帧添加一个"包裹"区域
                img_array[300:400, 100:200] = [139, 69, 19]   # 棕色区域
            
            frames.append(Image.fromarray(img_array))
        
        return frames

    async def _create_test_video(self) -> str:
        """创建测试视频文件"""
        try:
            import cv2
            
            # 创建临时视频文件
            temp_dir = tempfile.gettempdir()
            video_path = os.path.join(temp_dir, f"test_video_{int(time.time())}.mp4")
            
            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(video_path, fourcc, 10.0, (640, 480))
            
            # 写入30帧测试视频
            for i in range(30):
                frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
                # 添加帧号标识
                cv2.putText(frame, f"Frame {i}", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                out.write(frame)
            
            out.release()
            return video_path
            
        except Exception as e:
            logger.error(f"创建测试视频失败: {e}")
            return None

    async def generate_test_report(self):
        """生成测试报告"""
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "passed": sum(1 for r in self.test_results.values() if r["status"] == "PASS"),
                "failed": sum(1 for r in self.test_results.values() if r["status"] == "FAIL"),
                "errors": sum(1 for r in self.test_results.values() if r["status"] == "ERROR"),
                "total_duration": sum(r.get("duration", 0) for r in self.test_results.values())
            },
            "test_details": self.test_results,
            "performance_metrics": self.performance_metrics,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 保存报告
        report_path = f"smart_filter_test_report_{int(time.time())}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 测试报告已保存: {report_path}")
        
        # 打印摘要
        summary = report["test_summary"]
        logger.info(f"""
📋 测试摘要:
   总测试数: {summary['total_tests']}
   通过: {summary['passed']} ✅
   失败: {summary['failed']} ❌  
   错误: {summary['errors']} ⚠️
   总耗时: {summary['total_duration']:.2f}s
        """)

async def main():
    """主测试函数"""
    test_suite = SmartFilterTestSuite()
    results = await test_suite.run_all_tests()
    
    # 判断整体测试结果
    passed_tests = sum(1 for r in results.values() if r["status"] == "PASS")
    total_tests = len(results)
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！智能筛选系统已完全就绪！")
        return True
    else:
        logger.warning(f"⚠️  {total_tests - passed_tests} 个测试未通过，请检查相关功能")
        return False

if __name__ == "__main__":
    asyncio.run(main())
