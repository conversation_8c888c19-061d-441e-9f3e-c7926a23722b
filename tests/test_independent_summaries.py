#!/usr/bin/env python3
"""
测试独立总结功能
验证每个matches类型都有自己的total_summary
"""

import asyncio
import json
from llm_summary_service import LLMSummaryService


async def test_independent_summaries():
    """测试每个搜索类型的独立总结功能"""
    
    print("🧪 测试独立总结功能")
    print("=" * 50)
    
    # 初始化LLM摘要服务
    try:
        llm_service = LLMSummaryService()
        print("✅ LLM摘要服务初始化成功")
    except Exception as e:
        print(f"❌ LLM摘要服务初始化失败: {e}")
        return
    
    # 模拟搜索结果数据
    test_data = {
        "title_matches": [
            {
                "score": 0.95,
                "video_id": "12345",
                "title": "快递员送包裹",
                "summary": "顺丰快递员正常上门送货，穿制服，行为规范",
                "event_id": 12345,
                "security_risk": "无风险"
            }
        ],
        "summary_matches": [
            {
                "score": 0.88,
                "video_id": "12346", 
                "title": "外卖配送记录",
                "summary": "外卖员正常上门送餐，晚间配送",
                "event_id": 12346,
                "security_risk": "无风险"
            },
            {
                "score": 0.82,
                "video_id": "12347",
                "title": "陌生人徘徊",
                "summary": "陌生人在门口徘徊，行为可疑，未按门铃",
                "event_id": 12347,
                "security_risk": "中风险"
            }
        ],
        "frame_matches": [
            {
                "score": 0.75,
                "frame_id": "67890",
                "summary": "门口有快递员投递包裹的场景",
                "event_id": 12348,
                "timestamp": 125.5
            },
            {
                "score": 0.68,
                "frame_id": "67891", 
                "summary": "门口有外卖员按门铃的场景",
                "event_id": 12349,
                "timestamp": 58.2
            }
        ]
    }
    
    # 测试每个类型的独立总结
    print("\n🔍 测试各类型独立总结:")
    print("-" * 30)
    
    test_cases = [
        ("title", test_data["title_matches"], "标题匹配"),
        ("summary", test_data["summary_matches"], "内容匹配"),
        ("frame", test_data["frame_matches"], "场景匹配")
    ]
    
    results = {}
    
    for match_type, matches, type_name in test_cases:
        print(f"\n📋 测试 {type_name} 总结:")
        try:
            summary = await llm_service.summarize_by_type(
                matches=matches,
                match_type=match_type,
                query_text="快递员送包裹",
                esn="TEST001"
            )
            results[match_type] = summary
            print(f"✅ {type_name} 总结: {summary}")
        except Exception as e:
            print(f"❌ {type_name} 总结失败: {e}")
            results[match_type] = f"生成总结失败: {e}"
    
    # 验证总结的独立性
    print(f"\n🔍 验证总结独立性:")
    print("-" * 30)
    
    # 检查每个总结是否不同
    unique_summaries = set(results.values())
    if len(unique_summaries) == len(results):
        print("✅ 各类型总结完全独立，内容各不相同")
    else:
        print("⚠️  部分类型总结内容重复")
    
    # 检查总结是否针对性强
    for match_type, summary in results.items():
        type_specific_terms = {
            "title": ["标题", "快递"],
            "summary": ["内容", "配送", "风险"],
            "frame": ["场景", "时间点"]
        }
        
        contains_specific = any(term in summary for term in type_specific_terms.get(match_type, []))
        if contains_specific:
            print(f"✅ {match_type} 总结具有类型针对性")
        else:
            print(f"⚠️  {match_type} 总结缺乏类型针对性")
    
    # 测试空数据的处理
    print(f"\n🔍 测试空数据处理:")
    print("-" * 30)
    
    try:
        empty_summary = await llm_service.summarize_by_type(
            matches=[],
            match_type="title", 
            query_text="快递员送包裹",
            esn="TEST001"
        )
        print(f"✅ 空数据处理: {empty_summary}")
    except Exception as e:
        print(f"❌ 空数据处理失败: {e}")
    
    # 打印完整的测试结果
    print(f"\n📊 完整测试结果:")
    print("=" * 50)
    for match_type, summary in results.items():
        print(f"{match_type.upper()}_SUMMARY: {summary}")
    
    print(f"\n🎉 独立总结功能测试完成!")


async def test_connection():
    """测试LLM服务连接"""
    print("🔗 测试LLM服务连接...")
    try:
        llm_service = LLMSummaryService()
        is_connected = await llm_service.test_connection()
        if is_connected:
            print("✅ LLM服务连接正常")
            return True
        else:
            print("❌ LLM服务连接异常")
            return False
    except Exception as e:
        print(f"❌ LLM服务连接测试失败: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(test_connection())
    print("\n" + "="*50)
    asyncio.run(test_independent_summaries()) 
