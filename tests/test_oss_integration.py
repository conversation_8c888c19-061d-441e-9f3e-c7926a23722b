#!/usr/bin/env python3
"""
OSS集成测试脚本
测试视频分析结果保存、搜索日志记录和训练数据集创建功能
"""

import asyncio
import json
import time
from datetime import datetime
from oss_manager import OSSDataManager

async def test_oss_manager():
    """测试OSS管理器的主要功能"""
    print("🧪 开始测试OSS数据管理功能...")
    
    try:
        # 初始化OSS管理器
        oss_manager = OSSDataManager()
        print("✅ OSS管理器初始化成功")
        
        # 测试1: 保存视频分析结果
        print("\n📹 测试1: 保存视频分析结果")
        test_analysis_result = {
            "title": "快递员送包裹",
            "timestamp": "2024-12-27 14:30:00",
            "num_persons": 1,
            "persons": [],
            "couriers": [
                {
                    "company": "顺丰速运",
                    "uniform": "橙色上衣，黑色裤子",
                    "has_package": True,
                    "description": "男性快递员，约30岁，戴口罩",
                    "suspicious": False,
                    "reason": "",
                    "time_appeared": "14:30:00"
                }
            ],
            "food_deliverers": [],
            "packages": [
                {
                    "type": "快递",
                    "size": "small",
                    "description": "棕色纸箱，贴有顺丰快递单",
                    "time_appeared": "14:30:05"
                }
            ],
            "pets": [],
            "summary": "一名顺丰快递员送达包裹，整个过程正常",
            "security_risk": "无风险",
            "recommendation": "注意及时取走包裹"
        }
        
        test_reasoning = "分析视频帧，识别到一名穿着顺丰制服的快递员，手持包裹，按门铃后放下包裹离开。行为正常，无可疑之处。"
        
        oss_path = oss_manager.save_video_analysis(
            event_id="12345",
            esn="TEST_ESN_001",
            video_url="https://example.com/test_video.m3u8",
            analysis_result=test_analysis_result,
            reasoning_content=test_reasoning,
            processing_info={
                "processing_time": datetime.now().isoformat(),
                "mode": "cloud",
                "duration_seconds": 45.2
            },
            file_info={
                "video_url": "https://example.com/test_video.m3u8",
                "esn": "TEST_ESN_001",
                "analysis_version": "v1.4.0"
            }
        )
        print(f"✅ 视频分析结果保存成功: {oss_path}")
        
        # 测试2: 保存搜索日志
        print("\n🔍 测试2: 保存搜索日志")
        test_request_data = {
            "esn": "TEST_ESN_001",
            "query_text": "快递员送包裹",
            "limit": 5,
            "drop_ratio": 0.2,
            "search_types": ["title", "summary"]
        }
        
        test_response_data = {
            "total_matches": 3,
            "title_matches_count": 2,
            "summary_matches_count": 1,
            "frame_matches_count": 0,
            "results_summary": {
                "title_results": [
                    {"video_id": 12345, "score": 0.95, "event_id": "12345"},
                    {"video_id": 12346, "score": 0.88, "event_id": "12346"}
                ],
                "summary_results": [
                    {"video_id": 12347, "score": 0.82, "event_id": "12347"}
                ],
                "frame_results": []
            }
        }
        
        test_performance_data = {
            "search_duration_seconds": 0.256,
            "search_types_count": 2,
            "parallel_tasks": 2,
            "timestamp": time.time()
        }
        
        search_log_path = oss_manager.save_search_log(
            request_data=test_request_data,
            response_data=test_response_data,
            performance_data=test_performance_data,
            session_id="test_session_001"
        )
        print(f"✅ 搜索日志保存成功: {search_log_path}")
        
        # 测试3: 创建训练数据集
        print("\n🎯 测试3: 创建训练数据集")
        test_training_data = [
            {
                "query": "快递员送包裹",
                "esn": "TEST_ESN_001",
                "response": {
                    "title_matches": [{"video_id": 12345, "score": 0.95, "event_id": "12345"}],
                    "summary_matches": [{"video_id": 12346, "score": 0.88, "event_id": "12346"}]
                },
                "feedback": "positive",
                "timestamp": datetime.now().isoformat()
            },
            {
                "query": "外卖员送餐",
                "esn": "TEST_ESN_002",
                "response": {
                    "title_matches": [{"video_id": 12348, "score": 0.92, "event_id": "12348"}],
                    "summary_matches": []
                },
                "feedback": "positive",
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        test_metadata = {
            "description": "测试训练数据集，包含快递和外卖场景",
            "data_source": "video_understanding_service",
            "quality_score": 0.9,
            "tags": ["delivery", "courier", "food_delivery"]
        }
        
        dataset_path = oss_manager.create_training_dataset(
            dataset_name="delivery_scenarios",
            version="v1.0.0",
            data=test_training_data,
            metadata=test_metadata
        )
        print(f"✅ 训练数据集创建成功: {dataset_path}")
        
        # 测试4: 获取存储统计信息
        print("\n📊 测试4: 获取存储统计信息")
        stats = oss_manager.get_storage_stats()
        print(f"✅ 存储统计信息:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        # 测试5: 下载和验证数据
        print("\n⬇️ 测试5: 下载和验证保存的数据")
        try:
            # 尝试下载刚才保存的视频分析结果
            downloaded_data = oss_manager.download_json(f"{oss_path}/analysis.json")
            print(f"✅ 成功下载视频分析数据，event_id: {downloaded_data.get('event_id')}")
            
            # 验证数据完整性
            assert downloaded_data['event_id'] == "12345"
            assert downloaded_data['esn'] == "TEST_ESN_001"
            assert downloaded_data['analysis_result']['title'] == "快递员送包裹"
            print("✅ 数据完整性验证通过")
            
        except Exception as e:
            print(f"⚠️ 下载验证失败（这可能是正常的，取决于OSS配置）: {e}")
        
        print("\n🎉 所有OSS功能测试完成！")
        
    except Exception as e:
        print(f"❌ OSS测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_oss_directory_structure():
    """测试OSS目录结构"""
    print("\n📁 测试OSS目录结构...")
    
    try:
        oss_manager = OSSDataManager()
        
        # 列出不同目录的文件
        directories = [
            "video-analysis/",
            "search-logs/", 
            "training-data/",
            "backups/"
        ]
        
        for directory in directories:
            try:
                files = oss_manager.list_files(directory, max_keys=10)
                print(f"📂 {directory}: {len(files)} 个文件")
                if files:
                    print(f"   最新文件: {files[-1]}")
            except Exception as e:
                print(f"📂 {directory}: 目录为空或无法访问 ({e})")
                
    except Exception as e:
        print(f"❌ 目录结构测试失败: {str(e)}")

async def main():
    """主测试函数"""
    print("🚀 开始OSS集成测试")
    print("=" * 60)
    
    await test_oss_manager()
    await test_oss_directory_structure()
    
    print("=" * 60)
    print("✨ OSS集成测试完成")

if __name__ == "__main__":
    asyncio.run(main())
