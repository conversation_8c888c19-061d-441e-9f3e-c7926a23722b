#!/usr/bin/env python3
"""
LLM摘要功能测试脚本
测试对搜索结果进行智能总结的功能
"""

import asyncio
import json
from llm_summary_service import LLMSummaryService

async def test_llm_summary_service():
    """测试LLM摘要服务"""
    print("🧪 开始测试LLM摘要服务...")
    
    try:
        # 初始化LLM摘要服务
        llm_service = LLMSummaryService()
        print("✅ LLM摘要服务初始化成功")
        
        # 测试连接
        print("\n🔗 测试LLM服务连接...")
        connection_ok = await llm_service.test_connection()
        if connection_ok:
            print("✅ LLM服务连接正常")
        else:
            print("❌ LLM服务连接失败")
            return
        
        # 构造测试搜索结果
        test_search_results = {
            "title_matches": [
                {
                    "video_id": 12345,
                    "score": 0.95,
                    "esn": "TEST_ESN_001",
                    "event_id": 12345,
                    "url": "https://example.com/video1.m3u8",
                    "summary": "一名顺丰快递员在上午10点23分送达一个小型包裹，按门铃后将包裹放在门口便离开了。整个过程正常，无可疑行为。"
                },
                {
                    "video_id": 12346,
                    "score": 0.88,
                    "esn": "TEST_ESN_001", 
                    "event_id": 12346,
                    "url": "https://example.com/video2.m3u8",
                    "summary": "一名申通快递员在下午3点15分送达包裹，戴口罩，穿制服，行为正常，安全风险为无风险。"
                }
            ],
            "summary_matches": [
                {
                    "video_id": 12347,
                    "score": 0.82,
                    "esn": "TEST_ESN_001",
                    "event_id": 12347,
                    "url": "https://example.com/video3.m3u8",
                    "summary": "一名美团外卖员在晚上18点45分送达一份外卖，按门铃后将外卖放在门口便离开了。整个过程正常，无可疑行为。"
                }
            ],
            "frame_matches": [
                {
                    "frame_id": 12550,
                    "score": 0.75,
                    "esn": "TEST_ESN_001",
                    "event_id": 12348,
                    "url": "https://example.com/video4.m3u8",
                    "timestamp": 125.5,
                    "summary": ""  # 帧搜索结果没有summary
                }
            ],
            "total_matches": 4
        }
        
        # 测试正常的搜索结果总结
        print("\n📝 测试1: 正常搜索结果总结...")
        query_text = "快递员送包裹"
        esn = "TEST_ESN_001"
        
        summary = await llm_service.summarize_search_results(
            search_results=test_search_results,
            query_text=query_text,
            esn=esn
        )
        
        print(f"✅ 生成总结成功:")
        print(f"查询: {query_text}")
        print(f"设备: {esn}")
        print(f"总结: {summary}")
        
        # 测试空搜索结果
        print("\n📝 测试2: 空搜索结果...")
        empty_results = {
            "title_matches": [],
            "summary_matches": [],
            "frame_matches": [],
            "total_matches": 0
        }
        
        empty_summary = await llm_service.summarize_search_results(
            search_results=empty_results,
            query_text="不存在的内容",
            esn=esn
        )
        print(f"✅ 空结果总结: {empty_summary}")
        
        # 测试可疑行为的搜索结果
        print("\n📝 测试3: 包含可疑行为的搜索结果...")
        suspicious_results = {
            "title_matches": [
                {
                    "video_id": 12349,
                    "score": 0.92,
                    "esn": "TEST_ESN_001",
                    "event_id": 12349,
                    "url": "https://example.com/video5.m3u8",
                    "summary": "晚上23点15分，一名身穿黑色连帽衫的男子在门口徘徊约5分钟，戴着帽子和口罩遮挡面部，手持可疑工具试图操作门锁，随后发现摄像头后迅速离开。安全风险为高风险。"
                },
                {
                    "video_id": 12350,
                    "score": 0.85,
                    "esn": "TEST_ESN_001",
                    "event_id": 12350,
                    "url": "https://example.com/video6.m3u8",
                    "summary": "凌晨2点30分，一名不明身份人员在门前逗留，试图查看门锁情况，行为可疑，建议加强警惕。安全风险为中风险。"
                }
            ],
            "summary_matches": [],
            "frame_matches": [],
            "total_matches": 2
        }
        
        suspicious_summary = await llm_service.summarize_search_results(
            search_results=suspicious_results,
            query_text="可疑人员",
            esn=esn
        )
        print(f"✅ 可疑行为总结: {suspicious_summary}")
        
        # 测试大量搜索结果（验证token限制处理）
        print("\n📝 测试4: 大量搜索结果...")
        large_results = {
            "title_matches": [],
            "summary_matches": [],
            "frame_matches": [],
            "total_matches": 0
        }
        
        # 生成15个结果（超过max_summaries=10的限制）
        for i in range(15):
            large_results["title_matches"].append({
                "video_id": 20000 + i,
                "score": 0.9 - i * 0.05,
                "esn": "TEST_ESN_001",
                "event_id": 20000 + i,
                "url": f"https://example.com/video{20000+i}.m3u8",
                "summary": f"第{i+1}次快递送达记录，快递员正常操作，无异常行为。安全风险为无风险。"
            })
        
        large_results["total_matches"] = 15
        
        large_summary = await llm_service.summarize_search_results(
            search_results=large_results,
            query_text="快递记录",
            esn=esn
        )
        print(f"✅ 大量结果总结: {large_summary}")
        
        print("\n🎉 所有LLM摘要功能测试完成！")
        
    except Exception as e:
        print(f"❌ LLM摘要测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

async def test_fallback_summary():
    """测试降级总结功能"""
    print("\n🛡️ 测试降级总结功能...")
    
    try:
        llm_service = LLMSummaryService()
        
        # 模拟LLM调用失败的情况
        test_results = {
            "title_matches": [
                {"summary": "正常快递送达，无风险"},
                {"summary": "外卖配送，低风险"}
            ],
            "summary_matches": [
                {"summary": "可疑人员徘徊，高风险"}
            ],
            "frame_matches": [],
            "total_matches": 3
        }
        
        fallback_summary = llm_service._generate_fallback_summary(test_results, "测试查询")
        print(f"✅ 降级总结: {fallback_summary}")
        
        # 验证降级总结的用户友好性
        expected_keywords = ["事件", "安全风险"]
        unwanted_keywords = ["记录", "匹配", "搜索结果", "数据"]
        
        has_expected = any(keyword in fallback_summary for keyword in expected_keywords)
        has_unwanted = any(keyword in fallback_summary for keyword in unwanted_keywords)
        
        if has_expected and not has_unwanted:
            print("✅ 降级总结语言风格符合用户友好要求")
        else:
            print("⚠️ 降级总结可能包含技术术语，需要优化")
        
    except Exception as e:
        print(f"❌ 降级总结测试失败: {str(e)}")

async def main():
    """主测试函数"""
    print("🚀 开始LLM摘要功能测试")
    print("=" * 60)
    
    await test_llm_summary_service()
    await test_fallback_summary()
    
    print("=" * 60)
    print("✨ LLM摘要功能测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 
