import json
import logging
import aiohttp
import asyncio
from typing import Dict, Any, Optional
import yaml
import os


class CallbackService:
    def __init__(self):
        """
        初始化回调服务
        """
        self.logger = logging.getLogger(__name__)
        self.load_config()
        
    def load_config(self, path=None):
        """
        加载配置文件
        """
        if not path:
            path = f"config_{os.environ.get('DEPLOY_MODE', 'dev')}.yaml"
        
        self.logger.info(f"CallbackService loading config from: {path}")
        
        with open(path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        
        callback_config = config.get("callback_service", {})
        
        self.enabled = callback_config.get("enabled", False)
        self.url = callback_config.get("url", "")
        self.access_token = callback_config.get("access_token", "")
        self.timeout = callback_config.get("timeout", 30)
        self.max_retries = callback_config.get("max_retries", 3)
        self.retry_delay = callback_config.get("retry_delay", 2)
        self.async_mode = callback_config.get("async_mode", True)
        
        self.logger.info(f"CallbackService config: enabled={self.enabled}, url={self.url}, async_mode={self.async_mode}")
        
    async def send_callback(self, video_analysis_result: Dict[str, Any]) -> bool:
        """
        向业务方发送回调
        
        Args:
            video_analysis_result: 视频分析结果
            
        Returns:
            bool: 发送是否成功
        """
        if not self.enabled:
            self.logger.info("Callback service is disabled, skipping callback")
            return True
            
        if not self.url or not self.access_token:
            self.logger.error("Callback URL or access_token not configured")
            return False
            
        # 准备回调数据
        callback_data = self._prepare_callback_data(video_analysis_result)
        
        if self.async_mode:
            # 异步模式：启动后台任务，不等待结果
            asyncio.create_task(self._send_callback_async(callback_data))
            return True
        else:
            # 同步模式：等待回调完成
            return await self._send_callback_async(callback_data)
    
    def _prepare_callback_data(self, video_analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备回调数据，转换为业务方需要的格式
        
        Args:
            video_analysis_result: 视频分析结果
            
        Returns:
            Dict: 格式化后的回调数据
        """
        try:
            # 解析JSON字符串字段
            persons = json.loads(video_analysis_result.get("persons", "[]")) if video_analysis_result.get("persons") else []
            couriers = json.loads(video_analysis_result.get("couriers", "[]")) if video_analysis_result.get("couriers") else []
            food_deliverers = json.loads(video_analysis_result.get("food_deliverers", "[]")) if video_analysis_result.get("food_deliverers") else []
            packages = json.loads(video_analysis_result.get("packages", "[]")) if video_analysis_result.get("packages") else []
            pets = json.loads(video_analysis_result.get("pets", "[]")) if video_analysis_result.get("pets") else []
            
            # 构建回调数据
            callback_data = {
                "accessToken": self.access_token,
                "esn": video_analysis_result.get("esn", ""),
                "url": video_analysis_result.get("video_url", ""),
                "EventId": str(video_analysis_result.get("event_id", "")),
                "title": video_analysis_result.get("title", ""),
                "timestamp": video_analysis_result.get("timestamp", ""),
                "num_persons": video_analysis_result.get("num_persons", 0),
                "persons": persons,
                "couriers": couriers,
                "food_deliverers": food_deliverers,
                "packages": packages,
                "pets": pets,
                "summary": video_analysis_result.get("summary", ""),
                "security_risk": video_analysis_result.get("security_risk", ""),
                "recommendation": video_analysis_result.get("recommendation", "")
            }
            
            self.logger.debug(f"Prepared callback data: {json.dumps(callback_data, ensure_ascii=False, indent=2)}")
            return callback_data
            
        except Exception as e:
            self.logger.error(f"Error preparing callback data: {str(e)}")
            # 返回基础数据，避免回调完全失败
            return {
                "accessToken": self.access_token,
                "esn": video_analysis_result.get("esn", ""),
                "url": video_analysis_result.get("video_url", ""),
                "EventId": str(video_analysis_result.get("event_id", "")),
                "title": video_analysis_result.get("title", ""),
                "summary": video_analysis_result.get("summary", ""),
                "security_risk": video_analysis_result.get("security_risk", ""),
                "recommendation": video_analysis_result.get("recommendation", "")
            }
    
    async def _send_callback_async(self, callback_data: Dict[str, Any]) -> bool:
        """
        异步发送回调请求
        
        Args:
            callback_data: 回调数据
            
        Returns:
            bool: 发送是否成功
        """
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"Sending callback attempt {attempt + 1}/{self.max_retries} to {self.url}")
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        self.url,
                        json=callback_data,
                        headers={
                            "Content-Type": "application/json",
                            "User-Agent": "VideoUnderstanding/1.5.0"
                        },
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        
                        if response.status == 200:
                            response_text = await response.text()
                            self.logger.info(f"Callback sent successfully: {response.status}, response: {response_text}")
                            return True
                        else:
                            response_text = await response.text()
                            self.logger.warning(f"Callback failed with status {response.status}: {response_text}")
                            
            except asyncio.TimeoutError:
                self.logger.error(f"Callback timeout after {self.timeout} seconds (attempt {attempt + 1})")
            except aiohttp.ClientError as e:
                self.logger.error(f"Callback client error (attempt {attempt + 1}): {str(e)}")
            except Exception as e:
                self.logger.error(f"Unexpected callback error (attempt {attempt + 1}): {str(e)}")
            
            # 如果不是最后一次尝试，等待重试
            if attempt < self.max_retries - 1:
                wait_time = self.retry_delay * (2 ** attempt)  # 指数退避
                self.logger.info(f"Waiting {wait_time} seconds before retry...")
                await asyncio.sleep(wait_time)
        
        self.logger.error(f"Callback failed after {self.max_retries} attempts")
        return False
    
    async def test_connection(self) -> bool:
        """
        测试回调连接
        
        Returns:
            bool: 连接是否正常
        """
        if not self.enabled:
            self.logger.info("Callback service is disabled")
            return False
            
        if not self.url or not self.access_token:
            self.logger.error("Callback URL or access_token not configured")
            return False
        
        test_data = {
            "accessToken": self.access_token,
            "esn": "TEST_ESN",
            "url": "https://test.com/test.m3u8",
            "EventId": "test_event_id",
            "title": "连接测试",
            "summary": "这是一个连接测试",
            "security_risk": "无风险",
            "recommendation": "测试建议"
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.url,
                    json=test_data,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": "VideoUnderstanding/1.5.0"
                    },
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        self.logger.info("Callback connection test successful")
                        return True
                    else:
                        response_text = await response.text()
                        self.logger.warning(f"Callback connection test failed: {response.status}, {response_text}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Callback connection test error: {str(e)}")
            return False 
