# This file was generated using the `serve build` command on Ray v2.40.0.

proxy_location: EveryNode

# HTTP 配置
http_options:
  host: 0.0.0.0 # 监听所有网络接口

  port: 8009 # 监听8009端口

# gRPC 配置
grpc_options:

  port: 9001 # 修改为9000端口，避免冲突

  grpc_servicer_functions: []

logging_config:

  encoding: JSON

  log_level: INFO

  logs_dir: "/var/log/ray/serve"

  enable_access_log: true

applications:


# 合并视频应用
- name: VideoUnstanding # 注意不能有下划线的名字会有错误

  route_prefix: /kaadas_ai/video_unstanding

  import_path: server:app

  runtime_env:
    #pip: ["concat_video_ray_service"]  # 安装包名
    env_vars: 
      # CUDA_VISIBLE_DEVICES: "0"
      # FFMPEG_BINARY: "/usr/local/ffmpeg/bin/ffmpeg"
      # PATH: "/usr/local/ffmpeg/bin:$PATH"
      DEPLOY_MODE: "prod"

  deployments:

  - name: VideoUnstandingService # 注意不能有下划线的名字会有错误
    num_replicas: auto
    ray_actor_options:
      num_cpus: 2  # 减少到4个CPU核心
      num_gpus: 0   # 分配1个GPU
      memory: 2147483648  # 减少到2GB RAM(2*1024*1024*1024)
      object_store_memory: 3758096384  # 2GB object store memory (4096*1024*1024)
      runtime_env: {}  # 运行时环境配置
      max_restarts: 3  # 最大重启次数
      max_task_retries: 3  # 最大任务重试次数
      # resources: {"custom_resource": 1}  # 自定义资源需求
      # accelerator_type: "V100"  # GPU加速器类型
      # placement_group_bundles:  # 资源分组配置
      #   - CPU: 5
      #     GPU: 1
      #     memory: 536870912  # 512MB
      #   - CPU: 5
      #     memory: 536870912  # 512MB
    max_ongoing_requests: 64
    max_queued_requests: 100 # 每个副本的最大排队请求数限制(队列长度)
    # https://docs.ray.io/en/latest/serve/advanced-guides/advanced-autoscaling.html#serve-advanced-autoscaling
    autoscaling_config:
      # 1. 负载目标配置
      target_ongoing_requests: 64          # 每个副本的目标并发请求数
      max_ongoing_requests: 64             # 每个副本的最大并发请求数限制
      max_queued_requests: 100 # 每个副本的最大排队请求数限制
      
      # 2. 副本数量限制
      min_replicas: 1                     # 最小副本数
      max_replicas: 1                     # 最大副本数
      initial_replicas: 1                 # 初始副本数
      
      # 3. 时间延迟配置
      upscale_delay_s: 30                 # 扩容延迟(秒)
      downscale_delay_s: 600              # 缩容延迟(秒)
      
      # 4. 扩缩容行为调节
      upscaling_factor: 1.0               # 扩容因子
      downscaling_factor: 1.0             # 缩容因子
      
      # 5. 指标收集配置
      metrics_interval_s: 10              # 指标收集间隔(秒)
      look_back_period_s: 30              # 指标回溯时间窗口(秒)
      
      # 6. 决策阈值配置
      upscale_decision_threshold_s: 60    # 扩容决策阈值时间(秒)
      downscale_decision_threshold_s: 60  # 缩容决策阈值时间(秒)
      
      # 7. 错误处理配置
      error_window_s: 60                  # 错误统计窗口(秒)
      error_rate_threshold: 0.99          # 错误率阈值
      
      # 8. 高级配置
      reset_window_s: 600                 # 重置窗口时间(秒)
      smoothing_factor: 0.3               # 平滑因子
      uncertainty_margin: 0.1             # 不确定性边际
    
    #max_queued_requests: 100 # 增加队列容量，允许更多并发请求
    #user_config: {} # 用户自定义配置
    health_check_period_s: 1800 # 健康检查周期
    health_check_timeout_s: 1800 # 健康检查超时时间
    graceful_shutdown_wait_loop_s: 2 # 优雅关闭等待循环时间
    graceful_shutdown_timeout_s: 20 # 优雅关闭超时时间
    logging_config: {} # 日志配置

    


