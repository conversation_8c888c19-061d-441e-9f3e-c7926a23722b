FROM nvidia/cuda:12.4.1-runtime-ubuntu22.04 AS builder

# 添加代理参数支持
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

# 设置代理环境变量
ENV HTTP_PROXY=${HTTP_PROXY} \
    HTTPS_PROXY=${HTTPS_PROXY} \
    NO_PROXY=${NO_PROXY} \
    http_proxy=${http_proxy} \
    https_proxy=${https_proxy} \
    no_proxy=${no_proxy}

# 设置工作目录
WORKDIR /workspace

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    CUDA_HOME=/usr/local/cuda \
    PATH=/usr/local/cuda/bin:${PATH} \
    LD_LIBRARY_PATH=/usr/local/cuda/lib64:${LD_LIBRARY_PATH} \
    NVIDIA_VISIBLE_DEVICES=all \
    NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 预先设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 配置apt代理和更换国内源
RUN echo 'Acquire::http::Proxy "'${HTTP_PROXY}'";' > /etc/apt/apt.conf.d/proxy || true && \
    echo 'Acquire::https::Proxy "'${HTTPS_PROXY}'";' >> /etc/apt/apt.conf.d/proxy || true && \
    # 备份原始sources.list
    cp /etc/apt/sources.list /etc/apt/sources.list.backup && \
    # 使用阿里云镜像源
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list

# 安装基础依赖和Python 3.12
RUN apt-get update && apt-get install -y \
    software-properties-common \
    && add-apt-repository ppa:deadsnakes/ppa -y && \
    apt-get update && apt-get install -y \
    wget \
    curl \
    python3.12 \
    python3.12-dev \
    python3.12-venv \
    ca-certificates \
    git \
    # 编译工具（用于构建Python包如crcmod）
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && python3.12 -m ensurepip \
    && python3.12 -m pip install --no-cache-dir --upgrade pip setuptools \
    && ln -sf /usr/bin/python3.12 /usr/bin/python3 \
    && ln -sf /usr/bin/python3.12 /usr/bin/python


# 安装uv并配置环境变量
RUN python3 -m pip install uv -i https://mirrors.aliyun.com/pypi/simple/

ENV PATH="/root/.local/bin:${PATH}" \
    UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/ \
    UV_EXTRA_INDEX_URL=https://mirrors.cloud.tencent.com/pypi/simple/,https://pypi.douban.com/simple/ \
    UV_TRUSTED_HOST=mirrors.cloud.tencent.com,pypi.douban.com \
    UV_NO_PROGRESS=true \
    UV_SYSTEM_PYTHON=1 \
    UV_LINK_MODE=copy \
    UV_COMPILE_BYTECODE=1 \
    UV_CACHE_DIR=/tmp/uv-cache

# 复制uv的pyproject.toml和uv.lock依赖文件
COPY pyproject.toml uv.lock download.py /workspace/

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 第二阶段：最终运行时镜像
FROM nvidia/cuda:12.4.1-runtime-ubuntu22.04 AS final

# 继承代理设置
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG NO_PROXY
ARG http_proxy
ARG https_proxy
ARG no_proxy

ENV HTTP_PROXY=${HTTP_PROXY} \
    HTTPS_PROXY=${HTTPS_PROXY} \
    NO_PROXY=${NO_PROXY} \
    http_proxy=${http_proxy} \
    https_proxy=${https_proxy} \
    no_proxy=${no_proxy}

# 设置工作目录
WORKDIR /workspace

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    CUDA_HOME=/usr/local/cuda \
    PATH=/usr/local/cuda/bin:${PATH} \
    LD_LIBRARY_PATH=/usr/local/cuda/lib64:${LD_LIBRARY_PATH} \
    NVIDIA_VISIBLE_DEVICES=all \
    NVIDIA_DRIVER_CAPABILITIES=compute,utility \
    RAY_DEDUP_LOGS=0 \
    PYTHONPATH=/workspace

# 预先设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 配置apt源并安装运行时依赖
RUN echo 'Acquire::http::Proxy "'${HTTP_PROXY}'";' > /etc/apt/apt.conf.d/proxy || true && \
    echo 'Acquire::https::Proxy "'${HTTPS_PROXY}'";' >> /etc/apt/apt.conf.d/proxy || true && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    apt-get update && apt-get install -y \
    software-properties-common \
    && add-apt-repository ppa:deadsnakes/ppa -y && \
    apt-get update && apt-get install -y \
    python3.12 \
    python3.12-dev \
    python3.12-venv \
    vim \
    tmux \
    graphviz \
    ffmpeg \
    fonts-wqy-microhei \
    libsm6 \
    libxext6 \
    libgl1 \
    # FFmpeg编码相关依赖
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libavdevice-dev \
    libavfilter-dev \
    libswresample-dev \
    x264 \
    libx264-dev \
    x265 \
    libx265-dev \
    libmp3lame-dev \
    libopus-dev \
    libvpx-dev \
    yasm \
    nasm \
    pkg-config \
    git \
    # OpenCV依赖
    libglib2.0-0 \
    libxrender1 \
    && rm -rf /var/lib/apt/lists/* \
    && python3.12 -m ensurepip \
    && python3.12 -m pip install --no-cache-dir --upgrade pip setuptools \
    && ln -sf /usr/bin/python3.12 /usr/bin/python3 \
    && ln -sf /usr/bin/python3.12 /usr/bin/python

# 安装uv工具
RUN python3 -m pip install uv -i https://mirrors.aliyun.com/pypi/simple/

# 创建日志目录
RUN mkdir -p /var/log/video_unstanding_service \
    /var/log/ray/serve

# 从builder阶段复制项目文件和已安装的虚拟环境
COPY --from=builder /workspace .

# 设置uv环境变量，确保Ray使用现有的虚拟环境
ENV PATH="/root/.local/bin:/workspace/.venv/bin:${PATH}" \
    UV_SYSTEM_PYTHON=1 \
    VIRTUAL_ENV=/workspace/.venv \
    UV_PROJECT_ENVIRONMENT=/workspace/.venv

# 激活虚拟环境并确保Python路径正确
RUN echo 'source /workspace/.venv/bin/activate' >> /root/.bashrc

# 预下载CLIP模型，避免运行时下载338M文件
RUN uv run download.py

# 复制项目文件
COPY . .

# 暴露端口（可根据需要取消注释）
#EXPOSE 8265 52365 52366 8000 8008 9000 10001 6379-6381 8076 6380 62365 8080

# 设置启动命令 - 使用已安装的虚拟环境
#CMD ["/bin/bash", "-c", "source /workspace/.venv/bin/activate && uv run serve run server_dev.yaml"] 
