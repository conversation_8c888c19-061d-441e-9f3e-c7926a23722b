import asyncio
import os
from typing import Literal
from dotenv import load_dotenv
from fastmcp import FastMCP
import aiohttp
from starlette.requests import Request
from starlette.responses import PlainTextResponse

if os.getenv("DEPLOY_MODE") == "dev":
    load_dotenv(".env_dev",override=True)
else:
    load_dotenv(".env_prod",override=True)

video_mcp = FastMCP(name="video_understanding_mcp")

# We assume the video processing service is running and exposing an HTTP API.
VIDEO_SERVICE_BASE_URL = os.getenv("VIDEO_SERVICE_BASE_URL", "http://localhost:8009/kaadas_ai/video_unstanding")

async def _make_request(method: str, endpoint: str, **kwargs):
    """Helper function to make HTTP requests to the video service."""
    async with aiohttp.ClientSession() as session:
        url = f"{VIDEO_SERVICE_BASE_URL}{endpoint}"
        try:
            async with session.request(method, url, **kwargs) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            # Propagate HTTP errors as exceptions that the MCP can handle.
            raise Exception(f"Error calling video service at {url}: {e}") from e

# 添加健康检查端点
@video_mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request) -> PlainTextResponse:
    """健康检查端点"""
    return PlainTextResponse("OK")

# @video_mcp.tool()
# async def analyze_video(esn: str, event_id: str, url: str) -> dict:
#     """
#     通过调用外部服务来分析视频。
#     这将向视频处理服务发送一个任务。
#     Args:
#         esn: 设备序列号
#         event_id: 事件ID (将作为 task_id)
#         url: 视频文件路径 (将作为 file_url)
#     """
#     payload = {"esn": esn, "task_id": event_id, "file_url": url}
#     return await _make_request("POST", "/produce", json=payload)


@video_mcp.tool()
async def search_videos(esn: str, query_text: str, limit: int = 5) -> dict:
    """
    基于内容的语义搜索视频。
    
    Args:
        esn: 设备序列号
        query_text: 要搜索的文本查询
        limit: 返回结果的最大数量
    Returns:
        dict: 搜索结果
    """
    payload = {"esn": esn, "query_text": query_text, "limit": limit}
    return await _make_request("POST", "/search", json=payload)


if __name__ == "__main__":
    # Note: To run this, you need to have 'aiohttp' installed.
    # You can install it using: pip install aiohttp
    asyncio.run(video_mcp.run_async(transport="streamable-http", host="0.0.0.0", port=8012))
