[project]
name = "video-unstanding"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp",
    "python-dotenv",
    "ffmpeg-python>=0.2.0",
    "numpy",
    "openai>=1.86.0",
    "pillow",
    "pyyaml",
    "pymilvus>=2.5.11",
    "ray[serve]>=2.47.1",
    "fastapi>=0.115.12",
    "aiokafka>=0.12.0",
    "redis>=6.2.0",
    "oss2>=2.19.1",
    "dashscope>=1.23.4",
    "opencv-python>=*********",
    "json-repair>=0.47.5",
    "fastmcp>=2.10.5",
    "ultralytics>=8.3.167",
    "clip @ git+https://github.com/ultralytics/CLIP.git",
    "alibabacloud-oss-v2>=1.1.2",
]

# 配置阿里云 PyPI 镜像源
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
